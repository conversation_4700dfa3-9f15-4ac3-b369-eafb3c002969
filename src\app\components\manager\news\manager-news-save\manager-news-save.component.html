@if(crudForm){
<form [formGroup]="crudForm" class="h-full" (submit)="onSubmitCrudForm()">
    <div class="relative overflow-hidden pb-12 shadow-lg bgwhite dark:bg-gray-900 rounded-lg h-full min-w-full">
        <div class="container-table h-full relative overflow-auto   sm:rounded-lg">
            <div class="h-full">
                <div class="relative p-4 md:p-5 h-auto bg-white  dark:bg-gray-700">
                    <div class="flex items-center justify-start gap-4 pb-2 border-b rounded-t dark:border-gray-600">
                        <a (click)="goBack()"
                            class="inline-flex items-center hover:bg-gray-200 cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-arrow-left h-4 w-4">
                                <path d="m12 19-7-7 7-7"></path>
                                <path d="M19 12H5"></path>
                            </svg><span class="sr-only">Quay lại</span>
                        </a>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white uppercase">
                            {{titlePage}}
                        </h3>
                    </div>
                    <div class="grid md:grid-cols-2 gap-x-16 gap-y-8 mb-4  p-4">
                        <div class="space-y-2">
                            <input type="text" formControlName="Id" type="hidden">

                            <div>
                                <label for="countries"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Danh
                                    mục</label>
                                <select id="CategoryId" formControlName="CategoryId"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option selected value=''>Chọn danh mục</option>
                                    <option *ngFor="let category of categories" [value]="category.id">
                                        {{category.name}}
                                    </option>
                                </select>
                            </div>

                            <div>
                                <label for="name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                    Tiêu đề
                                    <span class="text-gray-400 font-light">({{fm.Name?.value.length ||
                                        0}}/255)</span>
                                </label>
                                <input type="text" formControlName="Name" [maxlength]="255"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    placeholder="Enter full name" required="">
                                @if(fm.Name.errors && (fm.Name.dirty || fm.Name.touched || formSubmitted)){
                                @if(fm.Name.errors.required){
                                <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400"
                                    role="alert">
                                    <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                                    </svg>
                                    <span class="sr-only">Info</span>
                                    <div>
                                        <span class="font-medium">Required!</span> Please enter name news
                                    </div>
                                </div>
                                }@else if(fm.Name.errors.maxlength) {
                                <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400"
                                    role="alert">
                                    <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                                    </svg>
                                    <span class="sr-only">Info</span>
                                    <div>
                                        <span class="font-medium">Max length!</span> Maximum length is 255 characters.
                                    </div>
                                </div>
                                }
                                }
                            </div>

                            <div>
                                <label for="message"
                                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Tóm tắt
                                    <span class="text-gray-400 font-light">({{fm.Description?.value.length ||
                                        0}}/2000)</span></label>
                                @if(fm.Description.errors && (fm.Description.dirty ||
                                fm.Description.touched || formSubmitted)){
                                @if(fm.Description.errors.required){
                                <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400"
                                    role="alert">
                                    <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                                    </svg>
                                    <span class="sr-only">Info</span>
                                    <div>
                                        <span class="font-medium">Required!</span> Please enter description news
                                    </div>
                                </div>
                                }@else if (fm.Description.errors.maxlength) {
                                <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400"
                                    role="alert">
                                    <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                                    </svg>
                                    <span class="sr-only">Info</span>
                                    <div>
                                        <span class="font-medium">Max length!</span> Maximum length is 2000 characters.
                                    </div>
                                </div>
                                }
                                }
                                <textarea rows="5" formControlName="Description"
                                    class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    placeholder="Write description here..."></textarea>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <div>
                                <label for="name" class="block my-2 text-sm font-medium text-gray-900 dark:text-white">
                                    Ảnh minh họa
                                </label>
                                <div class="group-image-selected">
                                    <div class="col-12 d-flex justify-content-start">
                                        <div id="img-selected" #img_selected>
                                            @if (newsDetail?.imagePath != null)
                                            {
                                            <div class="img-selected-item">
                                                <img src="{{newsDetail.imagePath}}" alt="">
                                                <i (click)="resetImage();">
                                                    <svg class="w-4 h-4 text-white" aria-hidden="true"
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        fill="none" viewBox="0 0 24 24">
                                                        <path stroke="currentColor" stroke-linecap="round"
                                                            stroke-linejoin="round" stroke-width="2"
                                                            d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z" />
                                                    </svg>
                                                </i>
                                            </div>
                                            }
                                            <label
                                                [ngClass]="{'hidden':images.length >= maxSelectedImg || newsDetail?.imagePath != null}"
                                                class="custom-file-upload p-1 w-20 h-20 bg-primary-600/20 cursor-pointer rounded border hover:border-dashed border-primary-600 flex flex-col justify-center items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="fill-primary-600 w-6 h-6"
                                                    viewBox="0 0 24 24">
                                                    <path
                                                        d="M4 5h13v7h2V5c0-1.103-.897-2-2-2H4c-1.103 0-2 .897-2 2v12c0 1.103.897 2 2 2h8v-2H4V5z">
                                                    </path>
                                                    <path d="m8 11-3 4h11l-4-6-3 4z"></path>
                                                    <path d="M19 14h-2v3h-3v2h3v3h2v-3h3v-2h-3z"></path>
                                                </svg>
                                                <span class="text-xs text-center text-primary-600 font-semibold">Add
                                                    images
                                                    ({{countSelectedImg}}/{{maxSelectedImg}})</span>
                                                <input name="ImageFile" type="file" id="choseQUES" class="hidden"
                                                    multiple accept="image/jpeg, image/png, image/jpg"
                                                    (change)="getImage($event)" />
                                            </label>
                                        </div>
                                    </div>

                                    <div>
                                        @if(images.length > maxSelectedImg){
                                        <span
                                            class="text-sm text-red-800 rounded-lg dark:bg-gray-800 dark:text-red-400">
                                            Bạn chỉ
                                            có thể
                                            chọn {{maxSelectedImg}} hình đính
                                            kèm</span>
                                        }
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="name" class="block my-2 text-sm font-medium text-gray-900 dark:text-white">
                                    Thứ tự
                                </label>
                                <input type="number" formControlName="Stock"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    placeholder="Enter sort order" required="">
                                @if(fm.Stock.errors && (fm.Stock.dirty || fm.Stock.touched || formSubmitted)){
                                @if(fm.Stock.errors.required){
                                <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400"
                                    role="alert">
                                    <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                                    </svg>
                                    <span class="sr-only">Info</span>
                                    <div>
                                        <span class="font-medium">Required!</span> Please enter sort order
                                    </div>
                                </div>
                                }
                                }
                            </div>

                            <div class="grid grid-cols-2">
                                <div>
                                    <label for="countries"
                                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                        Hiển thị trang chủ
                                    </label>
                                    <label class="inline-flex items-center me-5 cursor-pointer">
                                        <input type="checkbox" value="" class="sr-only peer"
                                            formControlName="ShowInHome">
                                        <div
                                            class="relative w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-500">
                                        </div>
                                    </label>
                                </div>
                                <div>
                                    <label for="countries"
                                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Hiển thị
                                    </label>
                                    <label class="inline-flex items-center me-5 cursor-pointer">
                                        <input type="checkbox" value="" class="sr-only peer" formControlName="Active">
                                        <div
                                            class="relative w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-500">
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="col-span-2">
                            <label for="message"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nội dung</label>
                            <!-- <quill-editor formControlName="Detail"
                                class="block w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"></quill-editor> -->
                            <div
                                class="w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                <app-text-editor formControlName="Detail"></app-text-editor>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div
            class="pagination absolute shadow-2xl border-t border-gray-200 z-20 bg-white sm:rounded-lg dark:bg-gray-600 w-full bottom-0 flex items-center flex-column flex-wrap md:flex-row justify-center py-2">
            <button type="button" (click)="onCancel()"
                class="text-white bg-gradient-to-br from-pink-500 to-primary-400 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-pink-200 dark:focus:ring-pink-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2 flex gap-2 hover:gap-6">
                <svg class="w-[25px] h-[25px] animate-pulse hover:animate-ping text-white dark:text-white rotate-180"
                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                    viewBox="0 0 24 24">
                    <path fill-rule="evenodd"
                        d="M3 4a1 1 0 0 0-.822 1.57L6.632 12l-4.454 6.43A1 1 0 0 0 3 20h13.153a1 1 0 0 0 .822-.43l4.847-7a1 1 0 0 0 0-1.14l-4.847-7a1 1 0 0 0-.822-.43H3Z"
                        clip-rule="evenodd" />
                </svg>
                Hủy
            </button>

            <button type="submit"
                class="group flex gap-2 hover:gap-6 text-white bg-gradient-to-l  from-lime-200 via-lime-400 to-lime-500 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-lime-300 dark:focus:ring-lime-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2">
                Lưu
                <svg class="w-[25px] h-[25px] animate-pulse group-hover:animate-ping  text-white dark:text-white "
                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                    viewBox="0 0 24 24">
                    <path
                        d="m7.4 3.736 3.43 3.429A5.046 5.046 0 0 1 12.133 7c.356.01.71.06 1.056.147l3.41-3.412a2.32 2.32 0 0 1 .451-.344A9.89 9.89 0 0 0 12.268 2a10.022 10.022 0 0 0-5.322 1.392c.165.095.318.211.454.344Zm11.451 1.54-.127-.127a.5.5 0 0 0-.706 0l-2.932 2.932c.03.023.05.054.078.077.237.194.454.41.651.645.033.038.077.067.11.107l2.926-2.927a.5.5 0 0 0 0-.707Zm-2.931 9.81c-.025.03-.058.052-.082.082a4.97 4.97 0 0 1-.633.639c-.04.036-.072.083-.115.117l2.927 2.927a.5.5 0 0 0 .707 0l.127-.127a.5.5 0 0 0 0-.707l-2.932-2.931Zm-1.443-4.763a3.037 3.037 0 0 0-1.383-1.1l-.012-.007a2.956 2.956 0 0 0-1-.213H12a2.964 2.964 0 0 0-2.122.893c-.285.29-.509.634-.657 1.013l-.009.016a2.96 2.96 0 0 0-.21 1 2.99 2.99 0 0 0 .488 1.716l.032.04a3.04 3.04 0 0 0 1.384 1.1l.012.007c.319.129.657.2 1 .213.393.015.784-.05 1.15-.192.012-.005.021-.013.033-.018a3.01 3.01 0 0 0 1.676-1.7v-.007a2.89 2.89 0 0 0 0-2.207 2.868 2.868 0 0 0-.27-.515c-.007-.012-.02-.025-.03-.039Zm6.137-3.373a2.53 2.53 0 0 1-.349.447l-3.426 3.426c.112.428.166.869.161 1.311a4.954 4.954 0 0 1-.148 1.054l3.413 3.412c.133.134.249.283.347.444A9.88 9.88 0 0 0 22 12.269a9.913 9.913 0 0 0-1.386-5.319ZM16.6 20.264l-3.42-3.421c-.386.1-.782.152-1.18.157h-.135c-.356-.01-.71-.06-1.056-.147L7.4 20.265a2.503 2.503 0 0 1-.444.347A9.884 9.884 0 0 0 11.732 22H12a9.9 9.9 0 0 0 5.044-1.388 2.515 2.515 0 0 1-.444-.348ZM3.735 16.6l3.426-3.426a4.608 4.608 0 0 1-.013-2.367L3.735 7.4a2.508 2.508 0 0 1-.349-.447 9.889 9.889 0 0 0 0 10.1 2.48 2.48 0 0 1 .35-.453Zm5.101-.758a4.959 4.959 0 0 1-.65-.645c-.034-.038-.078-.067-.11-.107L5.15 18.017a.5.5 0 0 0 0 .707l.127.127a.5.5 0 0 0 .706 0l2.932-2.933c-.029-.018-.049-.053-.078-.076Zm-.755-6.928c.03-.037.07-.063.1-.1.183-.22.383-.423.6-.609.046-.04.081-.092.128-.13L5.983 5.149a.5.5 0 0 0-.707 0l-.127.127a.5.5 0 0 0 0 .707l2.932 2.931Z" />
                </svg>

            </button>
        </div>
    </div>
</form>
}