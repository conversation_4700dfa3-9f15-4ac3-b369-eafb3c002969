import { Component } from '@angular/core';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexTitleSubtitle,
  ApexDataLabels,
  ApexFill,
  ApexMarkers,
  ApexYAxis,
  ApexXAxis,
  ApexTooltip,
  NgApexchartsModule
} from "ng-apexcharts";
import { ReportService } from '../../../services/report/report.service';
import { NumberFormatPipe } from '../../../pipe/number-format/number-format.pipe';

@Component({
  selector: 'app-dashboard-api',
  imports: [

    NumberFormatPipe,
    NgApexchartsModule,
  ],
  templateUrl: './dashboard-api.component.html',
  styleUrl: './dashboard-api.component.css'
})
export class DashboardApiComponent {
  public series: ApexAxisChartSeries | any;
  public chart: ApexChart = { type: 'line', height: 350 };
  public yaxis: ApexYAxis | any;
  public xaxis: ApexXAxis = {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    title: {
      text: 'Month'
    }
  };
  public dataLabels: any = {
    enabled: true, // Hiển thị dữ liệu trên các điểm
    formatter: (val: number) => val?.toLocaleString(), // Định dạng số (ví dụ: thêm dấu phẩy)
    style: {
      fontSize: '12px',
      colors: ['#304758'] // Màu chữ
    }
  };
  dashboardValue: any;

  constructor(
    private reportService: ReportService
  ) {
    this.series = [{
      name: 'line',
      data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    }];
    this.yaxis = {
      title: {
        text: 'Request'
      },
      min: 0,
      // max: 100
    };
  }


  async ngOnInit() {
    const res = await this.reportService.getPartnerApiReport().toPromise();
    try {
      if (res.isSuccessed) {
        this.dashboardValue = res.resultObj;
        const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
        var monthNow = new Date().getMonth() + 1; // Lấy tháng hiện tại (0-11) và cộng thêm 1 để chuyển sang tháng (1-12)

        this.series = this.dashboardValue.data.map((item: any) => {
          const monthData = new Array(12).fill(null); // Khởi tạo mảng 12 phần tử với giá trị null
          item.apiOverview.forEach((overview: any) => {
            const monthIndex = months.indexOf(overview.month); // Tìm vị trí tháng trong danh sách
            if (monthIndex !== -1) {
              monthData[monthIndex] = overview.bookings; // Gán dữ liệu vào đúng vị trí
            }
          });
          return {
            name: item.name,
            data: monthData
          };
        });


        this.yaxis = {
          title: {
            text: 'Request'
          },
          min: 0,
        };
        console.log(this.series);
      }
    }
    catch (error) {
      console.log(error);
    }
  }
}
