<div class="w-full relative text-gray-800">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
        class="lucide lucide-search absolute left-2 top-2.5 h-4 w-4">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
    </svg>
    <input (input)="searchAirPorts($event)" type="text" [value]="searchTerm"
        class="flex h-10 w-full rounded-md border border-input bg-background ps-10 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder: focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-8"
        placeholder="Search for country or province">
</div>
<div class="w-full flex text-gray-800 bg-white min-h-60 shadow-lg">
    <ng-container *ngIf="isLoading">
        <div class="w-full flex items-center justify-center min-h-60">
            <span class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mr-2"></span>
            <span>Đang tìm kiếm...</span>
        </div>
    </ng-container>
    <ng-container *ngIf="!isLoading">
        <ng-container *ngIf="AirportsDefaultFiltered.length === 0 && !hasSearched">
            <div class="border-r border-gray-100">
                <div *ngFor="let continent of AirportsDefault" (click)="continentClick(continent.continentCode)"
                    class="p-2 border-b cursor-pointer"
                    [ngClass]="{'bg-primary-500 text-white': continent.selected, 'bg-transparent': !continent.selected}">
                    <h3 class="text-sm font-semibold text-nowrap text-left">{{continent.continentName}}</h3>
                </div>
            </div>

            <div class="w-full">
                <div class="w-full overflow-y-scroll bg-white max-h-80 min-h-80 space-y-1 dark:bg-gray-700">
                    <ng-container *ngFor="let continent of AirportsDefault">
                        <ng-container *ngIf="continent.selected">
                            <div *ngFor="let airport of continent.airports" (click)="onAirportClick(airport)"
                                class="flex px-2 justify-between border-b border-gray-100 items-start overflow-hidden gap-1 whitespace-nowrap rounded-md text-sm cursor-pointer hover:bg-primary-400 hover:text-white group font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 w-full max-md:justify-between">
                                <div class="flex flex-col items-start w-full">
                                    <span class="text-wrap text-left">{{airport.cityName}}</span>
                                    <span class="text-xs font-light text-wrap text-left">{{airport.name}}</span>
                                </div>
                                <span
                                    class="text-primary-600 font-extrabold group-hover:text-white">{{airport.code}}</span>
                            </div>
                        </ng-container>
                    </ng-container>
                </div>
            </div>
        </ng-container>

        <ng-container *ngIf="AirportsDefaultFiltered !== null && hasSearched">
            <ng-container *ngIf="AirportsDefaultFiltered.length === 0">
                <div class="p-2 w-full text-center text-gray-500">
                    Không tìm thấy sân bay mà quý khách đã nhập
                </div>
            </ng-container>

            <ng-container *ngIf="AirportsDefaultFiltered.length > 0">
                <div class="p-2 w-full overflow-y-scroll max-h-80">
                    <ng-container *ngFor="let item of AirportsDefaultFiltered">
                        <ng-container *ngIf="item.isParent">
                            <div (click)="itemParentClick(item.code)" class="w-full">
                                <div
                                    class="inline-flex min-h-fit justify-between py-2 border-b border-gray-100 items-start overflow-hidden gap-1 whitespace-nowrap rounded-md text-sm cursor-pointer hover:bg-primary-400 hover:text-white group font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 w-full max-md:justify-between">
                                    <div class="flex flex-col items-start">
                                        <span class="max-w-48 max-md:min-w-36 text-wrap text-left">{{item.name}}</span>
                                        <span class="text-xs font-light">Tất cả sân bay tại {{item.name}}</span>
                                    </div>
                                    <span>
                                        <svg xmlns="http://www.w3.org/2000/svg"
                                            class="w-4 h-4 text-gray-600 dark:text-white group-hover:text-white"
                                            [ngClass]="{'rotate-180': item.selected}" viewBox="0 0 320 512">
                                            <path
                                                d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z" />
                                        </svg>
                                    </span>
                                </div>
                                <div *ngIf="item.selected" class="w-full bg-gray-100 px-2">
                                    <div *ngFor="let airport of item.child" (click)="onAirportClick(airport)"
                                        class="flex px-2 justify-between border-b border-gray-100 items-start overflow-hidden gap-1 whitespace-nowrap rounded-md text-sm cursor-pointer hover:bg-primary-400 hover:text-white group font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 w-full max-md:justify-between">
                                        <div class="flex flex-col items-start w-full">
                                            <span class="text-wrap text-left">{{airport.cityName}}</span>
                                            <span class="text-xs font-light text-wrap text-left">{{airport.name}}</span>
                                        </div>
                                        <span
                                            class="text-primary-600 font-extrabold group-hover:text-white">{{airport.code}}</span>
                                    </div>
                                </div>
                            </div>
                        </ng-container>

                        <ng-container *ngIf="!item.isParent">
                            <div (click)="onAirportClick(item)"
                                class="flex px-2 justify-between border-b border-gray-100 items-start overflow-hidden gap-1 whitespace-nowrap rounded-md text-sm cursor-pointer hover:bg-primary-400 hover:text-white group font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 w-full max-md:justify-between">
                                <div class="flex flex-col items-start w-full">
                                    <span class="text-wrap text-left">{{item.cityName}}</span>
                                    <span class="text-xs font-light text-wrap text-left">{{item.name}}</span>
                                </div>
                                <span
                                    class="text-primary-600 font-extrabold group-hover:text-white">{{item.code}}</span>
                            </div>
                        </ng-container>
                    </ng-container>
                </div>
            </ng-container>
        </ng-container>
    </ng-container>
</div>