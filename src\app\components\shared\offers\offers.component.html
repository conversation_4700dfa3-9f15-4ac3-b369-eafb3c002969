<section class="py-20 bg-gradient-to-b from-white to-primary-50 relative overflow-hidden">
    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row md:items-end justify-between mb-12">
            <div>
                <div
                    class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200">
                    Ưu đãi đặc biệt</div>
                <h2 class="text-3xl md:text-4xl font-bold mb-2 tracking-tight text-gray-600"><PERSON><PERSON><PERSON>ến mãi hấp dẫn</h2>
                <p class="text-gray-600 max-w-2xl">Đừng bỏ lỡ những ưu đãi đặc biệt cho chuyến đi sắp tới của bạn</p>
            </div><a href="/offers"
                class="hidden md:flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors mt-4 md:mt-0">Xem
                tất cả ưu đãi<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chevron-right h-4 w-4 ml-1">
                    <path d="m9 18 6-6-6-6"></path>
                </svg></a>
        </div>
        <div class="relative">
            <div class="flex overflow-x-hidden space-x-6 pb-8 hide-scrollbar snap-x snap-mandatory">
                <div class="min-w-[300px] md:min-w-[400px] snap-start">
                    <div class="border bg-card text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-300 h-full rounded-2xl group"
                        data-v0-t="card">
                        <div class="relative h-52 w-full overflow-hidden"><img alt="Khám phá Đông Nam Á" loading="lazy"
                                decoding="async" data-nimg="fill"
                                class="object-cover transition-transform duration-700 group-hover:scale-105"
                                src="\assets\img\background\service_tour.jpg"
                                style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent">
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-6">
                                <h3 class="text-xl font-bold text-white mb-1 drop-shadow-sm">Khám phá Đông Nam Á</h3>
                                <p class="text-white/90 text-sm line-clamp-2 drop-shadow-sm">Giảm đến 30% cho các chuyến
                                    bay đến
                                    Singapore, Thái Lan, Malaysia</p>
                            </div>
                        </div>
                        <div class="p-6 bg-white">
                            <div class="flex items-center text-sm text-gray-500 mb-4"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-calendar h-4 w-4 mr-2 text-primary-500">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg><span>Có hiệu lực đến: 30/06/2025</span></div>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center bg-primary-50 px-3 py-2 rounded-lg"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-tag h-4 w-4 mr-2 text-primary-600">
                                        <path
                                            d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z">
                                        </path>
                                        <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
                                    </svg><span class="font-mono font-medium text-primary-700">SEASIA30</span></div>
                                <button
                                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-full">Sử
                                    dụng ngay</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="min-w-[300px] md:min-w-[400px] snap-start">
                    <div class="border bg-card text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-300 h-full rounded-2xl group"
                        data-v0-t="card">
                        <div class="relative h-52 w-full overflow-hidden"><img alt="Ưu đãi mùa hè" loading="lazy"
                                decoding="async" data-nimg="fill"
                                class="object-cover transition-transform duration-700 group-hover:scale-105"
                                src="\assets\img\background\service_tour.jpg"
                                style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent">
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-6">
                                <h3 class="text-xl font-bold text-white mb-1 drop-shadow-sm">Ưu đãi mùa hè</h3>
                                <p class="text-white/90 text-sm line-clamp-2 drop-shadow-sm">Giảm 25% cho tất cả các
                                    chuyến bay nội
                                    địa trong tháng 7 và tháng 8</p>
                            </div>
                        </div>
                        <div class="p-6 bg-white">
                            <div class="flex items-center text-sm text-gray-500 mb-4"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-calendar h-4 w-4 mr-2 text-primary-500">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg><span>Có hiệu lực đến: 31/08/2025</span></div>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center bg-primary-50 px-3 py-2 rounded-lg"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-tag h-4 w-4 mr-2 text-primary-600">
                                        <path
                                            d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z">
                                        </path>
                                        <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
                                    </svg><span class="font-mono font-medium text-primary-700">SUMMER25</span></div>
                                <button
                                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-full">Sử
                                    dụng ngay</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="min-w-[300px] md:min-w-[400px] snap-start">
                    <div class="border bg-card text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-300 h-full rounded-2xl group"
                        data-v0-t="card">
                        <div class="relative h-52 w-full overflow-hidden"><img alt="Khám phá Châu Âu" loading="lazy"
                                decoding="async" data-nimg="fill"
                                class="object-cover transition-transform duration-700 group-hover:scale-105"
                                src="\assets\img\background\service_tour.jpg"
                                style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent">
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-6">
                                <h3 class="text-xl font-bold text-white mb-1 drop-shadow-sm">Khám phá Châu Âu</h3>
                                <p class="text-white/90 text-sm line-clamp-2 drop-shadow-sm">Giảm 20% cho các chuyến bay
                                    đến Paris,
                                    London, Rome</p>
                            </div>
                        </div>
                        <div class="p-6 bg-white">
                            <div class="flex items-center text-sm text-gray-500 mb-4"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-calendar h-4 w-4 mr-2 text-primary-500">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg><span>Có hiệu lực đến: 30/09/2025</span></div>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center bg-primary-50 px-3 py-2 rounded-lg"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-tag h-4 w-4 mr-2 text-primary-600">
                                        <path
                                            d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z">
                                        </path>
                                        <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
                                    </svg><span class="font-mono font-medium text-primary-700">EUROPE20</span></div>
                                <button
                                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-full">Sử
                                    dụng ngay</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="min-w-[300px] md:min-w-[400px] snap-start">
                    <div class="border bg-card text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-300 h-full rounded-2xl group"
                        data-v0-t="card">
                        <div class="relative h-52 w-full overflow-hidden"><img alt="Ưu đãi đặc biệt cho hội viên"
                                loading="lazy" decoding="async" data-nimg="fill"
                                class="object-cover transition-transform duration-700 group-hover:scale-105"
                                src="\assets\img\background\service_tour.jpg"
                                style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent">
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-6">
                                <h3 class="text-xl font-bold text-white mb-1 drop-shadow-sm">Ưu đãi đặc biệt cho hội
                                    viên</h3>
                                <p class="text-white/90 text-sm line-clamp-2 drop-shadow-sm">Giảm thêm 10% cho tất cả
                                    các chuyến bay
                                    khi đăng ký thành viên</p>
                            </div>
                        </div>
                        <div class="p-6 bg-white">
                            <div class="flex items-center text-sm text-gray-500 mb-4"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-calendar h-4 w-4 mr-2 text-primary-500">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg><span>Có hiệu lực đến: 31/12/2025</span></div>
                            <div class="flex justify-between items-center">
                                <div class="flex items-center bg-primary-50 px-3 py-2 rounded-lg"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-tag h-4 w-4 mr-2 text-primary-600">
                                        <path
                                            d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z">
                                        </path>
                                        <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
                                    </svg><span class="font-mono font-medium text-primary-700">MEMBER10</span></div>
                                <button
                                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-full">Sử
                                    dụng ngay</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div><button
                class="items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input hover:bg-accent hover:text-accent-foreground absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 bg-white shadow-lg z-10 rounded-full h-12 w-12 hidden md:flex border-none"
                disabled=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chevron-left h-5 w-5">
                    <path d="m15 18-6-6 6-6"></path>
                </svg><span class="sr-only">Scroll left</span></button><button
                class="items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input hover:bg-accent hover:text-accent-foreground absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-white shadow-lg z-10 rounded-full h-12 w-12 hidden md:flex border-none"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chevron-right h-5 w-5">
                    <path d="m9 18 6-6-6-6"></path>
                </svg><span class="sr-only">Scroll right</span></button>
        </div>
        <div class="flex md:hidden justify-center mt-8"><a href="/offers"
                class="flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors">Xem tất
                cả
                ưu
                đãi<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chevron-right h-4 w-4 ml-1">
                    <path d="m9 18 6-6-6-6"></path>
                </svg></a></div>
    </div>
    <div class="absolute top-1/4 -right-16 w-32 h-32 bg-primary-100 rounded-full opacity-50 blur-2xl"></div>
    <div class="absolute bottom-1/4 -left-16 w-32 h-32 bg-blue-100 rounded-full opacity-50 blur-2xl"></div>
</section>