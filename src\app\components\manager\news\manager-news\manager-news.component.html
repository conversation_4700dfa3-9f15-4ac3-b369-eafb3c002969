<div class="container relative w-full md:p-4 p-2 shadow-lg bg-white dark:bg-gray-900 rounded-lg h-full min-w-full">

    <div class="w-full h-full relative flex flex-col pb-4  sm:rounded-lg  md:gap-6 gap-2">
        <div class="w-full shadow rounded-xl flex flex-col p-2 md:gap-4 gap-2">
            <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div class="flex items-center">
                    <a routerLink="/manager/news" class="flex items-center gap-2 text-lg font-semibold md:text-xl">
                        <span>Quản lý tin tức</span>
                    </a>
                    <nav class="ml-8 flex items-center gap-6 text-gray-400">
                        <a routerLink="/manager/news" routerLinkActive="!text-gray-800"
                            [routerLinkActiveOptions]="{exact: true}" class="text-sm hover:text-gray-600">Tin tức</a>
                        <a routerLink="/manager/news/category" routerLinkActive="!text-gray-800"
                            [routerLinkActiveOptions]="{exact: true}" class="text-sm  hover:text-gray-600">Danh
                            mục</a>
                    </nav>
                </div>
                <button routerLink="/manager/news/create"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary-400 text-white hover:bg-primary/90 h-10 px-4 py-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-circle-plus mr-2 h-4 w-4">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M8 12h8"></path>
                        <path d="M12 8v8"></path>
                    </svg>Thêm tin tức mới
                </button>
            </div>
            <div class="flex flex-row flex-wrap justify-between md:gap-6 gap-2 relative">
                <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between w-full">
                    <div class="flex w-full max-w-sm items-center space-x-2">
                        <input
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder: focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            placeholder="Tìm kiếm tin tức..." type="search" [(ngModel)]="searchModel.Keyword"
                            (keyup.enter)="onSubmitSearch()">
                        <button
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input  hover:bg-primary-400 hover:text-white h-10 w-10"
                            type="button" (click)="onSubmitSearch()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-search h-4 w-4">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.3-4.3"></path>
                            </svg>
                            <span class="sr-only">Tìm kiếm</span>
                        </button>
                    </div>
                    <div class="flex items-center gap-2 flex-wrap">
                        <select class="rounded-md border border-input bg-background px-3 py-2 text-sm cursor-pointer"
                            [(ngModel)]="searchModel.Type" (change)="onSubmitSearch()">
                            <option value="">Tất cả danh mục</option>
                            @for (category of categoryNews; track $index) {
                            <option [value]="category.seoTitle">{{category.name}}</option>
                            }
                        </select>
                        <select class="rounded-md border border-input bg-background px-3 py-2 text-sm cursor-pointer"
                            [(ngModel)]="searchModel.FilterStatus" (change)="onSubmitSearch()">
                            <option [ngValue]="null">Tất cả trạng thái</option>
                            <option [ngValue]="1">Đã đăng</option>
                            <option [ngValue]="-2">Khóa</option>
                        </select>
                        <select [(ngModel)]="searchModel.PageSize" (change)="changePageSize($event)"
                            class="rounded-md border border-input bg-background px-3 py-2 text-sm cursor-pointer">
                            <option value="10" selected>10 dòng</option>
                            <option value="20">20 dòng</option>
                            <option value="50">50 dòng</option>
                            <option value="100">100 dòng</option>
                            <option value="200">200 dòng</option>
                        </select>
                    </div>
                </div>


            </div>


        </div>

        <div class="w-full h-full overflow-auto">
            <table
                class="w-full h-fill-available text-sm text-left sm:mb-28 max-sm:mb-32 rtl:text-right text-gray-500 dark:text-white">
                <thead
                    class="sticky -top-1 text-xs text-white uppercase bg-gradient-to-r from-[#fb6340] via-[#fb6340] to-[#fbb140] dark:bg-gray-700  dark:bg-none">
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                ID

                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Tiêu dề
                                <a (click)="sortTable('Name')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Danh mục
                                <a (click)="sortTable('CategoryName')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Tác giả
                                <a (click)="sortTable('Author')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Lượt xem
                                <a (click)="sortTable('viewCount')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Ngày đăng
                                <a (click)="sortTable('TimeCreate')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Trạng thái
                                <a (click)="sortTable('Active')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Thao tác
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if(dataTable.items.length > 0){
                    @for(item of dataTable.items || []; track $index ){
                    <tr (dblclick)="handleDoubleClick(item)"
                        [ngClass]="{'bg-red-200 dark:bg-red-600': item.isActive === false, 'odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800': item.isActive}"
                        class="cursor-pointer border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">

                        <td class="p-2 font-medium text-gray-800 text-left">
                            {{item.index}}
                        </td>
                        <td class="p-2 font-medium text-gray-800">
                            <div class="flex flex-row items-start gap-2 justify-start">
                                <img [src]="item.pathImage" alt="Image" class="w-16 h-16 rounded-md object-cover">
                                <div class="flex flex-col gap-1">
                                    <span
                                        class="text-base text-gray-500 font-normal dark:text-gray-400 text-left line-clamp-2 min-w-48">{{item.name}}</span>

                                </div>

                            </div>
                        </td>
                        <td class="p-2 font-medium text-gray-800">
                            {{item.categoryName}}
                        </td>
                        <td class="p-2 text-center text-nowrap">
                            {{item.author}}
                        </td>
                        <td class="p-2 text-center">
                            <span class="bg-yellow-100 text-yellow-700 font-semibold rounded px-2 py-1 shadow-sm">
                                {{item.viewCount}}
                            </span>
                        </td>
                        <td class="p-2 text-left">
                            {{item.timeCreate }}
                        </td>

                        <td class="p-2 text-center">
                            <span [class]="
                            'px-2 py-1 border rounded-full  ' +
                            (item.status? 'bg-green-200 text-green-500 border-green-500' : '') +
                            (!item.status ? 'bg-red-200 text-red-500 border-red-500' : '') 
                          ">
                                <span class="text-nowrap" *ngIf="item.status">Đã đăng </span>
                                <span class="text-nowrap" *ngIf="!item.status">Khóa</span>
                            </span>
                        </td>
                        <td class="p-4 align-middle  text-right">
                            <div class="flex justify-end gap-2">
                                <a href="/news/{{item.seoCategory}}/{{item.seoTitle}}" target="_blank"
                                    class="inline-flex items-center hover:bg-gray-300 justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground h-10 w-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-eye h-4 w-4">
                                        <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                    <span class="sr-only">Xem</span>
                                </a>
                                <a routerLink="/manager/news/{{item.id}}"
                                    class="inline-flex items-center hover:bg-gray-300 justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground h-10 w-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-square-pen h-4 w-4">
                                        <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                                    </svg>
                                    <span class="sr-only">Chỉnh sửa</span>
                                </a>
                            </div>
                        </td>
                    </tr>
                    }
                    }@else {
                    <tr
                        class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800 border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">
                        <td class="px-6 py-4" colspan="9">
                            <div class="flex items-center justify-center">
                                <span class="text-gray-500 dark:text-gray-400">
                                    Không có dữ liệu
                                </span>
                            </div>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>

    </div>
    <nav class="pagination absolute bg-white sm:rounded-lg dark:bg-gray-600 w-full bottom-0 flex items-center flex-column flex-wrap md:flex-row justify-between p-4"
        aria-label="Table navigation">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Showing
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable.from}} - {{dataTable.to}}</span> of
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable.totalRecords}}</span></span>
        <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(1)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m17 16-4-4 4-4m-6 8-4-4 4-4" />
                    </svg>
                </a>
            </li>
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(dataTable.pageIndex - 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m14 8-4 4 4 4" />
                    </svg>
                </a>
            </li>

            <li *ngFor="let page of range(startIndex, finishIndex); let i = index"
                [class.active]="page === dataTable.pageIndex">
                <a class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                    (click)="getUrl(page)" [title]="'Page ' + page">
                    {{ page }}
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageIndex + 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m10 16 4-4-4-4" />
                    </svg>
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageCount)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m7 16 4-4-4-4m6 8 4-4-4-4" />
                    </svg>
                </a>
            </li>
        </ul>
    </nav>


</div>