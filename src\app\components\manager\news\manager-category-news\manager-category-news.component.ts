import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { NgbCalendar, NgbDate, NgbDateParserFormatter, NgbDatepickerModule, NgbDropdownModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { range } from '../../../../common/function.common';

import { LoaderService } from '../../../../services/loader/loader.service';
import { SearchStateService } from '../../../../services/search-state/search-state.service';
import { NewsService } from '../../../../services/news/news.service';
import { CategoryNewsService } from '../../../../services/category-news/category-news.service';

interface CategoryNewsModel {
  name: string;
  seoTitle: string;
  description: string;
  sortOrder: number;
  status: boolean;
}

interface DataTable {
  allRecords: number;
  from: number;
  to: number;
  pageCount: number;
  pageSize: number;
  pageIndex: number;
  totalRecords: number;
  items: any[];
}

@Component({
  selector: 'app-manager-category-news',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterLink,
    RouterLinkActive,
    NgbDropdownModule,
    NgbDatepickerModule,
  ],
  templateUrl: './manager-category-news.component.html',
  styleUrl: './manager-category-news.component.css'
})

export class ManagerCategoryNewsComponent implements OnInit {
  // Services
  private readonly newsService = inject(NewsService);
  private readonly categoryNewsService = inject(CategoryNewsService);
  private readonly toastService = inject(ToastrService);
  private readonly calendar = inject(NgbCalendar);
  private readonly formatter = inject(NgbDateParserFormatter);
  private readonly router = inject(Router);
  private readonly searchStateService = inject(SearchStateService<any>);
  private readonly fb = inject(FormBuilder);
  public readonly loaderService = inject(LoaderService);

  // Form
  categoryForm!: FormGroup;
  isEditMode = false;
  selectedCategorySeoTitle: string | null = null;

  // ViewChild
  @ViewChild('fromDatepicker') fromDatepicker: any;
  @ViewChild('toDatepicker') toDatepicker: any;
  // Date picker properties
  hoveredDate: NgbDate | null = null;
  fromDate: NgbDate = this.calendar.getToday();
  toDate: NgbDate = this.calendar.getToday();
  isMobile = false;

  // Search and pagination
  searchModel: any = {
    TypeDate: '',
    PageIndex: 1,
    PageSize: 10,
    SortColumn: '',
    SortOrder: 'asc',
    Keyword: '',
  };

  dataTable: DataTable = {
    allRecords: 0,
    from: 0,
    to: 0,
    pageCount: 0,
    pageSize: 0,
    pageIndex: 0,
    totalRecords: 0,
    items: []
  };
  startIndex = 1;
  finishIndex = 1;

  // Modal properties
  showAddCategoryModal = false;

  constructor() {
    this.initializeScreenSize();
    this.initForm();
  }

  private initForm(): void {
    this.categoryForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      seoTitle: [''],
      description: ['', [Validators.maxLength(500)]],
      sortOrder: [1, [Validators.required, Validators.min(1)]],
      status: [true]
    });
  }

  private initializeScreenSize(): void {
    this.checkScreenSize();
    window.addEventListener('resize', () => this.checkScreenSize());
  }

  private checkScreenSize(): void {
    this.isMobile = window.innerWidth < 768;
  }

  async ngOnInit(): Promise<void> {
    const savedState = this.searchStateService.getSearchState();
    if (savedState) {
      this.searchModel = savedState;
    }
    await this.getPaging();
  }

  // Form methods
  openAddCategoryModal() {
    this.isEditMode = false;
    this.selectedCategorySeoTitle = null;
    this.categoryForm.reset({
      sortOrder: 1,
      status: true
    });
    this.showAddCategoryModal = true;
  }

  openEditCategoryModal(category: any) {
    this.isEditMode = true;
    this.selectedCategorySeoTitle = category.seoTitle || null;
    this.categoryForm.patchValue({
      name: category.name,
      seoTitle: category.seoTitle,
      description: category.description,
      sortOrder: category.sortOrder,
      status: category.active
    });
    this.showAddCategoryModal = true;
  }

  closeAddCategoryModal() {
    this.showAddCategoryModal = false;
    this.categoryForm.reset();
  }

  async submitCategory() {
    if (this.categoryForm.invalid) {
      this.markFormGroupTouched(this.categoryForm);
      return;
    }

    try {
      const categoryData: CategoryNewsModel = this.categoryForm.value;

      const response = await this.categoryNewsService.savePartner(categoryData).toPromise();
      if (response.isSuccessed) {
        this.toastService.success(this.isEditMode ? 'Cập nhật danh mục thành công' : 'Thêm danh mục mới thành công');
        this.closeAddCategoryModal();
        await this.getPaging();
      } else {
        this.toastService.error(response.message || (this.isEditMode ? 'Có lỗi xảy ra khi cập nhật danh mục' : 'Có lỗi xảy ra khi thêm danh mục'));
      }
    } catch (error) {
      this.toastService.error('Có lỗi xảy ra khi xử lý yêu cầu');
    }
  }


  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  // Pagination methods
  async changePageSize(event: Event): Promise<void> {
    const value = (event.target as HTMLSelectElement).value;
    this.searchModel.PageSize = Number(value);
  }

  async getUrl(page: number): Promise<void> {
    if (page < 1 || page > this.dataTable.pageCount || page === this.searchModel.PageIndex) {
      return;
    }
    this.searchModel.PageIndex = page;
    await this.getPaging();
  }

  // Data fetching methods
  async getPaging(): Promise<void> {
    try {
      const res = await this.categoryNewsService.getPartnerCateforyNewsPaging(this.searchModel).toPromise();
      if (res.isSuccessed) {
        this.dataTable = res.resultObj;
        this.updatePageIndices();
      }
    } catch (error) {
      this.toastService.error('Có lỗi xảy ra khi tải dữ liệu');
    }
  }

  private updatePageIndices(): void {
    if (this.dataTable.pageCount <= 5) {
      this.startIndex = 1;
      this.finishIndex = this.dataTable.pageCount;
    } else if (this.dataTable.pageIndex <= 3) {
      this.startIndex = 1;
      this.finishIndex = 5;
    } else if (this.dataTable.pageIndex + 2 >= this.dataTable.pageCount) {
      this.startIndex = this.dataTable.pageCount - 4;
      this.finishIndex = this.dataTable.pageCount;
    } else {
      this.startIndex = this.dataTable.pageIndex - 2;
      this.finishIndex = this.dataTable.pageIndex + 2;
    }
  }

  range(start: number, end: number): number[] {
    return range(start, end);
  }

  handleDoubleClick(item: any): void {
    this.searchStateService.setSearchState(this.searchModel);
    this.router.navigate(['/partner/flight', item.id]);
  }

  async sortTable(column: string): Promise<void> {
    if (this.searchModel.SortColumn === column) {
      this.searchModel.SortOrder = this.searchModel.SortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.searchModel.SortColumn = column;
      this.searchModel.SortOrder = 'asc';
    }
    await this.getPaging();
  }

  async onSubmitSearch(): Promise<void> {
    this.searchModel.PageIndex = 1;
    this.searchStateService.setSearchState(this.searchModel);
    await this.getPaging();
  }
}