<section class=" bg-gradient-to-b from-white to-primary-50 relative overflow-hidden max-md:px-2 pt-6" #newsSection>
    <div class="container mx-auto px-4">
        <div class="relative pb-8">
            <div class="flex flex-col md:flex-row md:items-end justify-between mb-12">
                <div>
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200"
                        data-v0-t="badge">Tin tức &amp; Cẩm nang</div>
                    <h2 class="text-3xl md:text-4xl font-bold mb-2 tracking-tight">Tin tức du lịch mới nhất</h2>
                    <p class="text-gray-600 max-w-2xl">Cậ<PERSON> nhật những thông tin, kinh nghiệm và cẩm nang du lịch hữu ích
                        cho chuyến đi của bạn</p>
                </div>
                <a routerLink="/news"
                    class="hidden md:flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors mt-4 md:mt-0">Xem
                    tất cả tin tức<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 ml-1">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </a>
            </div>

            <!-- Category List -->
            <div class="flex overflow-x-auto hide-scrollbar space-x-2 mb-8 pb-2">
                @if(_isLoading) {
                @for (item of [1,2,3,4]; track $index) {
                <div class="h-10 w-24 bg-gray-200 rounded-full animate-pulse"></div>
                }
                } @else {
                <button routerLink="/news"
                    [class]="'inline-flex items-center justify-center gap-2 text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 py-2 rounded-full px-4 whitespace-nowrap ' + 
                        (true ? 'bg-primary-500 hover:bg-primary-600 text-white' : 'border bg-background hover:text-accent-foreground border-gray-200 hover:border-primary-200 hover:bg-primary-50')">
                    Tất cả
                </button>
                @for (category of categories; track category.key) {
                <button routerLink="/news/{{category.key}}"
                    [class]="'inline-flex items-center justify-center gap-2 text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background hover:text-accent-foreground h-10 py-2 rounded-full px-4 whitespace-nowrap ' + 
                            (false ? 'bg-primary-500 hover:bg-primary-600 text-white border-none' : 'border-gray-200 hover:border-primary-200 hover:bg-primary-50')">
                    {{category.value}}
                </button>
                }
                }
            </div>

            <div class="grid grid-cols-1 md:grid-cols-12 gap-6 mb-12">
                <!-- Featured News -->
                <div class="md:col-span-8 group">
                    @if(_isLoading) {
                    <div class="relative h-[400px] md:h-[500px] rounded-3xl overflow-hidden bg-gray-200 animate-pulse">
                        <div class="absolute inset-0 bg-gradient-to-t from-gray-300/80 via-gray-200/50 to-transparent">
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-8">
                            <div class="h-6 w-24 bg-gray-300 rounded-full mb-4"></div>
                            <div class="h-8 w-3/4 bg-gray-300 rounded mb-4"></div>
                            <div class="h-4 w-full bg-gray-300 rounded mb-6"></div>
                        </div>
                    </div>
                    } @else if(featuredNews) {
                    <div
                        class="relative h-[400px] md:h-[500px] rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                        <img [src]="featuredNews.pathImage" [alt]="featuredNews.name"
                            class="object-cover transition-transform duration-700 group-hover:scale-105"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px;">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent"></div>
                        <div class="absolute bottom-0 left-0 right-0 p-8">
                            <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent mb-4 bg-primary-500 hover:bg-primary-600 text-white border-none"
                                data-v0-t="badge">{{featuredNews.categoryName}}</div>
                            <h3
                                class="text-2xl md:text-3xl font-bold mb-4 text-white group-hover:text-primary-200 transition-colors line-clamp-2">
                                {{featuredNews.name}}
                            </h3>
                            <p class="text-white/80 mb-6 line-clamp-2 max-w-3xl">{{featuredNews.description}}</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-white/80 space-x-4">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-calendar h-4 w-4 mr-2">
                                            <path d="M8 2v4"></path>
                                            <path d="M16 2v4"></path>
                                            <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                            <path d="M3 10h18"></path>
                                        </svg>
                                        <span>{{featuredNews.timeCreate }}</span>
                                    </div>
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-eye h-4 w-4 mr-2">
                                            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                            <circle cx="12" cy="12" r="3"></circle>
                                        </svg>
                                        <span>{{featuredNews.viewCount}}</span>
                                    </div>
                                </div>
                                <a [routerLink]="['/news', featuredNews.seoCategory ,featuredNews.seoTitle]"
                                    class="inline-flex items-center bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-full transition-colors">Đọc
                                    tiếp<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-arrow-right h-4 w-4 ml-2">
                                        <path d="M5 12h14"></path>
                                        <path d="m12 5 7 7-7 7"></path>
                                    </svg></a>
                            </div>
                        </div>
                    </div>
                    }
                </div>

                <!-- Secondary News -->
                <div class="md:col-span-4 grid grid-rows-2 gap-6">
                    @if(_isLoading) {
                    @for (item of [1,2]; track $index) {
                    <div class="relative h-[200px] md:h-[242px] rounded-3xl overflow-hidden bg-gray-200 animate-pulse">
                        <div class="absolute inset-0 bg-gradient-to-t from-gray-300/80 via-gray-200/50 to-transparent">
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <div class="h-4 w-20 bg-gray-300 rounded-full mb-2"></div>
                            <div class="h-6 w-3/4 bg-gray-300 rounded mb-2"></div>
                        </div>
                    </div>
                    }
                    } @else {
                    @for (item of secondaryNews; track item.id) {
                    <div class="group">
                        <div
                            class="relative h-[200px] md:h-[242px] rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                            <img [src]="item.pathImage" [alt]="item.name"
                                class="object-cover transition-transform duration-700 group-hover:scale-105"
                                style="position: absolute; height: 100%; width: 100%; inset: 0px;">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent">
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-6">
                                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent mb-2 bg-primary-500 hover:bg-primary-600 text-white border-none text-xs"
                                    data-v0-t="badge">{{item.categoryName}}</div>
                                <h3
                                    class="text-lg font-bold mb-2 text-white group-hover:text-primary-200 transition-colors line-clamp-2">
                                    {{item.name}}
                                </h3>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-white/80 text-xs">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-calendar h-3 w-3 mr-1">
                                            <path d="M8 2v4"></path>
                                            <path d="M16 2v4"></path>
                                            <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                            <path d="M3 10h18"></path>
                                        </svg>
                                        <span>{{item.timeCreate }}</span>
                                    </div>
                                    <a [routerLink]="['/news', item.seoCategory, item.seoTitle]"
                                        class="text-white/90 hover:text-white text-sm font-medium transition-colors">Đọc
                                        tiếp</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    }
                    }
                </div>
            </div>

            <!-- Regular News -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @if(_isLoading) {
                @for (item of [1,2,3]; track $index) {
                <div class="group md:col-span-1">
                    <div
                        class="h-full rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100">
                        <div class="relative h-48 w-full overflow-hidden bg-gray-200 animate-pulse">
                            <div class="absolute top-4 left-4 h-6 w-20 bg-gray-300 rounded-full"></div>
                        </div>
                        <div class="p-6">
                            <div class="h-4 w-24 bg-gray-300 rounded mb-3"></div>
                            <div class="h-6 w-full bg-gray-300 rounded mb-3"></div>
                            <div class="h-4 w-full bg-gray-300 rounded mb-4"></div>
                        </div>
                    </div>
                </div>
                }
                } @else {
                @for (item of regularNews; track item.id) {
                <div class="group md:col-span-1">
                    <div
                        class="h-full rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100">
                        <div class="relative h-48 w-full overflow-hidden">
                            <img [src]="item.pathImage" [alt]="item.name"
                                class="object-cover transition-transform duration-700 group-hover:scale-105"
                                style="position: absolute; height: 100%; width: 100%; inset: 0px;">
                            <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 left-4 bg-primary-500 hover:bg-primary-600 text-white border-none"
                                data-v0-t="badge">{{item.categoryName}}</div>
                            <button
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground absolute top-4 right-4 bg-white/20 backdrop-blur-sm hover:bg-white/40 text-white rounded-full h-8 w-8">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-bookmark h-4 w-4">
                                    <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <div class="flex items-center mr-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-calendar h-4 w-4 mr-1 text-primary-500">
                                        <path d="M8 2v4"></path>
                                        <path d="M16 2v4"></path>
                                        <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                        <path d="M3 10h18"></path>
                                    </svg>
                                    <span>{{item.timeCreate }}</span>
                                </div>
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-eye h-4 w-4 mr-1 text-primary-500">
                                        <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                    <span>{{item.viewCount}}</span>
                                </div>
                            </div>
                            <h3
                                class="text-xl font-bold mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors">
                                {{item.name}}
                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-2">{{item.description}}</p>
                            <a [routerLink]="['/news',item.seoCategory,  item.seoTitle]"
                                class="inline-flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors group-hover:translate-x-1 duration-300">Đọc
                                tiếp<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-right h-4 w-4 ml-1">
                                    <path d="M5 12h14"></path>
                                    <path d="m12 5 7 7-7 7"></path>
                                </svg></a>
                        </div>
                    </div>
                </div>
                }
                }
            </div>

            <!-- No News Found -->
            @if(!_isLoading && (!regularNews || regularNews.length === 0)) {
            <div class="flex flex-col items-center justify-center py-12 text-center">
                <div class="w-24 h-24 mb-6 text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-full h-full">
                        <path
                            d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2">
                        </path>
                        <path d="M18 14h-8"></path>
                        <path d="M18 18h-8"></path>
                        <path d="M18 10h-8"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">Không tìm thấy tin tức</h3>
                <p class="text-gray-500 max-w-md mb-6">Hiện tại chưa có tin tức nào phù hợp với yêu cầu của bạn. Vui
                    lòng thử lại sau hoặc xem các tin tức khác.</p>
                <a routerLink="/news"
                    class="inline-flex items-center bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full transition-colors">
                    Xem tất cả tin tức
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-arrow-right h-4 w-4 ml-2">
                        <path d="M5 12h14"></path>
                        <path d="m12 5 7 7-7 7"></path>
                    </svg>
                </a>
            </div>
            }

            <div class="flex md:hidden justify-center mt-8">
                <a routerLink="/news"
                    class="flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors">
                    Xem tất cả tin tức
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-chevron-right h-4 w-4 ml-1">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </a>
            </div>

            <div class="absolute top-1/4 -right-16 w-32 h-32 bg-primary-100 rounded-full opacity-50 blur-2xl"></div>
            <div class="absolute bottom-1/4 -left-16 w-32 h-32 bg-blue-100 rounded-full opacity-50 blur-2xl"></div>
        </div>
    </div>
</section>