import { CommonModule } from '@angular/common';
import { Component, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgbDropdownModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { WorldService } from '../../../services/world/world.service';
import { RouterLink, Router } from '@angular/router';
import { formatCurrency, range, removeFormatCurrency } from '../../../common/function.common';
import { TicketIssueFeeService } from '../../../services/TicketIssueFee/ticket-issue-fee.service';


interface TicketIssueFee {
  Id: number | null;
  FeeType: string;
  SubContinentCode: string;
  FeeAmountAdult: string;
  FeeAmountChild: string;
  FeeAmountInfant: string;
  Currency: string;
  IsActive: boolean;
  Note: string;
}

@Component({
  selector: 'app-ticket-issue-fee',
  imports: [
    NgbDropdownModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,

  ],
  templateUrl: './ticket-issue-fee.component.html',
  styleUrl: './ticket-issue-fee.component.css'
})
export class TicketIssueFeeComponent {
  public _isEdit: boolean = false;
  private _ticketIssueFeeModel: TicketIssueFee = {
    Id: null,
    FeeType: '',
    SubContinentCode: '',
    FeeAmountAdult: '0',
    FeeAmountChild: '0',
    FeeAmountInfant: '0',
    Currency: 'VND',
    IsActive: true,
    Note: ''
  }
  private modalRef: any;
  private modalRefDelete: any;
  public _typeFilter: any[] = [
    {
      key: '',
      value: 'Tất cả',
      selected: true
    }, {
      key: 'Active',
      value: 'Đang hoạt động',
      selected: false
    }, {
      key: 'Inactive',
      value: 'Ngưng hoạt động',
      selected: false
    }, {
      key: 'Domestic',
      value: 'Trong nước',
      selected: false
    }, {
      key: 'International',
      value: 'Quốc tế',
      selected: false
    }, {
      key: 'Area',
      value: 'Theo khu vực',
      selected: false
    }

  ];
  public _ticketIssueFeeForm?: FormGroup;

  public _subContinentCode: any[] = [];
  searchModel: any = {
    Keyword: '',
    PageIndex: 1,
    PageSize: 10,
    SortColumn: '',
    SortOrder: 'asc',
    Filter: ''
  };
  public dataTable: any = {
    allRecords: 0,
    from: 0,
    to: 0,
    pageCount: 0,
    pageSize: 0,
    pageIndex: 0,
    totalRecords: 0
  };
  startIndex: number = 1;
  finishIndex: number = 1;
  isSubmit: boolean = false;


  constructor(
    private formBuilder: FormBuilder,
    private readonly _modalService: NgbModal,
    private readonly _worldService: WorldService,
    private readonly _ticketIssueFeeService: TicketIssueFeeService,
    private readonly router: Router
  ) {

  }

  ngOnInit(): void {
    // this.validator();
    this.loadSubContinentCode();
    this.getPaging();
  }

  loadSubContinentCode() {
    this._worldService.getSubContinents('vi').subscribe(res => {
      this._subContinentCode = res.resultObj;
    });
  }

  validator() {
    this._ticketIssueFeeForm = this.formBuilder.group({
      Id: [this._ticketIssueFeeModel.Id],
      FeeType: [this._ticketIssueFeeModel.FeeType, Validators.required],
      SubContinentCode: [this._ticketIssueFeeModel.SubContinentCode],
      FeeAmountAdult: [this.formatCurrency(this._ticketIssueFeeModel.FeeAmountAdult), Validators.required],
      FeeAmountChild: [this.formatCurrency(this._ticketIssueFeeModel.FeeAmountChild), Validators.required],
      FeeAmountInfant: [this.formatCurrency(this._ticketIssueFeeModel.FeeAmountInfant), Validators.required],
      Currency: [this._ticketIssueFeeModel.Currency, Validators.required],
      IsActive: [this._ticketIssueFeeModel.IsActive, Validators.required],
      Note: [this._ticketIssueFeeModel.Note, Validators.required]
    })
  }

  onChangePrice(e: Event) {
    let value = (e.target as HTMLInputElement).value;
    let currencyFormat = this.formatCurrency(value);
    (e.target as HTMLInputElement).value = currencyFormat;
  }
  formatCurrency(value: string): string {
    return formatCurrency(value);
  }

  openLg(content: TemplateRef<any>) {
    //mở modal và không đóng tự động
    this.modalRef = this._modalService.open(content, { size: 'minw', backdrop: 'static', keyboard: false });
  }

  onCloseModal() {
    this.modalRef.dismiss();
  }

  addNewTicketIssueFee(content: TemplateRef<any>) {
    this._isEdit = false;
    this._ticketIssueFeeModel = {
      Id: null,
      FeeType: '',
      SubContinentCode: '',
      FeeAmountAdult: '0',
      FeeAmountChild: '0',
      FeeAmountInfant: '0',
      Currency: 'VND',
      IsActive: true,
      Note: ''
    }
    this.validator();
    this.openLg(content);
  }

  editTicketIssueFee(item: any, content: TemplateRef<any>) {
    this._isEdit = true;
    this._ticketIssueFeeModel = {
      Id: item.order,
      FeeType: item.feeType,
      SubContinentCode: item.subContinentCode,
      FeeAmountAdult: item.feeAmountAdult,
      FeeAmountChild: item.feeAmountChild,
      FeeAmountInfant: item.feeAmountInfant,
      Currency: 'VND',
      IsActive: item.isActive,
      Note: item.notes
    }
    this.validator();
    this.openLg(content);
  }


  get fm(): any {
    return this._ticketIssueFeeForm?.controls;
  }

  get _selectedTypeFilter() {
    return this._typeFilter.find(type => type.selected)?.value || 'Tất cả';
  }

  async SetSelectedTypeFilter(value: string) {
    this._typeFilter.forEach(type => {
      type.selected = type.key === value;
    });

    this.searchModel.Filter = value;
    this.searchModel.PageIndex = 1;
    await this.getPaging();
  }

  cancelForm() {
    this.isSubmit = false;
    this._ticketIssueFeeForm?.reset();
    this.modalRef.dismiss();
  }

  submitForm() {
    this.isSubmit = true;
    if (this._ticketIssueFeeForm?.valid) {
      //get value from form and convert fee amount to number
      let data = this._ticketIssueFeeForm?.value;
      if (data.FeeType === 'Area' && !data.SubContinentCode) {
        return;
      }
      data.FeeAmountAdult = Number(removeFormatCurrency(data.FeeAmountAdult));
      data.FeeAmountChild = Number(removeFormatCurrency(data.FeeAmountChild));
      data.FeeAmountInfant = Number(removeFormatCurrency(data.FeeAmountInfant));

      //call api post ticket issue fee
      this._ticketIssueFeeService.postTicketIssueFee(data).subscribe((res: any) => {
        if (res.isSuccessed) {
          this.onCloseModal();
          this._ticketIssueFeeForm?.reset();
          this.getPaging();
        } else {
          alert(res.message);
        }
      });
      this.isSubmit = false;
    }
  }
  range(start: number, end: number): number[] {
    return range(start, end);
  }

  async sortTable(column: string) {
    if (this.searchModel.SortColumn === column) {
      this.searchModel.SortOrder = this.searchModel.SortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.searchModel.SortColumn = column;
      this.searchModel.SortOrder = 'asc';
    }
    await this.getPaging();
  }
  async getUrl(page: number) {
    if (page < 1 || page > this.dataTable.pageCount || page === this.searchModel.PageIndex) {
      return;
    }
    this.searchModel.PageIndex = page;
    await this.getPaging();
  }
  async onFormSearchSubmit(f: NgForm) {
    this.searchModel.Keyword = f.value.Keyword;
    this.searchModel.PageIndex = 1;
    await this.getPaging();
  }
  async getPaging(): Promise<void> {
    try {
      const res = await this._ticketIssueFeeService.paging(this.searchModel).toPromise();

      if (res.isSuccessed) {
        this.dataTable = res.resultObj;
        if (this.dataTable.pageCount <= 5) {
          this.startIndex = 1;
          this.finishIndex = this.dataTable.pageCount;
        } else if (this.dataTable.pageIndex <= 3) {
          this.startIndex = 1;
          this.finishIndex = 5;
        } else if (this.dataTable.pageIndex + 2 >= this.dataTable.pageCount) {
          this.startIndex = this.dataTable.pageCount - 4;
          this.finishIndex = this.dataTable.pageCount;
        } else {
          this.startIndex = this.dataTable.pageIndex - 2;
          this.finishIndex = this.dataTable.pageIndex + 2;
        }

      } else {
      }
    } catch (e) {
    }
  }

  async changePageSize(event: Event) {
    let value = (event.target as HTMLSelectElement).value;
    this.searchModel.PageSize = Number(value);
    this.searchModel.PageIndex = 1;
    await this.getPaging();
  }
  private _selectedItem: any;
  deleteTicketIssueFee(item: any, content: TemplateRef<any>) {
    this._selectedItem = item;
    this.modalRefDelete = this._modalService.open(content, { size: 'minw', backdrop: 'static', keyboard: false });
  }
  onCloseModalDelete() {
    this._selectedItem = null;
    this.modalRefDelete.dismiss();
  }

  onDeleteTicketIssueFee() {
    console.log(this._selectedItem);
    let data = {
      FeeType: this._selectedItem.feeType,
      SubContinentCode: this._selectedItem.subContinentCode
    }
    this._ticketIssueFeeService.deleteTicketIssueFee(data).subscribe((res: any) => {
      if (res.isSuccessed) {
        this.onCloseModalDelete();
        this.getPaging();
      } else {
        alert(res.message);
      }
    });
  }

  toggleActive(item: any) {
    let data = {
      Id: item.order,
      FeeType: item.feeType,
      SubContinentCode: item.subContinentCode,
      FeeAmountAdult: item.feeAmountAdult,
      FeeAmountChild: item.feeAmountChild,
      FeeAmountInfant: item.feeAmountInfant,
      Currency: 'VND',
      IsActive: !item.isActive,
      Note: item.notes
    }
    this._ticketIssueFeeService.postTicketIssueFee(data).subscribe((res: any) => {
      if (res.isSuccessed) {
        this.getPaging();
      } else {
        alert(res.message);
      }
    });
  }

  handleDoubleClick(item: any) {
    this.router.navigate(['/partner/flight', item.order]);
  }

}
