import { CommonModule, DOCUMENT } from '@angular/common';
import { AfterViewInit, Component, ElementRef, HostListener, Inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { LoaderService } from '../../../services/loader/loader.service';
import { AuthService } from '../../../services/auth/auth.service';
import { NgbAccordionModule, NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { ScreenService } from '../../../services/screen/screen.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { Subject, takeUntil } from 'rxjs';
import { Token } from '../../../enum/token';


@Component({
  selector: 'app-manager',
  imports: [
    RouterOutlet,
    RouterLink,
    RouterLinkActive,
    CommonModule,
    NgbAccordionModule,
    NgbDropdownModule
  ],
  templateUrl: './manager.component.html',
  styleUrl: './manager.component.css'
})
export class ManagerComponent implements AfterViewInit, OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  isShowMenu: boolean = true;
  profile: any | null = null;
  menu: any | null = null;


  @ViewChild('slidebar') slidebar: ElementRef | null = null;
  @ViewChild('containerHeader') containerHeader: ElementRef | null = null;
  @ViewChild('showMenuButton') showMenuButton: ElementRef | null = null;
  @ViewChild('containerParent') containerParent: ElementRef | null = null;

  private readonly THEME_KEY = 'color-theme';
  private readonly MENU_KEY = 'isShowMenu';

  constructor(
    @Inject(DOCUMENT) private document: Document,
    public loaderService: LoaderService,
    private readonly authService: AuthService,
    private router: Router,
    private sanitizer: DomSanitizer,
    private readonly screenService: ScreenService
  ) { }

  ngOnInit(): void {
    this.initializeTheme();
    this.loadMenu();
  }

  ngAfterViewInit(): void {
    this.loadIsShowMenuFromLocalStorage();
    this.showMenu();
    this.loadProfile();
    this.setupMenuButtonListener();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeTheme(): void {
    const isDarkMode = localStorage.getItem(this.THEME_KEY) === 'dark';

    this.document.documentElement.classList.toggle('dark', isDarkMode);
    this.updateThemeIcons(isDarkMode);
    this.setupThemeToggle();
  }

  private updateThemeIcons(isDarkMode: boolean): void {
    const themeToggleDarkIcon = this.document.getElementById('theme-toggle-dark-icon');
    const themeToggleLightIcon = this.document.getElementById('theme-toggle-light-icon');

    if (themeToggleDarkIcon && themeToggleLightIcon) {
      themeToggleLightIcon.classList.toggle('hidden', !isDarkMode);
      themeToggleDarkIcon.classList.toggle('hidden', isDarkMode);
    }
  }

  private setupThemeToggle(): void {
    const themeToggleBtn = this.document.getElementById('theme-toggle');
    if (!themeToggleBtn) return;

    themeToggleBtn.addEventListener('click', () => {
      const isDark = this.document.documentElement.classList.contains('dark');
      this.document.documentElement.classList.toggle('dark');
      localStorage.setItem(this.THEME_KEY, isDark ? 'light' : 'dark');
      this.updateThemeIcons(!isDark);
    });
  }

  private setupMenuButtonListener(): void {
    if (this.showMenuButton) {
      this.showMenuButton.nativeElement.addEventListener('click', () => {
        this.isShowMenu = !this.isShowMenu;
        this.saveIsShowMenuToLocalStorage();
        this.showMenu();
      });
    }
  }

  loadMenu(): void {
    this.screenService.getMenu()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.isSuccessed) {
            this.menu = res.resultObj;
          } else {
            console.error('Failed to load menu:', res.message);
          }
        },
        error: (error) => console.error('Error loading menu:', error)
      });
  }

  loadProfile(): void {
    this.authService.getProfile()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.isSuccessed) {
            this.profile = res.resultObj;
          } else {
            console.error('Failed to load profile:', res.message);
          }
        },
        error: (error) => console.error('Error loading profile:', error)
      });
  }

  sanitizeSVG(svg: string): SafeHtml {
    if (!svg?.includes('<svg')) return '';
    return this.sanitizer.bypassSecurityTrustHtml(svg);
  }

  private loadIsShowMenuFromLocalStorage(): void {
    const storedValue = localStorage.getItem(this.MENU_KEY);
    this.isShowMenu = storedValue ? JSON.parse(storedValue) : true;
  }

  private saveIsShowMenuToLocalStorage(): void {
    localStorage.setItem(this.MENU_KEY, JSON.stringify(this.isShowMenu));
  }

  async logOut(): Promise<void> {
    try {
      const res = await this.authService.logout().toPromise();
      if (res?.isSuccessed) {
        // Chỉ xóa token ở chế độ điện thoại
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        if (isMobile) {
          localStorage.removeItem(Token.ACCESS_TOKEN);
          localStorage.removeItem(Token.REFRESH_TOKEN);
          localStorage.removeItem(Token.CONNECTION_ID);
        }
        this.router.navigate(['/login']);
      } else {
        console.error('Logout failed:', res?.message);
      }
    } catch (error) {
      console.error('Error during logout:', error);
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const isClickInsideMenu = target.closest('#default-sidebar');
    const isShowMenuButton = target.closest('.showMenuButton');

    if (!isClickInsideMenu && !isShowMenuButton && window.innerWidth <= 768) {
      this.isShowMenu = false;
      this.saveIsShowMenuToLocalStorage();
      this.slidebar?.nativeElement.classList.add('-translate-x-full');
    }
  }

  showMenu(): void {
    if (window.innerWidth > 768) {
      this.handleDesktopMenu();
    } else {
      this.handleMobileMenu();
    }
  }

  private handleDesktopMenu(): void {
    if (!this.slidebar || !this.containerParent || !this.containerHeader || !this.showMenuButton) return;

    if (this.isShowMenu) {
      this.slidebar.nativeElement.classList.add('scrollable');
      this.containerParent.nativeElement.classList.add('scaleX');
      this.containerHeader.nativeElement.classList.add('scaleX');
    } else {
      this.slidebar.nativeElement.classList.remove('scrollable');
      this.containerHeader.nativeElement.classList.remove('scaleX');
      this.containerParent.nativeElement.classList.remove('scaleX');
    }
  }

  private handleMobileMenu(): void {
    this.slidebar?.nativeElement.classList.toggle('-translate-x-full');
    if (this.slidebar && this.containerParent && this.containerHeader) {
      this.containerHeader.nativeElement.classList.remove('scaleX');
      this.containerParent.nativeElement.classList.remove('scaleX');
    }
  }

  ShowMenuShow(): void {
    this.isShowMenu = false;
    this.saveIsShowMenuToLocalStorage();
    this.showMenu();
  }
}