import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { FeatureService } from '../../../../services/feature/feature.service';
import { formatCurrency } from '../../../../common/function.common';

@Component({
  selector: 'app-setting-default',
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './setting-default.component.html',
  styleUrl: './setting-default.component.css'
})

export class SettingDefaultComponent {
  colors = ['slate', 'gray', 'zinc', 'neutral', 'stone', 'red', 'orange', 'amber', 'yellow', 'lime', 'green', 'emerald', 'teal', 'cyan', 'sky', 'blue', 'indigo', 'violet', 'purple', 'fuchsia', 'pink', 'rose']

  libSettings = {
    color: 'orange',
    IsBoxVertical: false,
    hexColor: '#ffffff',
    selectedColorType: 'color_name'
  }

  constructor(
    private readonly featureService: FeatureService
  ) {
  }

  ngOnInit() {
    this.loadFeatures();
  }

  loadFeatures() {
    this.featureService.getPartnerFeatures().subscribe((res: any) => {
      if (res.isSuccessed) {
        if (res.resultObj.color.includes('#')) {
          this.libSettings.hexColor = res.resultObj.color;
          this.libSettings.selectedColorType = 'color_hex';
        }
        else {
          this.libSettings.color = this.colors.find(color => color === res.resultObj.color) || 'orange';
          this.libSettings.selectedColorType = 'color_name';
        }
        this.libSettings.IsBoxVertical = res.resultObj.IsBoxVertical === "True";
      }
    });
  }

  onChangePrice(e: Event) {
    let value = (e.target as HTMLInputElement).value;
    let currencyFormat = this.formatCurrency(value);
    (e.target as HTMLInputElement).value = currencyFormat;
  }

  formatCurrency(value: string): string {
    return formatCurrency(value);
  }

  trackByFn(index: number, color: string) {
    return color;
  }
  onColorChange(color: string) {
    this.libSettings.color = color;
  }

  saveSettings() {

    this.featureService.updatePartnerFeatures(this.libSettings).subscribe((res: any) => {
      if (res.isSuccessed) {
        alert('Settings saved successfully!');
      } else {
        alert('Error saving settings!');
      }
    })
  }
}
