import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject, HostListener } from '@angular/core';
import { FormsModule, NgForm, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { NgbCalendar, NgbDate, NgbDateParserFormatter, NgbDatepickerModule, NgbDropdownModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
// import { formatCurrency, formatDate, formatDateTo_ddMMyyyy, range } 

import { LoaderService } from '../../../../services/loader/loader.service';
import { range } from '../../../../common/function.common';
import { SearchModel, SearchStateService } from '../../../../services/search-state/search-state.service';
import { UserService } from '../../../../services/user/user.service';

interface FilterOption {
  key: string | number | null;
  value: string;
  selected: boolean;
}

interface DataTable {
  allRecords: number;
  from: number;
  to: number;
  pageCount: number;
  pageSize: number;
  pageIndex: number;
  totalRecords: number;
  items: any[];
}
@Component({
  selector: 'app-manager-users',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterLink,
    NgbDropdownModule,
    NgbDatepickerModule,
  ],
  templateUrl: './manager-users.component.html',
  styleUrl: './manager-users.component.css'
})

export class ManagerUsersComponent implements OnInit {
  // Services
  private readonly userService = inject(UserService);
  private readonly toastService = inject(ToastrService);
  private readonly router = inject(Router);
  private readonly searchStateService = inject(SearchStateService<SearchModel>);
  private readonly formBuilder = inject(FormBuilder);
  public readonly loaderService = inject(LoaderService);

  // Dialog state
  showEditDialog = false;
  showRoleDropdown = false;
  selectedUser: any = null;
  formSubmitted = false;
  editForm!: FormGroup;

  // Delete Dialog state
  showDeleteDialog = false;
  userToDelete: any = null;

  // Create Dialog state
  showCreateDialog = false;
  createForm!: FormGroup;

  // Search and pagination
  searchModel: any = {
    PageIndex: 1,
    PageSize: 10,
    SortColumn: '',
    SortOrder: 'asc',
    Type: '',
    Keyword: '',
    FilterStatus: null
  };

  dataTable: DataTable = {
    allRecords: 0,
    from: 0,
    to: 0,
    pageCount: 0,
    pageSize: 0,
    pageIndex: 0,
    totalRecords: 0,
    items: []
  };
  Roles: any[] = [
    { normalizedName: "Partner Admin", name: "Partner Admin" },
    { normalizedName: "Partner Staff", name: "Partner Staff" }
  ];
  startIndex = 1;
  finishIndex = 1;

  constructor() {
    this.initForms();
  }

  private initForms(): void {
    this.editForm = this.formBuilder.group({
      id: [null],
      fullName: ['', [Validators.required, Validators.maxLength(200)]],
      userName: ['', [Validators.required, Validators.maxLength(200)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(200)]],
      roles: [[], [Validators.required]],
      status: [1, [Validators.required]],
      avatar: [null]
    });

    this.createForm = this.formBuilder.group({
      fullName: ['', [Validators.required, Validators.maxLength(200)]],
      userName: ['', [Validators.required, Validators.maxLength(200)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(200)]],
      password: ['', [Validators.required, Validators.maxLength(200), this.StrongPasswordValidator()]],
      roles: [[], [Validators.required]],
      status: [1, [Validators.required]],
      avatar: [null]
    });
  }

  // Custom password validator
  StrongPasswordValidator() {
    return (control: { value: string; }) => {
      const value = control.value;

      if (!value) {
        return null; // return null if control is empty
      }

      const hasUpperCase = /[A-Z]+/.test(value);
      const hasLowerCase = /[a-z]+/.test(value);
      const hasNumeric = /[0-9]+/.test(value);
      const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(value);
      const isMinimumLength = value.length >= 6;

      const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar && isMinimumLength;

      return passwordValid ? null : {
        strongPassword: {
          hasUpperCase: hasUpperCase,
          hasLowerCase: hasLowerCase,
          hasNumeric: hasNumeric,
          hasSpecialChar: hasSpecialChar,
          isMinimumLength: isMinimumLength
        }
      };
    }
  }

  async ngOnInit(): Promise<void> {
    const savedState = this.searchStateService.getSearchState();
    if (savedState) {
      this.searchModel = savedState;
    }
    await this.getPaging();
  }


  // Pagination methods
  async changePageSize(event: Event): Promise<void> {
    const value = (event.target as HTMLSelectElement).value;
    this.searchModel.PageSize = Number(value);
    await this.getPaging();
  }

  async getUrl(page: number): Promise<void> {
    if (page < 1 || page > this.dataTable.pageCount || page === this.searchModel.PageIndex) {
      return;
    }
    this.searchModel.PageIndex = page;
    await this.getPaging();
  }

  // Data fetching methods
  async getPaging(): Promise<void> {
    try {
      const res = await this.userService.getpartnerPaging(this.searchModel).toPromise();
      if (res.isSuccessed) {
        this.dataTable = res.resultObj;
        this.updatePageIndices();
      }
    } catch (error) {
      this.toastService.error('Có lỗi xảy ra khi tải dữ liệu');
    }
  }


  // Search and filter methods
  async onSubmitSearch(): Promise<void> {
    this.searchModel.PageIndex = 1;
    this.searchStateService.setSearchState(this.searchModel);
    await this.getPaging();
  }

  private updatePageIndices(): void {
    if (this.dataTable.pageCount <= 5) {
      this.startIndex = 1;
      this.finishIndex = this.dataTable.pageCount;
    } else if (this.dataTable.pageIndex <= 3) {
      this.startIndex = 1;
      this.finishIndex = 5;
    } else if (this.dataTable.pageIndex + 2 >= this.dataTable.pageCount) {
      this.startIndex = this.dataTable.pageCount - 4;
      this.finishIndex = this.dataTable.pageCount;
    } else {
      this.startIndex = this.dataTable.pageIndex - 2;
      this.finishIndex = this.dataTable.pageIndex + 2;
    }
  }


  range(start: number, end: number): number[] {
    return range(start, end);
  }


  handleDoubleClick(item: any): void {
    this.searchStateService.setSearchState(this.searchModel);
    this.router.navigate(['/manager/news', item.id]);
  }

  async sortTable(column: string): Promise<void> {
    if (this.searchModel.SortColumn === column) {
      this.searchModel.SortOrder = this.searchModel.SortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.searchModel.SortColumn = column;
      this.searchModel.SortOrder = 'asc';
    }
    await this.getPaging();
  }

  // Dialog methods
  openEditDialog(user: any): void {
    this.selectedUser = user;
    this.editForm.patchValue({
      id: user.id,
      fullName: user.fullName,
      userName: user.userName,
      email: user.email,
      roles: [...user.roles],
      status: user.status == 2 ? 1 : user.status,
      avatar: null
    });
    this.showEditDialog = true;
  }

  closeEditDialog(): void {
    this.showEditDialog = false;
    this.showRoleDropdown = false;
    this.selectedUser = null;
    this.editForm.reset({
      status: 1,
      roles: []
    });
  }

  toggleRole(role: string): void {
    const currentRoles = this.editForm.get('roles')?.value || [];
    const index = currentRoles.indexOf(role);
    if (index === -1) {
      currentRoles.push(role);
    } else {
      currentRoles.splice(index, 1);
    }
    this.editForm.patchValue({ roles: currentRoles });
  }

  isRoleSelected(role: string): boolean {
    const currentRoles = this.editForm.get('roles')?.value || [];
    return currentRoles.includes(role);
  }

  async handleFileInput(event: Event): Promise<void> {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      this.editForm.patchValue({ avatar: input.files[0] });
    }
  }

  async submitEditForm(): Promise<void> {
    this.formSubmitted = true;
    if (this.editForm.invalid) {
      this.toastService.error('Vui lòng kiểm tra lại thông tin');
      return;
    }

    try {
      const formData = new FormData();
      const formValue = this.editForm.value;
      var { firstName, lastName } = this.getFirstAndLastName(formValue.fullName);

      formData.append('id', formValue.id);
      formData.append('firstName', firstName);
      formData.append('lastName', lastName);
      formData.append('userName', formValue.userName);
      formData.append('email', formValue.email);
      formValue.roles.forEach((role: string) => {
        formData.append('roles[]', role);
      });
      formData.append('status', formValue.status.toString());
      if (formValue.avatar) {
        formData.append('avatar', formValue.avatar);
      }

      const res = await this.userService.updatePartner(formData).toPromise();

      if (res && 'isSuccessed' in res && res.isSuccessed) {
        this.toastService.success('Cập nhật thông tin thành công');
        this.closeEditDialog();
        await this.getPaging();
      } else {
        const errorMessage = res && 'message' in res ? (res.message as string) : 'Có lỗi xảy ra khi cập nhật';
        this.toastService.error(errorMessage);
      }
    } catch (error) {
      console.error(error);
      this.toastService.error('Có lỗi xảy ra khi cập nhật thông tin');
    }
  }
  getFirstAndLastName(fullName: string): { firstName: string; lastName: string } {
    const nameParts = fullName.trim().split(" ");

    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    return { firstName, lastName };
  }
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.role-dropdown') && this.showRoleDropdown) {
      this.showRoleDropdown = false;
    }
  }

  openDeleteDialog(user: any): void {
    this.userToDelete = user;
    this.showDeleteDialog = true;
  }

  closeDeleteDialog(): void {
    this.showDeleteDialog = false;
    this.userToDelete = null;
  }

  async confirmDelete(): Promise<void> {
    if (this.userToDelete) {
      try {
        // Assuming you have a delete method in your UserService
        // You might need to create one if it doesn't exist
        const res = await this.userService.deletePartner(this.userToDelete.id).toPromise();

        if (res && 'isSuccessed' in res && res.isSuccessed) {
          this.toastService.success('Xóa tài khoản thành công');
          this.closeDeleteDialog();
          await this.getPaging(); // Refresh the list after deletion
        } else {
          const errorMessage = res && 'message' in res ? (res.message as string) : 'Có lỗi xảy ra khi xóa tài khoản';
          this.toastService.error(errorMessage);
        }
      } catch (error) {
        console.error(error);
        this.toastService.error('Có lỗi xảy ra khi xóa tài khoản');
      }
    }
  }

  openCreateDialog(): void {
    this.showCreateDialog = true;
    this.createForm.reset({
      status: 1,
      roles: []
    });
  }

  closeCreateDialog(): void {
    this.showCreateDialog = false;
    this.createForm.reset({
      status: 1,
      roles: []
    });
  }

  toggleRoleForCreate(role: string): void {
    const currentRoles = this.createForm.get('roles')?.value || [];
    const index = currentRoles.indexOf(role);
    if (index === -1) {
      currentRoles.push(role);
    } else {
      currentRoles.splice(index, 1);
    }
    this.createForm.patchValue({ roles: currentRoles });
  }

  isRoleSelectedForCreate(role: string): boolean {
    const currentRoles = this.createForm.get('roles')?.value || [];
    return currentRoles.includes(role);
  }

  async handleFileInputForCreate(event: Event): Promise<void> {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      this.createForm.patchValue({ avatar: input.files[0] });
    }
  }

  async submitCreateForm(): Promise<void> {
    this.formSubmitted = true;
    if (this.createForm.invalid) {
      this.toastService.error('Vui lòng kiểm tra lại thông tin');
      return;
    }

    try {
      const formData = new FormData();
      const formValue = this.createForm.value;

      formData.append('fullName', formValue.fullName);
      formData.append('userName', formValue.userName);
      formData.append('email', formValue.email);
      formData.append('password', formValue.password);
      formValue.roles.forEach((role: string) => {
        formData.append('roles[]', role);
      });
      formData.append('status', formValue.status.toString());
      if (formValue.avatar) {
        formData.append('avatar', formValue.avatar);
      }

      // Assuming you have a create method in your UserService that accepts FormData
      // (We will update the UserService next if needed)
      const res = await this.userService.createPartner(formData).toPromise();

      if (res && 'isSuccessed' in res && res.isSuccessed) {
        this.toastService.success('Tạo tài khoản thành công');
        this.closeCreateDialog();
        await this.getPaging(); // Refresh the list
      } else {
        const errorMessage = res && 'message' in res ? (res.message as string) : 'Có lỗi xảy ra khi tạo tài khoản';
        this.toastService.error(errorMessage);
      }
    } catch (error) {
      console.error(error);
      this.toastService.error('Có lỗi xảy ra khi tạo tài khoản');
    }
  }

}