import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Interface for loader state
 */
interface LoaderState {
  count: number;
  timestamp: number;
}

/**
 * Service to manage loading states across the application
 * Supports both global and key-based loading states
 */
@Injectable({
  providedIn: 'root'
})
export class LoaderService {
  private readonly LOADER_TIMEOUT = 30000; // 30 seconds timeout
  private readonly loaderMap: Map<string, LoaderState> = new Map();

  public readonly isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public readonly isAnyLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public readonly activeLoaders: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);

  constructor(@Inject(LOCALE_ID) public locale: string) {
    // Cleanup stale loaders once during service initialization
    this.cleanupStaleLoaders();
  }

  /**
   * Shows the global loader
   */
  show(): void {
    this.isLoading.next(true);
    this.showByKey('global');
  }

  /**
   * Hides the global loader
   */
  hide(): void {
    this.isLoading.next(false);
    this.hideByKey('global');
  }

  /**
   * Shows loader for a specific key
   * @param key - Unique identifier for the loader
   */
  showByKey(key: string): void {
    if (!key) {
      console.warn('LoaderService: Attempted to show loader with empty key');
      return;
    }

    const currentState = this.loaderMap.get(key) || { count: 0, timestamp: Date.now() };
    this.loaderMap.set(key, {
      count: currentState.count + 1,
      timestamp: Date.now()
    });
    this.updateLoadingState();
  }

  /**
   * Hides loader for a specific key
   * @param key - Unique identifier for the loader
   */
  hideByKey(key: string): void {
    if (!key) {
      console.warn('LoaderService: Attempted to hide loader with empty key');
      return;
    }

    const currentState = this.loaderMap.get(key);
    if (!currentState) {
      console.warn(`LoaderService: Attempted to hide non-existent loader for key: ${key}`);
      return;
    }

    if (currentState.count > 1) {
      this.loaderMap.set(key, {
        count: currentState.count - 1,
        timestamp: currentState.timestamp
      });
    } else {
      this.loaderMap.delete(key);
    }
    this.updateLoadingState();
  }

  /**
   * Checks if a specific loader is active
   * @param key - Unique identifier for the loader
   * @returns boolean indicating if the loader is active
   */
  isLoadingByKey(key: string): boolean {
    return (this.loaderMap.get(key)?.count || 0) > 0;
  }

  /**
   * Gets the count of active loaders for a specific key
   * @param key - Unique identifier for the loader
   * @returns number of active loaders
   */
  getLoaderCount(key: string): number {
    return this.loaderMap.get(key)?.count || 0;
  }

  /**
   * Gets an observable of active loader keys
   * @returns Observable of active loader keys
   */
  getActiveLoaders(): Observable<string[]> {
    return this.activeLoaders.asObservable();
  }

  /**
   * Resets all loaders
   */
  resetAll(): void {
    this.loaderMap.clear();
    this.updateLoadingState();
  }

  /**
   * Updates the loading state and active loaders
   */
  private updateLoadingState(): void {
    const activeKeys = Array.from(this.loaderMap.keys());
    this.isAnyLoading.next(activeKeys.length > 0);
    this.activeLoaders.next(activeKeys);
  }

  /**
   * Cleans up stale loaders that have been active for too long
   */
  private cleanupStaleLoaders(): void {
    const now = Date.now();
    for (const [key, state] of this.loaderMap.entries()) {
      if (now - state.timestamp > this.LOADER_TIMEOUT) {
        console.warn(`LoaderService: Cleaning up stale loader for key: ${key}`);
        this.loaderMap.delete(key);
      }
    }
    this.updateLoadingState();
  }
}