import { isPlatformBrowser } from '@angular/common';
import { Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';

@Component({
  selector: 'app-testimonials',
  templateUrl: './testimonials.component.html',
  styleUrls: ['./testimonials.component.css']
})
export class TestimonialsComponent implements OnInit {
  currentSlide = 0;
  totalSlides = 2; // Since we show 2 cards at a time
  isFirstSlide = true;
  isLastSlide = false;


  constructor(@Inject(PLATFORM_ID) private platformId: Object) { }

  ngOnInit() {
    // Initialize the slider
    this.updateSliderPosition();
    this.updateSlideState();
  }

  slideTestimonials(direction: 'prev' | 'next') {
    if (direction === 'next') {
      this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
    } else {
      this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
    }
    this.updateSliderPosition();
    this.updateSlideState();
  }

  private updateSliderPosition() {
    if (isPlatformBrowser(this.platformId)) {
      const slider = document.getElementById('testimonialSlider');
      if (slider) {
        slider.style.transform = `translateX(-${this.currentSlide * 100}%)`;
      }
    }
  }

  private updateSlideState() {
    this.isFirstSlide = this.currentSlide === 0;
    this.isLastSlide = this.currentSlide === this.totalSlides - 1;
  }
}
