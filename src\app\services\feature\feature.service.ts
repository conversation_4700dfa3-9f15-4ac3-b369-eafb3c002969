import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { removeFormatCurrency } from '../../common/function.common';

const apiUrl = environment.apiUrl + '/api/Features/';

@Injectable({
  providedIn: 'root'
})

export class FeatureService {

  constructor(
    private readonly http: HttpClient
  ) { }

  getPartnerFeatures() {
    return this.http.get(`${apiUrl}partner`);
  }
  getPartnerFeaturesWithId(id: string) {
    return this.http.get(`${apiUrl}partner/${id}`);
  }

  updatePartnerFeatures(data: any, idkey?: string) {
    const updatedData = { ...data };

    // Chuyển đổi các giá trị boolean sang chuỗi
    if (idkey) {
      updatedData.HoldTrip = updatedData.HoldTrip.toString();
      updatedData.SendEmail = updatedData.SendEmail.toString();
    }

    updatedData.IsBoxVertical = updatedData.IsBoxVertical.toString();
    updatedData.color = updatedData.selectedColorType == 'color_name' ? updatedData.color : updatedData.hexColor;
    updatedData.ticketIssuanceFee = removeFormatCurrency(updatedData.ticketIssuanceFee).toString();

    //remove hexColor
    delete updatedData.selectedColorType;
    delete updatedData.hexColor;

    if (idkey) {
      return this.http.put(`${apiUrl}partner/${idkey}`, updatedData);
    } else {
      return this.http.put(`${apiUrl}partner`, updatedData);
    }
  }

  PartnerUpdatePaymentInfo(data: any, typepayment: string) {
    const updatedData = {
      TypePayment: typepayment,
      Value: JSON.stringify(data)
    };

    return this.http.put(`${apiUrl}partner/payment`, updatedData);
  }

  getPartnerPaymentInfo(typepayment: string) {
    return this.http.get(`${apiUrl}partner/payment/${typepayment}`);
  }
}
