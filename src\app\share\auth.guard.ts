import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../service/auth/auth.service';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);


  authService.getProfile().subscribe((res) => {
    if (res.isSuccessed) {
      const isPartner = res.resultObj.isPartner;
      const currentUrl = state.url;

      if (isPartner && currentUrl.startsWith('/manager')) {
        router.navigate(['/partner']); // Chuyển hướng đến trang "partner"
        return false;
      } else if (!isPartner && currentUrl.startsWith('/partner')) {
        router.navigate(['/manager']); // Chuyển hướng đến trang "manager"
        return false;
      } else {
        return true;
      }
    } else {
      router.navigate(['/login']); // Chuyển hướng đến trang "không có quyền"
      return false;
    }
  });
  return true;
};





