import { Component, inject, TemplateRef, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AuthService } from '../../../services/auth/auth.service';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit {
  private readonly modalService = inject(NgbModal);
  private readonly authService = inject(AuthService);
  private readonly fb = inject(FormBuilder);
  private readonly toastr = inject(ToastrService);

  private modalRef: any;
  profileData: any = null;
  loading: boolean = true;
  passwordForm: FormGroup;
  showCurrentPassword: boolean = false;
  showNewPassword: boolean = false;
  showConfirmPassword: boolean = false;
  submitting: boolean = false;

  constructor() {
    this.passwordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [
        Validators.required,
        Validators.minLength(6),
        Validators.pattern(/^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{6,}$/)
      ]],
      confirmPassword: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  ngOnInit() {
    this.loadProfile();
  }

  loadProfile() {
    this.loading = true;
    this.authService.getProfileAdvance().subscribe({
      next: (response) => {
        if (response.isSuccessed) {
          this.profileData = response.resultObj;
          console.log(this.profileData);
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading profile:', error);
        this.loading = false;
      }
    });
  }

  openLg(content: TemplateRef<any>) {
    this.modalRef = this.modalService.open(content, { size: 'minw' });
    this.passwordForm.reset();
    this.showCurrentPassword = false;
    this.showNewPassword = false;
    this.showConfirmPassword = false;
  }

  togglePasswordVisibility(field: 'currentPassword' | 'newPassword' | 'confirmPassword') {
    switch (field) {
      case 'currentPassword':
        this.showCurrentPassword = !this.showCurrentPassword;
        break;
      case 'newPassword':
        this.showNewPassword = !this.showNewPassword;
        break;
      case 'confirmPassword':
        this.showConfirmPassword = !this.showConfirmPassword;
        break;
    }
  }

  passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;

    if (newPassword !== confirmPassword) {
      form.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    return null;
  }

  getErrorMessage(controlName: string): string {
    const control = this.passwordForm.get(controlName);
    if (!control) return '';

    if (control.hasError('required')) {
      return 'Vui lòng nhập trường này';
    }

    if (controlName === 'newPassword') {
      if (control.hasError('minlength')) {
        return 'Mật khẩu phải có ít nhất 6 ký tự';
      }
      if (control.hasError('pattern')) {
        return 'Mật khẩu phải chứa chữ, số và ký tự đặc biệt';
      }
    }

    if (controlName === 'confirmPassword' && control.hasError('passwordMismatch')) {
      return 'Mật khẩu xác nhận không khớp';
    }

    return '';
  }

  onSubmit() {
    if (this.passwordForm.invalid) {
      Object.keys(this.passwordForm.controls).forEach(key => {
        const control = this.passwordForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
      return;
    }

    this.submitting = true;

    this.authService.changePassword(this.passwordForm.value).subscribe({
      next: (response) => {
        if (response.isSuccessed) {
          this.toastr.success('Đổi mật khẩu thành công');
          this.modalRef.close();
          this.passwordForm.reset();
        } else {
          this.toastr.error(response.message || 'Đổi mật khẩu thất bại');
        }
        this.submitting = false;
      },
      error: (error) => {
        console.error('Error changing password:', error);
        this.toastr.error('Đổi mật khẩu thất bại');
        this.submitting = false;
      }
    });
  }
}
