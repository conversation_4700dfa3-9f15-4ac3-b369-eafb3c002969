.footer {
    @apply text-white relative w-full antialiased;
}


.footer-top {
    @apply py-20 pb-10 relative z-10;
}

.footer-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 w-full max-w-7xl mx-auto px-5;
}

.footer-info h3 {
    @apply text-3xl mb-5 font-bold bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
}

.footer-info p {
    @apply text-gray-400 leading-relaxed mb-6 text-sm;
}

.social-links {
    @apply flex gap-4 flex-wrap;
}

.social-link {
    @apply flex items-center justify-center w-10 h-10 rounded-full bg-white/10 text-white transition-all duration-300 ease-in-out hover:bg-primary-400 hover:-translate-y-1 hover:shadow-lg no-underline;
}

.footer-links h4,
.footer-contact h4 {
    @apply text-white text-xl mb-6 relative pb-2.5 font-semibold;
}

.footer-links h4::after,
.footer-contact h4::after {
    @apply content-[''] absolute left-0 bottom-0 w-12 h-0.5 bg-primary-400 transition-all duration-300 ease-in-out;
}

.footer-links h4:hover::after,
.footer-contact h4:hover::after {
    @apply w-16;
}

.footer-links ul {
    @apply list-none p-0 m-0 max-md:text-center;
}

.footer-links ul li {
    @apply mb-4;
}

.footer-links ul li a {
    @apply text-gray-400 no-underline transition-all duration-300 ease-in-out relative hover:text-primary-400 hover:translate-x-1;
}

.contact-info p {
    @apply text-gray-400 mb-4 flex items-center gap-2.5 text-sm max-md:justify-center;
}

.contact-info i {
    @apply text-primary-400 text-lg;
}

.newsletter {
    @apply mt-8;
}

.newsletter-form {
    @apply flex gap-2.5 flex-row w-full;
}

.newsletter-form input {
    @apply px-4 py-3 border border-white/10 rounded bg-white/10 text-white transition-all duration-300 ease-in-out focus:outline-none focus:border-primary-400 focus:bg-white/15;
}

.newsletter-form input::placeholder {
    @apply text-gray-400;
}

.newsletter-form button {
    @apply px-5 py-3 border-none rounded bg-primary-400 text-white cursor-pointer transition-all duration-300 ease-in-out font-medium whitespace-nowrap hover:bg-primary-400/90 hover:-translate-y-0.5 hover:shadow-lg;
}

.footer-bottom {
    @apply py-5 border-t border-white/10 mt-10;
}

.footer-bottom-content {
    @apply flex justify-between items-center max-w-7xl mx-auto px-5;
}

.footer-bottom p {
    @apply text-gray-400 text-sm;
}

.payment-methods {
    @apply flex gap-4 flex-wrap;
}

.payment-methods i {
    @apply text-3xl text-gray-400 transition-all duration-300 ease-in-out cursor-pointer hover:text-primary-400 hover:scale-110;
}

@media (max-width: 768px) {
    .footer-top {
        @apply py-16 pb-8;
    }

    .footer-grid {
        @apply grid-cols-1 gap-8;
    }

    .footer-bottom-content {
        @apply flex-col gap-5 text-center;
    }

    .newsletter-form {
        @apply flex-row;
    }

    .newsletter-form input {
        @apply w-full;
    }

    .social-links {
        @apply justify-center;
    }

    .footer-links h4,
    .footer-contact h4 {
        @apply text-center;
    }

    .footer-links h4::after,
    .footer-contact h4::after {
        @apply left-1/2 -translate-x-1/2;
    }
}

@media (max-width: 480px) {
    .footer-top {
        @apply py-10 pb-5;
    }

    .footer-info h3 {
        @apply text-3xl text-center;
    }

    .footer-info p {
        @apply text-center;
    }

    .payment-methods {
        @apply justify-center;
    }
}