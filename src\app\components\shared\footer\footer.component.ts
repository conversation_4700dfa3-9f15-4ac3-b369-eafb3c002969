import { Component, Inject, PLATFORM_ID } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.css'
})
export class FooterComponent {
  email: string = '';
  isSubscribed: boolean = false;
  errorMessage: string = '';
  isBrowser: boolean;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  ngAfterViewInit() {
  if (this.isBrowser) {
    // Reinitialize Facebook widgets after view init
    setTimeout(() => {
      if ((window as any).FB) {
        (window as any).FB.XFBML.parse();
      }
    }, 1000);
  }
}

  onSubmit() {
    if (!this.email) {
      this.errorMessage = 'Vui lòng nhập email của bạn';
      return;
    }

    if (!this.validateEmail(this.email)) {
      this.errorMessage = 'Email không hợp lệ';
      return;
    }

    // Here you would typically make an API call to your backend
    // For now, we'll just simulate a successful subscription
    this.isSubscribed = true;
    this.errorMessage = '';
    this.email = '';

    // Reset subscription status after 3 seconds
    setTimeout(() => {
      this.isSubscribed = false;
    }, 3000);
  }

  private validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
