<div class="container relative w-full md:p-4 p-2 shadow-lg bg-white dark:bg-gray-900 rounded-lg h-full min-w-full">

    <div class="w-full h-full relative flex flex-col pb-4  sm:rounded-lg  md:gap-6 gap-2">
        <div class="w-full shadow rounded-xl flex flex-col p-2 md:gap-4 gap-2">
            <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div class="flex items-center">
                    <a routerLink="/manager/news/category"
                        class="flex items-center gap-2 text-lg font-semibold md:text-xl">
                        <span>Quản lý danh mục</span>
                    </a>
                    <nav class="ml-8 flex items-center gap-6 text-gray-400">
                        <a routerLink="/manager/news" routerLinkActive="!text-gray-800"
                            [routerLinkActiveOptions]="{exact: true}" class="text-sm hover:text-gray-600">Tin tức</a>
                        <a routerLink="/manager/news/category" routerLinkActive="!text-gray-800"
                            [routerLinkActiveOptions]="{exact: true}" class="text-sm  hover:text-gray-600">Danh
                            mục</a>
                    </nav>
                </div>
                <button
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary-400 text-white hover:bg-primary/90 h-10 px-4 py-2"
                    (click)="openAddCategoryModal()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-circle-plus mr-2 h-4 w-4">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M8 12h8"></path>
                        <path d="M12 8v8"></path>
                    </svg>Thêm danh mục mới
                </button>
            </div>
            <div class="flex flex-row flex-wrap justify-between md:gap-6 gap-2 relative">
                <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between w-full">
                    <div class="flex w-full max-w-sm items-center space-x-2">
                        <input
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder: focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            placeholder="Tìm kiếm tin tức..." type="search" [(ngModel)]="searchModel.Keyword"
                            (keyup.enter)="onSubmitSearch()">
                        <button
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input  hover:bg-primary-400 hover:text-white h-10 w-10"
                            type="button" (click)="onSubmitSearch()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-search h-4 w-4">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.3-4.3"></path>
                            </svg>
                            <span class="sr-only">Tìm kiếm</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="w-full h-full overflow-auto">
            <table
                class="w-full h-fill-available text-sm text-left sm:mb-28 max-sm:mb-32 rtl:text-right text-gray-500 dark:text-white">
                <thead
                    class="sticky -top-1 text-xs text-white uppercase bg-gradient-to-r from-[#fb6340] via-[#fb6340] to-[#fbb140] dark:bg-gray-700  dark:bg-none">
                    <tr class="border-b transition-colors hover:bg-muted/50">

                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                ID
                                <a (click)="sortTable('Index')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Tên danh mục
                                <a (click)="sortTable('Name')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                SeoTitle
                                <a (click)="sortTable('SeoTitle')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Mô tả
                                <a (click)="sortTable('Description')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Số bài viết
                                <a (click)="sortTable('TotalNews')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Ngày tạo
                                <a (click)="sortTable('TimeCreate')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Trạng thái
                                <a (click)="sortTable('Status')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Thao tác
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if(dataTable.items.length > 0){
                    @for(item of dataTable.items || []; track $index ){
                    <tr (dblclick)="handleDoubleClick(item)"
                        [ngClass]="{'bg-red-200 dark:bg-red-600': item.isActive === false, 'odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800': item.isActive}"
                        class="cursor-pointer border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">

                        <td class="p-2 font-medium text-gray-800 text-left">
                            {{item.index}}
                        </td>
                        <td class="p-2 font-medium text-gray-800">
                            {{item.name}}
                        </td>
                        <td class="p-2 font-medium text-gray-800">
                            {{item.seoTitle}}
                        </td>
                        <td class="p-2 text-left">
                            {{item.description}}
                        </td>
                        <td class="p-2 text-center">
                            {{item.totalNews}}
                        </td>
                        <td class="p-2 text-center text-nowrap">
                            <div class="text-sm text-right">
                                <div class="text-nowrap">{{item?.dateCreated}}</div>
                                <div class="text-nowrap text-xs text-gray-400">{{item?.userCreate}}</div>
                            </div>
                        </td>
                        <td class="p-2 text-center">
                            <span [class]="
                            'px-2 py-1 border rounded-full  ' +
                            (item.active  ? 'bg-green-200 text-green-500 border-green-500' : '') +
                            (!item.active  ? 'bg-red-200 text-red-500 border-red-500' : '') 
                          ">
                                <span class="text-nowrap" *ngIf="item.active">
                                    Hoạt động
                                </span>
                                <span class="text-nowrap" *ngIf="!item.active">
                                    Ngừng hoạt động
                                </span>
                            </span>
                        </td>
                        <td class="p-4 align-middle  text-right">
                            <div class="flex justify-end gap-2">
                                <button
                                    class="inline-flex items-center hover:bg-gray-300 hover:text-white justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground h-10 w-10"
                                    type="button" (click)="openEditCategoryModal(item)">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-square-pen h-4 w-4">
                                        <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                                    </svg>
                                    <span class="sr-only">Chỉnh sửa</span>
                                </button>

                            </div>
                        </td>
                    </tr>
                    }
                    }@else {
                    <tr
                        class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800 border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">
                        <td class="px-6 py-4" colspan="8">
                            <div class="flex items-center justify-center">
                                <span class="text-gray-500 dark:text-gray-400">
                                    Không có dữ liệu
                                </span>
                            </div>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>

    </div>
    <nav class="pagination absolute bg-white sm:rounded-lg dark:bg-gray-600 w-full bottom-0 flex items-center flex-column flex-wrap md:flex-row justify-between p-4"
        aria-label="Table navigation">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Showing
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable.from}} - {{dataTable.to}}</span> of
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable.totalRecords}}</span></span>
        <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(1)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m17 16-4-4 4-4m-6 8-4-4 4-4" />
                    </svg>
                </a>
            </li>
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(dataTable.pageIndex - 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m14 8-4 4 4 4" />
                    </svg>
                </a>
            </li>

            <li *ngFor="let page of range(startIndex, finishIndex); let i = index"
                [class.active]="page === dataTable.pageIndex">
                <a class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                    (click)="getUrl(page)" [title]="'Page ' + page">
                    {{ page }}
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageIndex + 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m10 16 4-4-4-4" />
                    </svg>
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageCount)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m7 16 4-4-4-4m6 8 4-4-4-4" />
                    </svg>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Modal Thêm danh mục mới -->
    <div *ngIf="showAddCategoryModal"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
        <div role="dialog"
            class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg sm:max-w-[500px]"
            tabindex="-1" style="pointer-events: auto;">
            <form [formGroup]="categoryForm" (ngSubmit)="submitCategory()">
                <div class="flex flex-col space-y-1.5 text-center sm:text-left">
                    <h2 class="text-lg font-semibold leading-none tracking-tight">{{isEditMode ? 'Cập nhật danh mục' :
                        'Thêm danh mục mới'}}</h2>
                    <p class="text-sm text-gray-400">Nhập thông tin để {{isEditMode ? 'cập nhật' : 'tạo'}} danh mục cho
                        tin tức</p>
                </div>
                <div class="grid gap-4 py-4">
                    <div class="grid grid-cols-4 items-center gap-4">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right md:text-nowrap"
                            for="name">Tên danh mục</label>
                        <div class="col-span-3">
                            <input formControlName="name"
                                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                [ngClass]="{'border-red-500': categoryForm.get('name')?.invalid && categoryForm.get('name')?.touched}">
                            <div *ngIf="categoryForm.get('name')?.invalid && categoryForm.get('name')?.touched"
                                class="text-red-500 text-xs mt-1">
                                <span *ngIf="categoryForm.get('name')?.errors?.['required']">Tên danh mục là bắt
                                    buộc</span>
                                <span *ngIf="categoryForm.get('name')?.errors?.['minlength']">Tên danh mục phải có ít
                                    nhất 3 ký tự</span>
                                <span *ngIf="categoryForm.get('name')?.errors?.['maxlength']">Tên danh mục không được
                                    vượt quá 100 ký tự</span>
                            </div>
                        </div>
                    </div>
                    <div class=" grid-cols-4 items-center gap-4 hidden">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                            for="seoTitle">SEO Title</label>
                        <div class="col-span-3">
                            <input formControlName="seoTitle"
                                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                [ngClass]="{'border-red-500': categoryForm.get('seoTitle')?.invalid && categoryForm.get('seoTitle')?.touched}">
                            <div *ngIf="categoryForm.get('seoTitle')?.invalid && categoryForm.get('seoTitle')?.touched"
                                class="text-red-500 text-xs mt-1">
                                <span *ngIf="categoryForm.get('seoTitle')?.errors?.['required']">SEO Title là bắt
                                    buộc</span>
                                <span *ngIf="categoryForm.get('seoTitle')?.errors?.['minlength']">SEO Title phải có ít
                                    nhất 3 ký tự</span>
                                <span *ngIf="categoryForm.get('seoTitle')?.errors?.['maxlength']">SEO Title không được
                                    vượt quá 100 ký tự</span>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-4 items-center gap-4">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                            for="description">Mô tả</label>
                        <div class="col-span-3">
                            <textarea formControlName="description"
                                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                [ngClass]="{'border-red-500': categoryForm.get('description')?.invalid && categoryForm.get('description')?.touched}"
                                rows="3"></textarea>
                            <div *ngIf="categoryForm.get('description')?.invalid && categoryForm.get('description')?.touched"
                                class="text-red-500 text-xs mt-1">
                                <span *ngIf="categoryForm.get('description')?.errors?.['maxlength']">Mô tả không được
                                    vượt quá 500 ký tự</span>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-4 items-center gap-4">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                            for="sortOrder">Thứ tự</label>
                        <div class="col-span-3">
                            <input type="number" inputmode="numeric" formControlName="sortOrder"
                                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                [ngClass]="{'border-red-500': categoryForm.get('sortOrder')?.invalid && categoryForm.get('sortOrder')?.touched}">
                            <div *ngIf="categoryForm.get('sortOrder')?.invalid && categoryForm.get('sortOrder')?.touched"
                                class="text-red-500 text-xs mt-1">
                                <span *ngIf="categoryForm.get('sortOrder')?.errors?.['required']">Thứ tự là bắt
                                    buộc</span>
                                <span *ngIf="categoryForm.get('sortOrder')?.errors?.['min']">Thứ tự phải lớn hơn
                                    0</span>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-4 items-center gap-4">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                            for="status">Hoạt động</label>
                        <div class="col-span-3">
                            <label class="inline-flex items-center me-5 cursor-pointer">
                                <input type="checkbox" formControlName="status" class="sr-only peer">
                                <div
                                    class="relative w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-500">
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
                    <button type="button" (click)="closeAddCategoryModal()"
                        class="inline-flex hover:bg-gray-50 items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                        Hủy
                    </button>
                    <button type="submit" [disabled]="categoryForm.invalid"
                        class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary-400 text-white hover:bg-primary/90 h-10 px-4 py-2">
                        {{isEditMode ? 'Cập nhật' : 'Thêm danh mục'}}
                    </button>
                </div>
            </form>
            <button type="button" (click)="closeAddCategoryModal()"
                class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x h-4 w-4">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
                <span class="sr-only">Close</span>
            </button>
        </div>
    </div>

</div>