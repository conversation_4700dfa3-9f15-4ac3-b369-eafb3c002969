{"name": "hvn_Indeedtravel", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:hvn_Indeedtravel": "node dist/hvn_Indeedtravel/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.2.11", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.11", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.3", "@ckeditor/ckeditor5-angular": "^7.0.1", "@ckeditor/ckeditor5-build-classic": "^41.4.2", "@fingerprintjs/fingerprintjs": "^4.6.2", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@ng-select/ng-select": "^14.9.0", "@tinymce/tinymce-angular": "^8.0.1", "@txtextcontrol/tx-ng-document-editor": "^33.0.0", "@types/crypto-js": "^4.2.2", "apexcharts": "^4.7.0", "bootstrap": "^5.3.6", "crypto-js": "^4.2.0", "express": "^4.18.2", "ng-apexcharts": "^1.15.0", "ngx-cookie-service": "^19.1.2", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "tinymce": "^7.9.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.3", "@angular/cli": "^19.2.3", "@angular/compiler-cli": "^19.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "tailwindcss": "^3.4.17", "typescript": "~5.7.2"}}