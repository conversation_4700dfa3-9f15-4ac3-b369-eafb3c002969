import { HttpEvent, HttpHandlerFn, HttpHeaders, HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { AuthService } from '../../services/auth/auth.service';
import { LoaderService } from '../../services/loader/loader.service';
import { inject } from '@angular/core';
import { BehaviorSubject, catchError, filter, finalize, Observable, switchMap, take, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { Token } from '../../enum/token';

let isRefreshing = false;
let refreshTokenSubject: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null); // Manage refresh state

// Create the token interceptor function
export const tokenInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const loadService = inject(LoaderService);
  const router = inject(Router);

  loadService.isLoading.next(true);

  // Kiểm tra nếu là thiết bị di động
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  // Add withCredentials to the request
  let clonedRequest = req.clone({
    withCredentials: true
  });

  // Nếu là thiết bị di động, thêm token từ localStorage vào header
  if (isMobile) {
    const token = localStorage.getItem(Token.ACCESS_TOKEN);
    if (token) {
      clonedRequest = clonedRequest.clone({
        headers: clonedRequest.headers.set('Authorization', `Bearer ${token}`)
      });
    }
  }

  // Add the token to the request headers if available
  // const token = authService.getCookie(Token.ACCESS_TOKEN);
  // console.log('token', token);
  // const clonedRequest = token ? addTokenHeader(req, token) : req;

  return next(clonedRequest).pipe(
    catchError((error) => {
      // If there's a 401 Unauthorized error, handle token refresh
      if (error.status === 401 && router.url.includes("/manager/")) {
        return handle401Error(clonedRequest, next, authService, router);
      } else if (error.status === 500) {
        console.log('500', router.url);
        if (router.url.includes("/manager/") || router.url.includes("/partner/")) {
          // return next(clonedRequest); // Thực hiện gọi lại request
          return handle401Error(clonedRequest, next, authService, router);
        }
      }
      return throwError(error); // If it's not 401, propagate the error
    }),
    finalize(() => {
      loadService.isLoading.next(false);
    })
  );
};

// Helper function to add token to headers
function addTokenHeader(req: HttpRequest<any>, token: string): HttpRequest<any> {
  return req.clone({
    // headers: req.headers.set('Authorization', `Bearer ${token}`),
    withCredentials: true,
    setHeaders: {
      // Authorization: `Bearer ${token}`,
      'X-Request-Mode': 'no-cors' // Thêm header tùy chỉnh (chỉ để thử nghiệm)
    }
  });
}

function handle401Error(request: HttpRequest<any>, next: HttpHandlerFn, authService: AuthService, router: Router): Observable<HttpEvent<any>> {
  if (!isRefreshing) {
    isRefreshing = true;
    refreshTokenSubject.next(null);

    // Kiểm tra nếu là thiết bị di động
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    // Chuẩn bị headers cho request refresh token
    let headers = new HttpHeaders();

    if (isMobile) {
      const refreshToken = localStorage.getItem(Token.REFRESH_TOKEN);
      const conectionID = localStorage.getItem(Token.CONNECTION_ID);
      if (refreshToken && conectionID) {
        headers = headers.set('X-Refresh-Token', refreshToken);
        headers = headers.set('X-Connection-Id', conectionID);
      }
    }

    // Gọi API refresh token với headers phù hợp
    return authService.refreshToken().pipe(
      switchMap((res: any) => {
        isRefreshing = false;

        if (res.isSuccessed) {
          // Lưu token mới nếu là thiết bị di động
          if (isMobile && res.resultObj) {
            localStorage.setItem(Token.ACCESS_TOKEN, res.resultObj.access_token);
            localStorage.setItem(Token.REFRESH_TOKEN, res.resultObj.refresh_token);
            localStorage.setItem(Token.CONNECTION_ID, res.resultObj.connection_id);

            // Thêm token mới vào header
            request = request.clone({
              headers: request.headers.set('Authorization', `Bearer ${res.resultObj.access_token}`)
            });

            refreshTokenSubject.next(res.resultObj.access_token);
          } else {
            // Lấy token từ cookie cho desktop
            const newToken = authService.getCookie(Token.ACCESS_TOKEN);
            refreshTokenSubject.next(newToken);
          }

          return next(request);
        } else {
          authService.logout();
          router.navigate(['/login']);
          return throwError(() => new Error('Token refresh failed'));
        }
      }),
      catchError(error => {
        isRefreshing = false;
        authService.logout();
        router.navigate(['/login']);
        return throwError(() => error);
      })
    );
  }

  return refreshTokenSubject.pipe(
    filter(token => token !== null),
    take(1),
    switchMap(token => {
      // Thêm token vào header nếu là thiết bị di động
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        request = request.clone({
          headers: request.headers.set('Authorization', `Bearer ${token}`)
        });
      }
      return next(request);
    })
  );
}