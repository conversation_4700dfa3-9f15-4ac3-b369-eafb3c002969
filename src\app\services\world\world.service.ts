import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

const apiUrl = environment.apiUrl + '/api/World/';

@Injectable({
  providedIn: 'root'
})
export class WorldService {

  constructor(
    private http: HttpClient
  ) { }
  // Start Global
  getRegions(): Observable<any> {
    return this.http.get(apiUrl + 'regions');
  }

  getCountries(region_name?: string): Observable<any> {
    var request = {
      regionName: region_name || ''
    }
    return this.http.post(apiUrl + `countries`, request);;
  }

  getStates(country_code: string): Observable<any> {
    return this.http.get(apiUrl + 'states?CountryCode=' + country_code);
  }

  search(request: any) {
    return this.http.post(apiUrl + 'search', request);
  }

  phones(): Observable<any> {
    return this.http.get(apiUrl + 'phones');
  }
  getSubContinents(langCode: string): Observable<any> {
    return this.http.get(apiUrl + 'SubContinents/' + langCode);
  }
  // End Global

  // Start Tour
  getRegionsTour(request: any): Observable<any> {
    return this.http.post(apiUrl + 'tour/regions', request);
  }

  getCountriesTour(request: any): Observable<any> {
    return this.http.post(apiUrl + `tour/countries`, request);;
  }

  getStatesTour(request: any): Observable<any> {
    return this.http.post(apiUrl + 'tour/states', request);
  }

  searchTour(request: any) {
    return this.http.post(apiUrl + 'tour/search', request);
  }
  // End Tour

  //start flight
  getRegionsFlight(request: any): Observable<any> {
    return this.http.post(apiUrl + 'flight/regions', request);
  }

  getCountriesFlight(request: any): Observable<any> {
    return this.http.post(apiUrl + `flight/countries`, request);;
  }

  getFlightAirports(request: any): Observable<any> {
    return this.http.post(apiUrl + 'flight/airports', request);
  }

  searchAirport(request: any): Observable<any> {
    // return this.http.post(apiUrl + 'flight/search', request);
    return this.http.post(apiUrl + 'flight/airport-search', request);
  }

  getAirportInfoByCode(airportsCode: string[], language: string): Observable<any> {
    var queryString = airportsCode.join(';');
    var request = {
      airportsCode: queryString,
      language: language
    }
    return this.http.post(`${apiUrl}flight/airport-info`, request);
  }
  getFlightAirportsDefault(languge: string): Observable<any> {
    var request = {
      language: languge
    }
    return this.http.post(apiUrl + 'flight/airports-default', request);
  }
  getAirportRanking(): Observable<any> {
    return this.http.get(apiUrl + 'flight/airports-ranking');
  }
  airportRankingSearch(request: any): Observable<any> {
    return this.http.post(apiUrl + 'flight/airport-ranking-search', request);
  }
  updateAirportRanking(request: any): Observable<any> {
    return this.http.put(apiUrl + 'flight/update-airport-ranking', request);
  }
  // end flight
}
