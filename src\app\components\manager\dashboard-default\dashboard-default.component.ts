import { Component } from '@angular/core';
import { FormatcurrencyPipe } from '../../../formatcurrency.pipe';

import { ApexAxisChartSeries, ApexChart, ApexXAxis, ApexYAxis, NgApexchartsModule } from 'ng-apexcharts';
import { ReportService } from '../../../services/report/report.service';

@Component({
  selector: 'app-dashboard-default',
  imports: [
    NgApexchartsModule,
    FormatcurrencyPipe
  ],
  templateUrl: './dashboard-default.component.html',
  styleUrl: './dashboard-default.component.css'
})

export class DashboardDefaultComponent {
  public series: ApexAxisChartSeries | any;
  public chart: ApexChart = { type: 'bar', height: 350 };
  public yaxis: ApexYAxis = {
    title: {
      text: 'Request'
    },
    min: 0,
    // max: 100
  };
  public xaxis: ApexXAxis = {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    title: {
      text: 'Month'
    }
  };
  public dataLabels: any = {
    enabled: true, // Hiển thị dữ liệu trên các điểm
    formatter: (val: number) => val?.toLocaleString(), // Định dạng số (ví dụ: thêm dấu phẩy)
    style: {
      fontSize: '12px',
      colors: ['#304758'] // Màu chữ
    }
  };
  dashboardValue: any;

  constructor(
    private reportService: ReportService
  ) {
    this.series = [{
      name: 'booking',
      data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    }];
  }

  async ngOnInit() {
    const res = await this.reportService.getPartnerReport().toPromise();
    try {
      if (res.isSuccessed) {
        this.dashboardValue = res.resultObj;
        var month = new Date().getMonth() + 1; // Lấy tháng hiện tại (1-12)
        this.series = [{
          name: 'booking',
          data: this.dashboardValue.bookingOverview.map((item: any, index: number) => {
            if (item.month > month)
              return null;
            return item.bookings;
          })
        }];

      }
    }
    catch (error) {
      console.log(error);
    }
  }
}