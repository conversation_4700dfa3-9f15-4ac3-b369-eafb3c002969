<header class="fixed top-0 left-0 right-0 z-50">
    <div class="container mx-auto px-4">
        <div class="">
            <div class="flex items-center justify-center py-4 px-6 w-auto">
                <div
                    class="flex items-center space-x-1 px-4 py-2  bg-white/70 backdrop-blur-md  rounded-2xl shadow-sm border border-white/20">
                    <a routerLink="/"
                        class="flex flex-row items-center gap-2 space-x-2 relative z-10 h-10  w-auto max-md:mr-12  max-md:items-start max-md:space-x-0 max-md:space-y-1">
                        <img alt="Ngọc Mai" loading="lazy" decoding="async" class="object-contain md:h-16 h-12 "
                            src="/assets/img/favicon/logoNgocMai-text.png" style="inset: 0px; color: transparent;">
                        <div class="uppercase text-primary-400 max-md:text-base  md:hidden">
                            <div class="flex items-center space-x-2 group">
                                <div
                                    class="h-8 w-1 rounded-full bg-gradient-to-b from-primary-400 to-primary-600 mr-2 shadow-lg">
                                </div>
                                <div>
                                    <div
                                        class="text-xs max-md:text-xs font-bold tracking-widest leading-3 text-primary-600 drop-shadow-lg font-sans font-display transition-all duration-200 group-hover:scale-105 group-hover:text-primary-700">
                                        <span
                                            class="bg-gradient-to-r from-primary-400 via-primary-600 to-primary-400 bg-clip-text text-transparent">Phòng
                                            vé</span>
                                    </div>
                                    <div
                                        class="text-base max-md:text-base font-extrabold leading-3 text-primary-500 drop-shadow-xl font-sans font-display tracking-wider transition-all duration-200 group-hover:scale-105">
                                        <span
                                            class="bg-gradient-to-r from-primary-500 via-primary-700 to-primary-400 bg-clip-text text-transparent ">Indeed</span>
                                        <span
                                            class="ml-1 text-primary-700 bg-gradient-to-r from-primary-700 to-primary-400 bg-clip-text text-transparent">Travel</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                    <nav class="hidden md:flex text-black/90  font-medium transition-colors">

                        <a routerLink="/news" class="relative px-3 py-2 hover:text-white group text-nowrap "
                            routerLinkActive="active">
                            Tin
                            Tức<span
                                class="absolute bottom-0 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-primary-400 to-primary-500 group-hover:w-1/2 group-[.active]:w-1/2 transition-all duration-300"></span>
                        </a>
                        <a routerLink="/TripAvailable" routerLinkActive="active"
                            class="relative px-3 py-2 hover:text-white group text-nowrap">
                            Đặt chỗ của tôi
                            <span
                                class="absolute bottom-0 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-primary-400 to-primary-500 group-hover:w-1/2 group-[.active]:w-1/2 transition-all duration-300"></span>
                        </a>
                        <a routerLink="/about-us" routerLinkActive="active"
                            class="relative px-3 py-2 hover:text-white group text-nowrap">
                            Giới thiệu<span
                                class="absolute bottom-0 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-primary-400 to-primary-500 group-hover:w-1/2 group-[.active]:w-1/2 transition-all duration-300"></span>
                        </a>
                        <a routerLink="/contact" routerLinkActive="active"
                            class="relative px-3 py-2 hover:text-white group text-nowrap">
                            Liên
                            Hệ<span
                                class="absolute bottom-0 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-primary-400 to-primary-500 group-hover:w-1/2 group-[.active]:w-1/2 transition-all duration-300"></span>
                        </a>
                    </nav>
                    <div class="flex items-center space-x-4  md:pl-4 pl-6 ">
                        <button routerLink="/login"
                            class="md:block h-12 hidden items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary hover:bg-primary/90  bg-gradient-to-r from-primary-500 to-primary-400 text-white hover:from-primary-600 hover:to-primary-500 rounded-xl px-6">Đăng
                            Nhập
                        </button>
                        <button (click)="toggleMobileMenu()"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:text-accent-foreground h-10 w-10 md:hidden text-white border-white/30 hover:bg-white/10"><span
                                class="sr-only">Menu</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="h-5 w-5 text-black">
                                <line x1="4" x2="20" y1="12" y2="12"></line>
                                <line x1="4" x2="20" y1="6" y2="6"></line>
                                <line x1="4" x2="20" y1="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Mobile Menu -->
                <div class="fixed inset-0 z-50 md:hidden" [class.hidden]="!isMobileMenuOpen">
                    <!-- Backdrop -->
                    <div class="fixed inset-0 bg-black/50 backdrop-blur-sm"
                        [@fadeInOut]="isMobileMenuOpen ? 'in' : 'out'" (click)="toggleMobileMenu()"></div>

                    <!-- Menu Content -->
                    <div class="fixed right-0 top-0 h-full w-[80%] max-w-sm bg-white shadow-lg"
                        [@slideInOut]="isMobileMenuOpen ? 'in' : 'out'">
                        <div class="flex flex-col h-full">
                            <div class="flex items-center justify-between p-4 border-b">
                                <h2 class="text-lg font-semibold">Menu</h2>
                                <button (click)="toggleMobileMenu()" class="p-2 hover:bg-gray-100 rounded-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="h-6 w-6">
                                        <line x1="18" y1="6" x2="6" y2="18"></line>
                                        <line x1="6" y1="6" x2="18" y2="18"></line>
                                    </svg>
                                </button>
                            </div>

                            <nav class="flex-1 p-4 space-y-2" [@menuItems]="isMobileMenuOpen">

                                <a routerLink="/news"
                                    class="block px-4 py-2 text-lg hover:bg-gray-100 rounded-lg transition-colors">
                                    Tin Tức
                                </a>
                                <a routerLink="/TripAvailable"
                                    class="block px-4 py-2 text-lg hover:bg-gray-100 rounded-lg transition-colors">
                                    Đặt chỗ của tôi
                                </a>
                                <a routerLink="/about-us"
                                    class="block px-4 py-2 text-lg hover:bg-gray-100 rounded-lg transition-colors">
                                    Giới thiệu
                                </a>
                                <a routerLink="/contact"
                                    class="block px-4 py-2 text-lg hover:bg-gray-100 rounded-lg transition-colors">
                                    Liên Hệ
                                </a>
                            </nav>

                            <div class="p-4 border-t">
                                <button routerLink="/login"
                                    class="w-full h-12 bg-gradient-to-r from-primary-500 to-primary-400 text-white rounded-full hover:from-primary-600 hover:to-primary-500 transition-colors">
                                    Đăng Nhập
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>