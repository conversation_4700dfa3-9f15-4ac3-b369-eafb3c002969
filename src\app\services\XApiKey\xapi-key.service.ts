import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';

const apiUrl = environment.apiUrl + '/api/XApiKey/';

@Injectable({
  providedIn: 'root'
})
export class XApiKeyService {

  constructor(
    private http: HttpClient
  ) { }

  Items(api: any) {
    return this.http.get(apiUrl + 'items', api);
  }

  Create(request: any) {
    return this.http.post(apiUrl, request);
  }

  getPaging(request: any) {
    return this.http.post(apiUrl + 'paging', request);
  }

  GetPartnerEmailSetting() {
    return this.http.get(apiUrl + 'partner/email-setting');
  }
  GetPartnerEmailSettingWithId(id: string) {
    return this.http.get(apiUrl + 'partner/email-setting/' + id);
  }

  UpdatePartnerEmailSetting(data: any) {
    return this.http.put(apiUrl + 'partner/email-setting', data);
  }

  GetApiKeySetting() {
    return this.http.get(apiUrl + 'partner/apikey-setting');
  }

  GetApiKeySettingWithId(id: string) {
    return this.http.get(apiUrl + 'partner/apikey-setting/' + id);
  }

  toggleApikey(isActive: boolean) {
    return this.http.put(apiUrl + 'partner/toggle-apikey', { isActive });
  }
  toggleApikeyWithId(id: string, isActive: boolean) {
    return this.http.put(apiUrl + 'partner/toggle-apikey/' + id, { isActive });
  }
  updateApiKeySettingWithID(id: string, data: any) {
    return this.http.put(apiUrl + 'partner/apikey-setting/' + id, data);
  }
}
