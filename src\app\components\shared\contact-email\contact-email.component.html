<section class="py-20 bg-white relative overflow-hidden">
    <div class="container mx-auto px-4">
        <div
            class="bg-gradient-to-r from-primary-500 to-primary-600 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div class="absolute -top-20 -right-20 w-64 h-64 bg-primary-400/20 rounded-full blur-3xl"></div>
            <div class="absolute -bottom-20 -left-20 w-64 h-64 bg-primary-400/20 rounded-full blur-3xl"></div>
            <div class="max-w-3xl mx-auto text-center relative z-10">
                <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">Đăng ký nhận thông tin ưu đãi</h3>
                <p class="text-white/90 mb-8 max-w-2xl">Nhận thông báo về các ưu đãi đặc biệt, mẹo du lịch và nhiều hơn
                    nữ<PERSON>
                    khi đăng ký
                    nhận bản tin của chúng tôi</p>

                <form [formGroup]="emailForm" (ngSubmit)="subscribeNewsletter()"
                    class="flex flex-col md:flex-row gap-4 justify-center">
                    <div class="relative flex-1">
                        <input formControlName="email"
                            class="flex w-full border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white/20 border-white/30 text-white placeholder:text-white/60 focus-visible:ring-white h-12 pl-4 rounded-xl"
                            placeholder="Email của bạn" type="email">
                        @if (emailError) {
                        <p class="text-red-200 text-xs mt-1">{{ emailError }}</p>
                        }
                    </div>
                    <button type="submit" [disabled]="isSubmitting"
                        class="inline-flex items-center justify-center gap-2 text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 py-2 bg-white text-primary-600 hover:bg-primary-50 whitespace-nowrap rounded-xl h-12 px-6 font-medium">
                        {{ isSubmitting ? 'Đang xử lý...' : 'Đăng ký ngay' }}
                    </button>
                </form>
                <p class="text-white/70 text-sm mt-4">Chúng tôi tôn trọng quyền riêng tư của bạn. Bạn có thể hủy đăng ký
                    bất
                    cứ lúc nào.</p>
            </div>
        </div>
    </div>
</section>