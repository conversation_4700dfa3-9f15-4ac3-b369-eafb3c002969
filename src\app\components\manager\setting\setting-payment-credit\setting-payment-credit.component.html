<div
    class="mt-2  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-6">
    <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div class="flex md:flex-row flex-col justify-between space-y-1.5 p-6">
            <div>
                <h3 class="text-2xl font-semibold leading-none tracking-tight"><PERSON><PERSON>ch <PERSON>
                </h3>
                <p class="text-sm text-muted-foreground">Quản lý thông tin tài khoản ngân hàng cho khách hàng
                    chuyển khoản</p>
            </div>
            <button *ngIf="banksInfoModel.banksInfo.length > 0" (click)="saveBankInfor()"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-[#fb6340] via-[#fb6340] to-[#fbb140] text-white hover:opacity-90 h-10 px-4 py-2 ">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-save mr-2 h-4 w-4">
                    <path
                        d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z">
                    </path>
                    <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"></path>
                    <path d="M7 3v4a1 1 0 0 0 1 1h7"></path>
                </svg> Lưu Thay Đổi
            </button>
        </div>

        <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-6">
            <div class="p-6 pt-0 space-y-2">
                <label
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Hướng
                    dẫn xác nhận thanh toán:</label>
                <div class="flex items-start gap-2  rounded-lg  relative">
                    <textarea [(ngModel)]="banksInfoModel.note"
                        placeholder="Quý khách sau khi thực hiện việc chuyển khoản thành công, vui lòng gửi email đến <EMAIL>. hoặc gọi số (028) 2229 3030 để được xác nhận thanh toán từ Ngọc Mai Travel."
                        class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[60px] flex-1"></textarea>
                </div>

                <label
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Nội
                    dung chuyển khoản:</label>
                <div class="flex items-start gap-2  rounded-lg  relative">
                    <textarea [(ngModel)]="banksInfoModel.transferContent" placeholder="Mã đơn hàng"
                        class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[60px] flex-1"></textarea>
                </div>
            </div>
        </div>


        <div class="p-6 pt-0 space-y-6">
            @for (bankInfor of banksInfoModel.banksInfo; track $index) {
            <div class="border rounded-lg p-4 relative">
                <button (click)="removeBankInfor($index)"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 w-10 absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"><svg
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-trash2 h-5 w-5">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" x2="10" y1="11" y2="17"></line>
                        <line x1="14" x2="14" y1="11" y2="17"></line>
                    </svg>
                </button>
                <div class="grid gap-4 md:grid-cols-2">
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            for="account-holder-1">Chủ Tài Khoản</label>
                        <input [(ngModel)]="bankInfor.accountHolder" placeholder="Nguyễn Văn A"
                            class="flex h-10 w-full rounded-md border border-input  px-3 py-2 text-sm  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                    </div>
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            for="bank-name-1">Tên Ngân Hàng</label>


                        <ng-select [items]="bankLogos" [(ngModel)]="bankInfor.bankName" bindLabel="name"
                            bindValue="name"
                            class="flex h-10 w-full items-center justify-between rounded-md   text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&amp;>span]:line-clamp-1 col-span-3"
                            placeholder="Vui lòng chọn khu vực">
                            <ng-template ng-option-tmp let-item="item">
                                <div
                                    class="flex items-center gap-2 border-b border-gray-100 bg-white  hover:bg-gray-100 ">
                                    <img [src]="item.logoPath" class="h-6 w-auto" />
                                    {{ item.name }}
                                </div>
                            </ng-template>
                        </ng-select>

                    </div>
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            for="branch-1">Chi Nhánh</label>
                        <input [(ngModel)]="bankInfor.branch" placeholder="Vui lòng nhân tên chi nhánh"
                            class="flex h-10 w-full rounded-md border border-input  px-3 py-2 text-sm  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                    </div>
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Số
                            Tài Khoản</label>
                        <input [(ngModel)]="bankInfor.accountNumber" placeholder="Vui lòng nhập số tài khoản"
                            class="flex h-10 w-full rounded-md border border-input  px-3 py-2 text-sm  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                    </div>
                </div>
            </div>
            }


            <button *ngIf="banksInfoModel.banksInfo.length < 3" (click)="addBankInfor()"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-black text-white hover:opacity-90 h-10 px-4 py-2 ">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-plus mr-2 h-4 w-4">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                </svg> Thêm Tài Khoản
            </button>
        </div>
    </div>
</div>