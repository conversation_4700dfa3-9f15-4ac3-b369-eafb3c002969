import { CommonModule, Location } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { XApiKeyService } from '../../../../services/XApiKey/xapi-key.service';

@Component({
  selector: 'app-setting-email',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './setting-email.component.html',
  styleUrl: './setting-email.component.css'
})
export class SettingEmailComponent {
  showPassword: boolean = false;
  settingEmailForm?: FormGroup;
  formSubmitted: boolean = false;


  constructor(
    private formBuilder: FormBuilder,
    private xapiKeyService: XApiKeyService
  ) {

  }

  ngOnInit(): void {
    this.valiator();
    this.loadEmailSetting();
  }

  valiator() {
    this.settingEmailForm = this.formBuilder.group({
      Id: [''],
      SenderEmail: ['', [Validators.required, Validators.maxLength(255), Validators.email]],
      SMTPServer: ['', [Validators.required, Validators.maxLength(255)]],
      EmailPort: ['587', [Validators.required]],
      EmailPass: ['', [Validators.required]],
    });
  }

  loadEmailSetting() {
    this.xapiKeyService.GetPartnerEmailSetting().subscribe((res: any) => {
      if (res) {
        this.settingEmailForm?.patchValue({
          Id: res.resultObj.id,
          SenderEmail: res.resultObj.senderEmail,
          SMTPServer: res.resultObj.smtpServer,
          EmailPort: res.resultObj.emailPort,
          EmailPass: res.resultObj.emailPass,
        });
      }
    });
  }

  onSubmitForm() {
    this.formSubmitted = true;
    if (this.settingEmailForm?.valid) {
      const formData = this.settingEmailForm.value;
      this.xapiKeyService.UpdatePartnerEmailSetting(formData).subscribe((res: any) => {
        if (res) {
          alert('Email setting updated successfully!');
        } else {
          alert('Failed to update email setting!');
        }
      });
    } else {
      alert('Please fill in all required fields correctly!');
    }
  }

  get fm(): any {
    return this.settingEmailForm?.controls;
  }
  setShowPassword() {
    this.showPassword = !this.showPassword;
  }
}
