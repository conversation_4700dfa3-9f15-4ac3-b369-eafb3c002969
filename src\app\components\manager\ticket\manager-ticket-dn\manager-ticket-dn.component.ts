import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject } from '@angular/core';
import { FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { Ngb<PERSON>alendar, NgbDate, NgbDateParserFormatter, NgbDatepickerModule, NgbDropdownModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { formatCurrency, formatDate, formatDateTo_ddMMyyyy, range } from '../../../../common/function.common';
import { FlightService } from '../../../../services/flight/flight.service';
import { LoaderService } from '../../../../services/loader/loader.service';
import { SearchStateService } from '../../../../services/search-state/search-state.service';



interface FilterOption {
  key: string | number | null;
  value: string;
  selected: boolean;
}

interface DataTable {
  allRecords: number;
  from: number;
  to: number;
  pageCount: number;
  pageSize: number;
  pageIndex: number;
  totalRecords: number;
  items: any[];
}
@Component({
  selector: 'app-manager-ticket-dn',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterLink,
    NgbDropdownModule,
    NgbDatepickerModule,
  ],
  templateUrl: './manager-ticket-dn.component.html',
  styleUrl: './manager-ticket-dn.component.css'
})

export class ManagerTicketDNComponent implements OnInit {
  // Services
  private readonly flightService = inject(FlightService);
  private readonly toastService = inject(ToastrService);
  private readonly calendar = inject(NgbCalendar);
  private readonly formatter = inject(NgbDateParserFormatter);
  private readonly router = inject(Router);
  private readonly searchStateService = inject(SearchStateService);
  public readonly loaderService = inject(LoaderService);

  // ViewChild
  @ViewChild('fromDatepicker') fromDatepicker: any;
  @ViewChild('toDatepicker') toDatepicker: any;
  // Date picker properties
  hoveredDate: NgbDate | null = null;
  fromDate: NgbDate = this.calendar.getToday();
  toDate: NgbDate = this.calendar.getToday();
  isMobile = false;

  // Filter options
  readonly typeFilter: FilterOption[] = [
    { key: null, value: 'Tất cả', selected: true },
    { key: 0, value: 'Đơn mới', selected: false },
    { key: 2, value: 'Đang thực hiện', selected: false },
    { key: 1, value: 'Đã thanh toán', selected: false },
    { key: -1, value: 'Đóng', selected: false }
  ];

  readonly airlineFilter: FilterOption[] = [
    { key: '', value: 'Tất cả', selected: true },
    { key: 'VN', value: 'Vietnam Airlines', selected: false },
    { key: 'VJ', value: 'VietJet Air', selected: false },
    { key: 'QH', value: 'Bamboo Airways', selected: false },
    { key: '1G', value: '1G Galileo', selected: false },
    { key: 'AK', value: 'AK AirAsia', selected: false }
  ];

  // Search and pagination
  searchModel: any = {
    PageIndex: 1,
    PageSize: 10,
    SortColumn: '',
    SortOrder: 'asc',
    TypeDate: 'BookingDate',
    FromDate: this.formatter.format(this.fromDate),
    ToDate: this.formatter.format(this.toDate),
    CodeKeyword: '',
    FilterStatus: null,
    FilterAirline: '',
    FilterAgent: '',
    CustomerKeyword: '',
  };

  dataTable: DataTable = {
    allRecords: 0,
    from: 0,
    to: 0,
    pageCount: 0,
    pageSize: 0,
    pageIndex: 0,
    totalRecords: 0,
    items: []
  };

  startIndex = 1;
  finishIndex = 1;

  constructor() {
    this.initializeScreenSize();
  }

  private initializeScreenSize(): void {
    this.checkScreenSize();
    window.addEventListener('resize', () => this.checkScreenSize());
  }

  private checkScreenSize(): void {
    this.isMobile = window.innerWidth < 768;
  }

  async ngOnInit(): Promise<void> {
    const savedState = this.searchStateService.getSearchState();
    if (savedState) {
      this.searchModel = savedState;
      // Update date pickers if needed
      if (savedState.FromDate) {
        const parsedDate = this.formatter.parse(savedState.FromDate);
        if (parsedDate) {
          this.fromDate = new NgbDate(parsedDate.year, parsedDate.month, parsedDate.day);
        }
      }
      if (savedState.ToDate) {
        const parsedDate = this.formatter.parse(savedState.ToDate);
        if (parsedDate) {
          this.toDate = new NgbDate(parsedDate.year, parsedDate.month, parsedDate.day);
        }
      }
    }
    await this.getPaging();
  }

  // Date picker methods
  formatDateTo_ddMMyyyy(date: NgbDate | null): string | null {
    if (!date) return null;
    const dateT = new Date(date.year, date.month - 1, date.day);
    return formatDateTo_ddMMyyyy(dateT);
  }

  onFromDateSelect(date: NgbDate): void {
    this.fromDate = date;
    this.searchModel.FromDate = this.formatter.format(date);
    this.fromDatepicker.close();
  }

  onToDateSelect(date: NgbDate): void {
    this.toDate = date;
    this.searchModel.ToDate = this.formatter.format(date);
    this.toDatepicker.close();
  }

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    const parsed = this.formatter.parse(input);
    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;
  }

  // Filter methods
  async setSelectedTypeFilter(value: string | number | null): Promise<void> {
    this.typeFilter.forEach(type => type.selected = type.key === value);
    this.searchModel.FilterStatus = value as number | null;
  }

  async setSelectedAirlineFilter(value: string | number | null): Promise<void> {
    this.airlineFilter.forEach(airline => airline.selected = airline.key === value);
    this.searchModel.FilterAirline = value as string;
  }

  get selectedAirlineFilter(): string {
    return this.airlineFilter.find(airline => airline.selected)?.value ?? 'Tất cả';
  }

  get selectedTypeFilter(): string {
    return this.typeFilter.find(type => type.selected)?.value ?? 'Tất cả';
  }

  // Pagination methods
  async changePageSize(event: Event): Promise<void> {
    const value = (event.target as HTMLSelectElement).value;
    this.searchModel.PageSize = Number(value);
  }

  async getUrl(page: number): Promise<void> {
    if (page < 1 || page > this.dataTable.pageCount || page === this.searchModel.PageIndex) {
      return;
    }
    this.searchModel.PageIndex = page;
    await this.getPaging();
  }

  // Data fetching methods
  async getPaging(): Promise<void> {
    try {
      const res = await this.flightService.pagingPartnerFlightRequest(this.searchModel).toPromise();
      if (res.isSuccessed) {
        this.dataTable = res.resultObj;
        this.updatePaginationInfo();
      }
    } catch (error) {
      this.toastService.error('Có lỗi xảy ra khi tải dữ liệu');
    }
  }

  private updatePaginationInfo(): void {
    this.dataTable.from = (this.dataTable.pageIndex - 1) * this.dataTable.pageSize + 1;
    this.dataTable.to = this.dataTable.from + this.dataTable.items.length - 1;
    this.updatePageIndices();
  }

  private updatePageIndices(): void {
    if (this.dataTable.pageCount <= 5) {
      this.startIndex = 1;
      this.finishIndex = this.dataTable.pageCount;
    } else if (this.dataTable.pageIndex <= 3) {
      this.startIndex = 1;
      this.finishIndex = 5;
    } else if (this.dataTable.pageIndex + 2 >= this.dataTable.pageCount) {
      this.startIndex = this.dataTable.pageCount - 4;
      this.finishIndex = this.dataTable.pageCount;
    } else {
      this.startIndex = this.dataTable.pageIndex - 2;
      this.finishIndex = this.dataTable.pageIndex + 2;
    }
  }

  // Utility methods
  formatCurrency(value: string): string {
    return formatCurrency(value);
  }

  range(start: number, end: number): number[] {
    return range(start, end);
  }

  formatDate(date: Date): string | null {
    return formatDate(date);
  }

  handleDoubleClick(item: any): void {
    this.searchStateService.setSearchState(this.searchModel);
    this.router.navigate(['/manager/ticket', item.id]);
  }

  async sortTable(column: string): Promise<void> {
    if (this.searchModel.SortColumn === column) {
      this.searchModel.SortOrder = this.searchModel.SortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.searchModel.SortColumn = column;
      this.searchModel.SortOrder = 'asc';
    }
    await this.getPaging();
  }

  async onSubmitSearch(): Promise<void> {
    this.searchModel.PageIndex = 1;
    this.searchStateService.setSearchState(this.searchModel);
    await this.getPaging();
  }

}