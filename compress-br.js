const fs = require("fs");
const path = require("path");
const zlib = require("zlib");

const folder = "dist/hvn_Indeedtravel"; // ⚠️ Sửa lại đúng tên app của bạn

const compressFile = (filePath) => {
    const content = fs.readFileSync(filePath);
    const compressed = zlib.brotliCompressSync(content);
    fs.writeFileSync(filePath + ".br", compressed);
    console.log("Compressed:", filePath);
};

const walkDir = (dir) => {
    fs.readdirSync(dir).forEach((file) => {
        const fullPath = path.join(dir, file);
        if (fs.statSync(fullPath).isDirectory()) {
            walkDir(fullPath);
        } else if (/\.(js|css|html|json|svg|txt)$/.test(fullPath)) {
            compressFile(fullPath);
        }
    });
};

walkDir(folder);
