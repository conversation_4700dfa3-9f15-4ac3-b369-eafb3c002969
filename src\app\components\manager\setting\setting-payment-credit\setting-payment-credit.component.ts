import { Component } from '@angular/core';
import { BANK_LOGOS } from '../../../../share/data/bank-logos';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { FeatureService } from '../../../../services/feature/feature.service';

export interface BankAccountInfo {
  accountHolder: string;   // Chủ Tài Khoản
  bankName: string | null;        // Tên Ngân Hàng
  branch: string;          // Chi Nhánh
  accountNumber: string;   // Số <PERSON>
}

@Component({
  selector: 'app-setting-payment-credit',
  imports: [
    CommonModule,
    FormsModule,
    NgSelectModule
  ],
  templateUrl: './setting-payment-credit.component.html',
  styleUrl: './setting-payment-credit.component.css'
})
export class SettingPaymentCreditComponent {
  public bankLogos = BANK_LOGOS;
  public banksInfoModel: {
    banksInfo: BankAccountInfo[];
    note: string;
    transferContent: string;
  } = {
      banksInfo: [],
      note: '',
      transferContent: ''
    };

  constructor(
    private readonly featureService: FeatureService
  ) {
  }


  ngOnInit() {
    this.loadBankInfor();
    if (this.banksInfoModel.banksInfo.length === 0) {
      this.banksInfoModel.banksInfo.push({
        accountHolder: '',
        bankName: null,
        branch: '',
        accountNumber: ''
      });
    }
  }

  loadBankInfor() {
    this.featureService.getPartnerPaymentInfo('credit').subscribe(
      (res: any) => {
        if (res.isSuccessed) {
          this.banksInfoModel = JSON.parse(res.resultObj);
          if (this.banksInfoModel.banksInfo.length === 0) {
            this.banksInfoModel.banksInfo.push({
              accountHolder: '',
              bankName: null,
              branch: '',
              accountNumber: ''
            });
          }
        }
      }
    );
  }


  removeBankInfor(index: number) {
    var confirm = window.confirm('Bạn có chắc chắn muốn xóa ngân hàng này không?');
    if (confirm) {
      this.banksInfoModel.banksInfo.splice(index, 1);
    }
  }

  addBankInfor() {
    for (let i = 0; i < this.banksInfoModel.banksInfo.length; i++) {
      if (this.banksInfoModel.banksInfo[i].accountHolder === '' || this.banksInfoModel.banksInfo[i].bankName === null || this.banksInfoModel.banksInfo[i].branch === '' || this.banksInfoModel.banksInfo[i].accountNumber === '') {
        alert('Vui lòng điền đầy đủ thông tin ngân hàng trước khi thêm ngân hàng mới!');
        return;
      }
    }
    if (this.banksInfoModel.banksInfo.length >= 3) {
      alert('Bạn chỉ được thêm tối đa 5 ngân hàng!');
      return;
    }
    this.banksInfoModel.banksInfo.push({
      accountHolder: '',
      bankName: null,
      branch: '',
      accountNumber: ''
    });
  }

  saveBankInfor() {
    if (this.banksInfoModel.banksInfo.length === 0) {
      alert('Vui lòng thêm ngân hàng trước khi lưu!');
      return;
    }

    if (this.banksInfoModel.note === '') {
      alert('Vui lòng điền ghi chú trước khi lưu!');
      return;
    }
    if (this.banksInfoModel.transferContent === '') {
      alert('Vui lòng điền nội dung chuyển khoản trước khi lưu!');
      return;
    }

    for (let i = 0; i < this.banksInfoModel.banksInfo.length; i++) {
      if (this.banksInfoModel.banksInfo[i].accountHolder === '' || this.banksInfoModel.banksInfo[i].bankName === null || this.banksInfoModel.banksInfo[i].branch === '' || this.banksInfoModel.banksInfo[i].accountNumber === '') {
        alert('Vui lòng điền đầy đủ thông tin ngân hàng trước khi lưu!');
        return;
      }
    }
    this.featureService.PartnerUpdatePaymentInfo(this.banksInfoModel, 'credit').subscribe(
      (res: any) => {
        if (res.isSuccessed) {
          alert('Cập nhật thành công!');
        } else {
          alert('Cập nhật thất bại!');
        }
      },
      (error: any) => {
        alert('Error: Cập nhật thất bại!');
      }
    );
  }

}
