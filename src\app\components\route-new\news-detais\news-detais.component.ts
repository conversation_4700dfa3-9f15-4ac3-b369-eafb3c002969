import { Component, OnInit, inject, AfterViewInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { NewsService } from '../../../services/news/news.service';
import { ToastrService } from 'ngx-toastr';
import { EditorViewerComponent } from '../../ui-common/editor-viewer/editor-viewer.component';
import { promises } from 'dns';
import { Subscription } from 'rxjs';
import { CategoryNewsService } from '../../../services/category-news/category-news.service';
import { ContactService } from '../../../services/contact/contact.service';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

interface NewsDetail {
  name: string;
  description: string;
  pathImage: string;
  detail: string;
  seoTitle: string;
  timeCreate: string;
  viewCount: number;
  categoryName: string;
  seoCategory: string;
  author: string;
}

interface SavedArticle {
  seoTitle: string;
  seoCategory: string;
  name: string;
  pathImage: string;
  timeCreate: string;
  savedAt: string;
}

interface LikedArticle {
  seoTitle: string;
  seoCategory: string;
  name: string;
  pathImage: string;
  timeCreate: string;
  likedAt: string;
}

@Component({
  selector: 'app-news-detais',
  imports: [
    CommonModule,
    RouterLink,
    EditorViewerComponent,
    ReactiveFormsModule
  ],
  templateUrl: './news-detais.component.html',
  styleUrl: './news-detais.component.css'
})
export class NewsDetaisComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly newsService = inject(NewsService);
  private readonly categoryNewsService = inject(CategoryNewsService);
  private readonly contactService = inject(ContactService);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastrService);
  private readonly fb = inject(FormBuilder);
  private routeSubscription: Subscription | null = null;

  seoTitle: string = '';
  typeNew: string = '';
  newsDetail: NewsDetail | null = null;
  relatedNews: NewsDetail[] = [];
  popularNews: NewsDetail[] = [];
  recommendedNews: NewsDetail[] = [];
  categories: any[] = [];
  isLoading: boolean = true;
  editorContent: string = '';
  isSaved: boolean = false;
  isLiked: boolean = false;
  emailForm: FormGroup;
  isSubmitting: boolean = false;

  searchModel: any = {
    PageIndex: 1,
    PageSize: 4,
    SortColumn: '',
    SortOrder: 'desc',
    Type: '',
    Keyword: '',
    FilterStatus: null
  };

  constructor() {
    this.emailForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  async ngOnInit() {
    // Subscribe to route parameter changes
    this.routeSubscription = this.route.params.subscribe(async (params) => {
      this.seoTitle = params['seoTitle'] || '';
      this.typeNew = params['type'] || '';

      if (!this.seoTitle) {
        this.router.navigate(['/news']);
        return;
      }

      await this.loadNewsDetail(this.seoTitle);
      this.scrollToTop();
    });
    await this.loadCategory();
  }

  ngAfterViewInit(): void {
    this.scrollToTop();
  }

  ngOnDestroy() {
    // Clean up subscription when component is destroyed
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }

  private scrollToTop(): void {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  async loadNewsDetail(seoTitle: string): Promise<void> {
    try {
      const response = await this.newsService.getPartnerArticle(seoTitle).toPromise();
      if (response?.isSuccessed) {
        this.newsDetail = response.resultObj;
        this.editorContent = this.newsDetail?.detail || '';
        this.checkArticleStatus();
        await this.loadAllNews();
      } else {
        this.router.navigate(['/404']);
        this.newsDetail = null;
      }
    } catch (error) {
      // this.router.navigate(['/404']);
      this.newsDetail = null;
    } finally {
      this.isLoading = false;
    }
  }

  async loadCategory() {
    try {
      const response = await this.categoryNewsService.getPartnerClientItems().toPromise();
      if (response?.isSuccessed) {
        this.categories = response.resultObj;
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }

  async loadRelatedNews(): Promise<void> {
    try {
      if (!this.typeNew) return;

      // Lấy bài viết cùng danh mục
      this.searchModel.Type = this.newsDetail?.seoCategory;
      this.searchModel.SortColumn = 'TimeCreate';

      const response = await this.newsService.getPartnerClientNewsPaging(this.searchModel).toPromise();
      if (response?.isSuccessed && Array.isArray(response.resultObj?.items)) {
        const filteredNews = response.resultObj.items.filter(
          (news: NewsDetail) => news.seoTitle !== this.newsDetail?.seoTitle
        );
        this.relatedNews = filteredNews.slice(0, 3);
      }
    } catch (error) {
      console.error('Error loading related news:', error);
    }
  }

  async loadPopularNews(): Promise<void> {
    try {
      // Lấy bài viết có lượt xem cao nhất
      this.searchModel.Type = '';
      this.searchModel.SortColumn = 'viewCount';

      const response = await this.newsService.getPartnerClientNewsPaging(this.searchModel).toPromise();
      if (response?.isSuccessed && Array.isArray(response.resultObj?.items)) {
        const filteredNews = response.resultObj.items.filter(
          (news: NewsDetail) => news.seoTitle !== this.newsDetail?.seoTitle
        );
        this.popularNews = filteredNews.slice(0, 3);
      }
    } catch (error) {
      console.error('Error loading popular news:', error);
    }
  }

  async loadRecommendedNews(): Promise<void> {
    try {
      // Lấy bài viết mới nhất
      this.searchModel.Type = '';
      this.searchModel.SortColumn = 'TimeCreate';

      const response = await this.newsService.getPartnerClientNewsPaging(this.searchModel).toPromise();
      if (response?.isSuccessed && Array.isArray(response.resultObj?.items)) {
        const filteredNews = response.resultObj.items.filter(
          (news: NewsDetail) => news.seoTitle !== this.newsDetail?.seoTitle
        );
        this.recommendedNews = filteredNews.slice(0, 3);
      }
    } catch (error) {
      console.error('Error loading recommended news:', error);
    }
  }

  async loadAllNews(): Promise<void> {
    try {
      await Promise.all([
        this.loadRelatedNews(),
        this.loadPopularNews(),
        this.loadRecommendedNews()
      ]);
    } catch (error) {
      console.error('Error loading all news:', error);
    }
  }

  goBack(): void {
    this.router.navigate(['/news']);
  }

  shareNews(): void {
    if (navigator.share) {
      navigator.share({
        title: this.newsDetail?.name,
        text: this.newsDetail?.description,
        url: window.location.href
      }).catch(console.error);
    } else {
      const url = window.location.href;
      navigator.clipboard.writeText(url).then(() => {
        this.toastService.success('Đã sao chép link bài viết', 'Thành công');
      }).catch(() => {
        this.toastService.error('Không thể sao chép link', 'Lỗi');
      });
    }
  }

  private checkArticleStatus(): void {
    if (!this.newsDetail) return;

    // Check if article is saved
    const savedArticles = this.getSavedArticles();
    this.isSaved = savedArticles.some(article => article.seoTitle === this.newsDetail?.seoTitle);

    // Check if article is liked
    const likedArticles = this.getLikedArticles();
    this.isLiked = likedArticles.some(article => article.seoTitle === this.newsDetail?.seoTitle);
  }

  private getSavedArticles(): SavedArticle[] {
    const saved = localStorage.getItem('savedArticles');
    return saved ? JSON.parse(saved) : [];
  }

  private getLikedArticles(): LikedArticle[] {
    const liked = localStorage.getItem('likedArticles');
    return liked ? JSON.parse(liked) : [];
  }

  private saveToLocalStorage(key: string, article: SavedArticle | LikedArticle): void {
    const existing = localStorage.getItem(key);
    const articles = existing ? JSON.parse(existing) : [];

    // Check if article already exists
    const exists = articles.some((a: any) => a.seoTitle === article.seoTitle);

    if (!exists) {
      articles.push(article);
      localStorage.setItem(key, JSON.stringify(articles));
    }
  }

  private removeFromLocalStorage(key: string, seoTitle: string): void {
    const existing = localStorage.getItem(key);
    if (existing) {
      const articles = JSON.parse(existing);
      const filtered = articles.filter((a: any) => a.seoTitle !== seoTitle);
      localStorage.setItem(key, JSON.stringify(filtered));
    }
  }

  saveNews(): void {
    if (!this.newsDetail) return;

    const savedArticle: SavedArticle = {
      seoTitle: this.newsDetail.seoTitle,
      seoCategory: this.newsDetail.seoCategory,
      name: this.newsDetail.name,
      pathImage: this.newsDetail.pathImage,
      timeCreate: this.newsDetail.timeCreate,
      savedAt: new Date().toISOString()
    };

    if (this.isSaved) {
      this.removeFromLocalStorage('savedArticles', this.newsDetail.seoTitle);
      this.isSaved = false;
      this.toastService.success('Đã xóa khỏi danh sách lưu', 'Thành công');
    } else {
      this.saveToLocalStorage('savedArticles', savedArticle);
      this.isSaved = true;
      this.toastService.success('Đã lưu bài viết', 'Thành công');
    }
  }

  likeNews(): void {
    if (!this.newsDetail) return;

    const likedArticle: LikedArticle = {
      seoTitle: this.newsDetail.seoTitle,
      seoCategory: this.newsDetail.seoCategory,
      name: this.newsDetail.name,
      pathImage: this.newsDetail.pathImage,
      timeCreate: this.newsDetail.timeCreate,
      likedAt: new Date().toISOString()
    };

    if (this.isLiked) {
      this.removeFromLocalStorage('likedArticles', this.newsDetail.seoTitle);
      this.isLiked = false;
      this.toastService.success('Đã bỏ thích bài viết', 'Thành công');
    } else {
      this.saveToLocalStorage('likedArticles', likedArticle);
      this.isLiked = true;
      this.toastService.success('Đã thích bài viết', 'Thành công');
    }
  }

  async subscribeNewsletter(): Promise<void> {
    if (this.emailForm.invalid) {
      this.toastService.error('Vui lòng nhập email hợp lệ', 'Lỗi');
      return;
    }

    try {
      this.isSubmitting = true;
      const response = await this.contactService.sentContact({
        email: this.emailForm.get('email')?.value
      }).toPromise();

      if (response?.isSuccessed) {
        this.toastService.success('Đăng ký nhận tin thành công', 'Thành công');
        this.emailForm.reset();
      } else {
        this.toastService.error('Đăng ký nhận tin thất bại', 'Lỗi');
      }
    } catch (error) {
      this.toastService.error('Có lỗi xảy ra khi đăng ký', 'Lỗi');
    } finally {
      this.isSubmitting = false;
    }
  }

  get emailError(): string {
    const control = this.emailForm.get('email');
    if (control?.hasError('required')) {
      return 'Vui lòng nhập email';
    }
    if (control?.hasError('email')) {
      return 'Email không hợp lệ';
    }
    return '';
  }
}
