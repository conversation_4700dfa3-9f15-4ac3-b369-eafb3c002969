import { Component, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { FlightService } from '../../../services/flight/flight.service';
import { lastValueFrom } from 'rxjs';
import { WorldService } from '../../../services/world/world.service';
import { CommonModule } from '@angular/common';
import { formatDateTo_ddMMyyyy } from '../../../common/function.common';
import { CryptoService } from '../../../services/crypto/crypto.service';


@Component({
  selector: 'app-trip-available',
  imports: [
    FormsModule,
    ReactiveFormsModule,

    CommonModule
  ],
  templateUrl: './trip-available.component.html',
  styleUrl: './trip-available.component.css'
})
export class TripAvailableComponent {
  crudForm?: FormGroup;
  formSubmitted: boolean = false;
  @ViewChild('modalNotificationpass') modalNotificationpass!: TemplateRef<any>;
  isLoading: boolean = false;
  retrievedPnr: any[] = [];
  timeLeft: number = 15 * 60; // 15 minutes in seconds
  interval: any;
  isNotValid: boolean = false;
  orderAvailable: any;
  orderDetails: any;
  _PaymentNote: any = null;
  _NoteModel: any = null;
  inforAirports: any[] = [];
  bankSelected: string = '';
  request = {
    OrderCode: '',
    PhoneCustomer: '',
    EmailCustomer: ''
  };

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private _flightService: FlightService,
    private worldService: WorldService,
    private _cryptoService: CryptoService,
  ) { }

  ngOnInit() {
    this.valiator();

    this.route.queryParams.subscribe(async (params: any) => {
      this.request.OrderCode = params['OrderCode'];
      this.request.PhoneCustomer = params['Contact'];
      this.request.EmailCustomer = params['Contact'];
      if (this.request.OrderCode && this.request.PhoneCustomer && this.request.EmailCustomer) {
        await this.AvailableTrip(this.request);
      } else {
        this.orderAvailable = null;
      }
    });
  }

  valiator() {
    this.crudForm = this.formBuilder.group({
      OrderCode: ['', [Validators.required, Validators.maxLength(15)]],
      Contact: ['', [Validators.required, Validators.maxLength(50)]]
    });
  }

  get fm(): any {
    return this.crudForm?.controls;
  }
  errorString = '';
  async onSubmitCrudForm() {
    this.formSubmitted = true;
    if (this.crudForm?.invalid) {
      return;
    }
    if (this.fm.Contact.value === '') {
      this.errorString = 'Vui lòng nhập số điện thoại hoặc email đặt vé.';
      return;
    }
    this.errorString = '';
    var params = new URLSearchParams();
    params.append('OrderCode', this.fm.OrderCode.value);
    if (this.fm.Contact.value !== '')
      params.append('Contact', this.fm.Contact.value);

    this.router.navigateByUrl('/TripAvailable?' + params.toString());

  }
  async RequestEncrypt(data: any): Promise<any> {
    const encryptedData = await this._cryptoService.eda(JSON.stringify(data));
    return {
      EncryptData: encryptedData
    };
  }
  rePayment() {
    this.router.navigateByUrl(`/TripRePayment?OrderCode=${this.request.OrderCode}&PhoneCustomer=${this.request.PhoneCustomer}&EmailCustomer=${this.request.EmailCustomer}`);
  }
  async CallAvailableTrip(request: any) {
    this.isLoading = true;

    var payloadsEncrypted = await this.RequestEncrypt(request);

    try {
      const res = await this._flightService.AvailableTrip(payloadsEncrypted).toPromise();
      const resDecrypted = await this._cryptoService.dda(res.resultObj);
      var resJson = JSON.parse(resDecrypted);
      if (resJson.IsSuccessed) {
        console.log(resJson);
        var noteData = JSON.parse(resJson.ResultObj.Note);
        this.orderDetails = noteData;
        this.isNotValid = true;
        this.orderAvailable = resJson.ResultObj;

        this._PaymentNote = JSON.parse(resJson.ResultObj.PaymentNote);
        this._NoteModel = JSON.parse(resJson.ResultObj.NoteResult);
        this.formatPassenger();
        await this.getInforAirports();
        if (this.orderAvailable?.PaymentMethod.includes('bank-transfer')) {
          this.bankSelected = this.orderAvailable?.PaymentMethod.split('_')[1];
        }
        this.isLoading = false;
      } else {

        this.errorString = 'Không tìm thấy thông tin đơn hàng này';
        this.isLoading = false;
      }
    } catch (error: any) {
      if (error.status !== 200) {
        this._cryptoService.ra();
        await this._cryptoService.spu();
        await this.CallAvailableTrip(request);
      }
    }
  }
  formatPassenger() {
    var indexInfant = 0;
    this.orderDetails?.paxList.forEach((pax: any, index: number) => {
      if (pax.type == 'infant') {
        //get pax adult index same index infant
        var paxAdult = this.orderDetails.paxList.find((pax: any) => pax.type == 'adult' && pax.index == indexInfant);
        if (paxAdult) {
          paxAdult.withInfant = pax;
          //remove pax infant
          this.orderDetails.paxList.splice(index, 1);
        }
        indexInfant++;
      } else {
        pax.index = index;
      }
    });
  }
  async AvailableTrip(request: any) {
    if (!this._cryptoService.ch()) {
      await this._cryptoService.spu();
    }
    this.CallAvailableTrip(request);
  }

  async getInforAirports() {
    var airportsCode: string[] = [];
    this.orderDetails.full?.InventoriesSelected.forEach((inventory: any) => {
      inventory.segment.Legs.forEach((leg: any) => {
        if (!airportsCode.includes(leg.DepartureCode)) {
          airportsCode.push(leg.DepartureCode);
        }
        if (!airportsCode.includes(leg.ArrivalCode)) {
          airportsCode.push(leg.ArrivalCode);
        }
      }
      )

    });
    try {
      var res = await lastValueFrom(this.worldService.getAirportInfoByCode(airportsCode, 'vi'));
      if (res.isSuccessed) {
        this.inforAirports = res.resultObj;
      }
    } catch (error: any) {
      console.error(error);
    }
  }

  formatDateTo_ddMMyyyy(date: string): string | null {
    var dateT = new Date(date);
    return formatDateTo_ddMMyyyy(dateT);
  }
  getDurationByArray(legs: any[]): string {
    if (legs == null) return '';
    var duration = 0;
    var departure = new Date(legs[0].DepartureDate);
    var arrival = new Date(legs[legs.length - 1].ArrivalDate);
    duration = arrival.getTime() - departure.getTime();
    var hours = Math.floor(duration / 3600000);
    var minutes = Math.floor((duration % 3600000) / 60000);
    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');
  }
  convertDurationToHour(duration: number): string {
    const hours = Math.floor(duration / 60).toString().padStart(2, '0');
    const minutes = (duration % 60).toString().padStart(2, '0');
    return `${hours}h${minutes}`;
  }

  getTimeFromDateTime(dateTime: string): string {
    const date = new Date(dateTime);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }
  getDayInWeek(date: string): string {
    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    const dateObj = new Date(date);
    return days[dateObj.getDay()];
  }
  formatddMMyyyy(date: string): string {
    if (date == null) return '';
    var dateObj = new Date(date);
    return dateObj.getDate().toString().padStart(2, '0') + '/' + ((dateObj.getMonth() + 1).toString().padStart(2, '0')) + '/' + dateObj.getFullYear();
  }
  getDuration(leg: any): string {
    if (leg == null) return '';
    var departure = new Date(leg.DepartureDate);
    var arrival = new Date(leg.ArrivalDate);
    var duration = arrival.getTime() - departure.getTime();
    var hours = Math.floor(duration / 3600000);
    var minutes = Math.floor((duration % 3600000) / 60000);
    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');
  }
}
