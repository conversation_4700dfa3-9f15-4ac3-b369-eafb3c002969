.faq-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.faq-header {
    text-align: center;
    margin-bottom: 3rem;
}

.faq-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a365d;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.faq-subtitle {
    font-size: 1.1rem;
    color: #718096;
    margin-bottom: 0;
}

.faq-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.faq-item {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    cursor: pointer;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    transition: all 0.3s ease;
    border-bottom: 1px solid #e2e8f0;
}

.faq-question:hover {
    background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1.5;
    flex: 1;
    padding-right: 1rem;
}

.faq-icon {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a5568;
    transition: all 0.3s ease;
    min-width: 24px;
    text-align: center;
}

.faq-icon.expanded {
    transform: rotate(45deg);
    color: #3182ce;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-in-out;
    background: #ffffff;
}

.faq-answer.expanded {
    max-height: 2000px;
}

.answer-content {
    padding: 2rem;
    color: #4a5568;
    line-height: 1.7;
}

.answer-content h4 {
    color: #2d3748;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
}

.answer-content h4:first-child {
    margin-top: 0;
}

.answer-content h5 {
    color: #2d3748;
    font-size: 1rem;
    font-weight: 600;
    margin: 1.2rem 0 0.8rem 0;
}

.answer-content p {
    margin: 1rem 0;
    color: #4a5568;
}

.answer-content ul,
.answer-content ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.answer-content li {
    margin: 0.5rem 0;
    color: #4a5568;
}

.answer-content strong {
    color: #2d3748;
    font-weight: 600;
}

.important-note {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border: 1px solid #feb2b2;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.important-note h5 {
    color: #c53030;
    margin-top: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.important-note ul {
    margin: 1rem 0 0 0;
    padding-left: 1.5rem;
}

.important-note li {
    color: #742a2a;
    margin: 0.5rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .faq-container {
        padding: 1rem;
    }

    .faq-title {
        font-size: 2rem;
    }

    .faq-question {
        padding: 1rem 1.5rem;
    }

    .faq-question h3 {
        font-size: 1rem;
    }

    .answer-content {
        padding: 1.5rem;
    }

    .answer-content h4 {
        font-size: 1.1rem;
    }

    .answer-content h5 {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .faq-container {
        padding: 0.5rem;
    }

    .faq-title {
        font-size: 1.75rem;
    }

    .faq-question {
        padding: 0.75rem 1rem;
    }

    .faq-question h3 {
        font-size: 0.95rem;
    }

    .answer-content {
        padding: 1rem;
    }

    .important-note {
        padding: 1rem;
    }
}

/* Animation for smooth transitions */
.faq-item {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom scrollbar for long content */
.answer-content::-webkit-scrollbar {
    width: 6px;
}

.answer-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.answer-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.answer-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}