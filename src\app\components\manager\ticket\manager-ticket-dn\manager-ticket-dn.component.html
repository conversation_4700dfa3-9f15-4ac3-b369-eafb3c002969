<div class="container relative w-full md:p-4 p-2 shadow-lg bg-white dark:bg-gray-900 rounded-lg h-full min-w-full">

    <div class="w-full h-full relative flex flex-col pb-4  sm:rounded-lg  md:gap-6 gap-2">
        <div class="w-full shadow rounded-xl flex flex-col p-2 md:gap-6 gap-2">
            <div class="flex flex-row flex-wrap justify-between md:gap-6 gap-2 relative">
                <div class="flex flex-row flex-wrap md:gap-6 gap-2">
                    <select [(ngModel)]="searchModel.TypeDate"
                        class="flex items-center cursor-pointer justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-[120px]">
                        <option value="BookingDate" selected>Ngày đặt</option>
                        <option value="DepartDate">Ngày bay</option>
                    </select>
                    <div class="h-fit w-full md:w-auto md:max-w-52">
                        <div class="flex relative">
                            <div
                                class="px-2 w-14  py-2.5 h-12 rounded-s-lg flex items-center justify-center bg-gray-50 border border-gray-300 text-gray-900 text-nowrap">
                                Từ
                            </div>
                            <input #dpFromDate readonly
                                class="rounded-none min-w-[6.5rem] bg-gray-50 border text-gray-900 focus:ring-primary-500 focus:border-primary-500 block flex-1 w-full text-sm border-gray-300 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="dd/MM/yyyy" name="dpFromDate" [value]="formatDateTo_ddMMyyyy(fromDate)"
                                (input)="fromDate = validateInput(fromDate, dpFromDate.value) || fromDate" />
                            <button
                                class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 hover:bg-gray-300 border rounded-e-0 border-gray-300 border-e-0 rounded-e-md dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600"
                                (click)="fromDatepicker.toggle()" type="button">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="w-4 h-4 fill-gray-500 hover:fill-gray-700" viewBox="0 0 448 512">
                                    <path
                                        d="M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z" />
                                </svg>
                            </button>
                        </div>
                        <div class="dp-hidden absolute">
                            <div class="input-group">
                                <input name="fromDatepicker" class="form-control" ngbDatepicker
                                    #fromDatepicker="ngbDatepicker" [autoClose]="'outside'"
                                    (dateSelect)="onFromDateSelect($event)" [displayMonths]="isMobile ? 1 : 2"
                                    [dayTemplate]="t" outsideDays="hidden" tabindex="-1" />
                                <ng-template #t let-date let-focused="focused">
                                    <span class="custom-day" [class.focused]="focused"
                                        [class.selected]="date.equals(fromDate)" (mouseenter)="hoveredDate = date"
                                        (mouseleave)="hoveredDate = null">
                                        {{ date.day }}
                                    </span>
                                </ng-template>
                            </div>
                        </div>
                    </div>
                    <div class="h-fit w-full md:w-auto md:max-w-52">
                        <div class="flex relative">
                            <div
                                class="px-2 w-14  py-2.5 h-12 rounded-s-lg flex items-center justify-center bg-gray-50 border border-gray-300 text-gray-900 text-nowrap">
                                Đến
                            </div>
                            <input #dpToDate readonly
                                class="rounded-none min-w-[6.5rem] bg-gray-50 border text-gray-900 focus:ring-primary-500 focus:border-primary-500 block flex-1 w-full text-sm border-gray-300 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="dd/MM/yyyy" name="dpToDate" [value]="formatDateTo_ddMMyyyy(toDate)"
                                (input)="toDate = validateInput(toDate, dpToDate.value) || toDate" />
                            <button
                                class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 hover:bg-gray-300 border rounded-e-0 border-gray-300 border-e-0 rounded-e-md dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600"
                                (click)="toDatepicker.toggle()" type="button">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="w-4 h-4 fill-gray-500 hover:fill-gray-700" viewBox="0 0 448 512">
                                    <path
                                        d="M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z" />
                                </svg>
                            </button>
                        </div>
                        <div class="dp-hidden absolute">
                            <div class="input-group">
                                <input name="toDatepicker" class="form-control" ngbDatepicker
                                    #toDatepicker="ngbDatepicker" [autoClose]="'outside'"
                                    (dateSelect)="onToDateSelect($event)" [displayMonths]="isMobile ? 1 : 2"
                                    [dayTemplate]="t" outsideDays="hidden" tabindex="-1" />
                                <ng-template #t let-date let-focused="focused">
                                    <span class="custom-day" [class.focused]="focused"
                                        [class.selected]="date.equals(toDate)" (mouseenter)="hoveredDate = date"
                                        (mouseleave)="hoveredDate = null">
                                        {{ date.day }}
                                    </span>
                                </ng-template>
                            </div>
                        </div>
                    </div>

                    <div ngbDropdown>
                        <button ngbDropdownToggle id="dropdown-filter"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-12 rounded-md px-3"
                            type="button">
                            <svg class="fill-black" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                aria-hidden="true" width="1rem" height="1rem" fill="white">
                                <path
                                    d="m1.802 6.623 6.279 3.676a1.11 1.11 0 0 0 1.317-.205L12.75 6.75c.32-.326-.336-.632-.782-.716l-8.274-1.69a1.11 1.11 0 0 0-1 .317L1.664 5.71a.58.58 0 0 0 .138.913">
                                </path>
                                <path
                                    d="M6.125 15.085a1.11 1.11 0 0 0 .973-.321l11.77-11.995a4.3 4.3 0 0 1 2.13-1.197l.201-.043a1.05 1.05 0 0 1 1.273 1.296l-.043.206a4.4 4.4 0 0 1-1.175 2.17L17.45 9.078a1.16 1.16 0 0 0-.31 1.016l1.742 9.732a1.16 1.16 0 0 1-.3 1.069l-1.008 1.026a.557.557 0 0 1-.896-.14l-3.664-7.13a.557.557 0 0 0-.896-.14l-2.59 2.64a1.16 1.16 0 0 0-.297 1.085l.662 2.746a1.475 1.475 0 0 1-.86 1.399h-.001a1.1 1.1 0 0 1-1.529-.547L6.29 18.822a1.73 1.73 0 0 0-.791-.807l-3.212-.951a1.163 1.163 0 0 1-.542-1.537 1.43 1.43 0 0 1 1.375-.93z">
                                </path>
                            </svg>
                            Hãng:
                            <span class="text-sm font-normal text-gray-600">{{selectedAirlineFilter}} </span>
                        </button>
                        <div ngbDropdownMenu aria-labelledby="dropdown-filter" state="closed" side="bottom"
                            class="z-50 hidden">
                            <div
                                class="z-50 bg-white w-full min-w-[8rem] overflow-hidden rounded-md border p-1 text-gray-600 shadow-md">
                                @for (type of airlineFilter; track $index) {
                                <button type="button" ngbDropdownItem (click)="setSelectedAirlineFilter(type.key)"
                                    class="relative w-full hover:bg-gray-200 flex cursor-pointer select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                                    {{type.value}}
                                </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <select [(ngModel)]="searchModel.PageSize" (change)="changePageSize($event)"
                    class="flex max-md:absolute top-0 px-2 md:py-3 py-2 right-0 border border-gray-300  items-center cursor-pointer justify-between rounded-md  bg-background  text-sm ring-offset-background focus:outline-none   ">
                    <option value="10" selected>10 dòng</option>
                    <option value="20">20 dòng</option>
                    <option value="50">50 dòng</option>
                    <option value="100">100 dòng</option>
                    <option value="200">200 dòng</option>
                </select>
            </div>

            <div class="flex md:flex-row flex-col justify-between w-full gap-4 md:gap-6">
                <div class="md:flex w-full  md:gap-6 gap-2 md:flex-row md:flex-wrap grid grid-cols-2">
                    <input type="search" [(ngModel)]="searchModel.CodeKeyword"
                        class="block py-2 text-sm text-gray-900 border p-2 border-gray-300 rounded-lg w-full md:w-80 bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        placeholder="Mã đơn hàng/Mã đặt chỗ">
                    <input type="search" [(ngModel)]="searchModel.CustomerKeyword"
                        class="block py-2 text-sm text-gray-900 border p-2 border-gray-300 rounded-lg w-full md:w-80 bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        placeholder="Họ tên khách/Email/Phone">
                </div>
                <div class="flex items-center space-x-2 mt-2 md:mt-0">
                    <div ngbDropdown>
                        <button ngbDropdownToggle id="dropdown-filter"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"
                            type="button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-filter h-4 w-4 mr-2">
                                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                            </svg>Lọc:
                            <span class="text-sm font-normal text-gray-600">{{selectedTypeFilter}} </span>
                        </button>
                        <div ngbDropdownMenu aria-labelledby="dropdown-filter" state="closed" side="bottom"
                            class="z-50 hidden">
                            <div
                                class="z-50 bg-white w-full min-w-[8rem] overflow-hidden rounded-md border p-1 text-gray-600 shadow-md">
                                @for (type of typeFilter; track $index) {
                                <button type="button" ngbDropdownItem (click)="setSelectedTypeFilter(type.key)"
                                    class="relative w-full hover:bg-gray-200 flex cursor-pointer select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                                    {{type.value}}
                                </button>
                                }
                            </div>
                        </div>
                    </div>
                    <button type="button" (click)="onSubmitSearch()"
                        class="italic from-primary-300 to-primary-300 via-primary-400 hover:via-primary-500 hover:from-primary-300 hover:to-primary-300 transition-all duration-300 bg-gradient-to-r gap-2 whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 h-10 px-4 w-full md:w-auto py-6 text-base font-medium rounded-2xl shadow-md hover:shadow-lg text-white focus:ring-4 focus:outline-none focus:ring-primary-300 flex items-center justify-center">
                        <svg class="w-6 h-6 inline-block text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                                d="m21 21-3.5-3.5M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" />
                        </svg>
                        <span>Tìm </span>
                    </button>
                </div>
            </div>
        </div>

        <div class="w-full h-full overflow-auto">
            <table
                class="w-full h-fill-available text-sm text-left sm:mb-28 max-sm:mb-32 rtl:text-right text-gray-500 dark:text-white">
                <thead
                    class="sticky -top-1 text-xs text-white uppercase bg-gradient-to-r from-[#fb6340] via-[#fb6340] to-[#fbb140] dark:bg-gray-700  dark:bg-none">
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Thao tác

                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Mã đơn hàng
                                <a (click)="sortTable('Id')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Mã đặt chỗ
                                <a (click)="sortTable('Pnrs')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Họ tên khách
                                <a (click)="sortTable('CustomerName')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Điện thoại
                                <a (click)="sortTable('PhoneNumber')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Hành trình

                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Tổng tiền
                                <a (click)="sortTable('TotalPrice')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Ngày đặt
                                <a (click)="sortTable('TimeCreate')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Trạng thái
                                <a (click)="sortTable('Status')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if(dataTable.items.length > 0){
                    @for(item of dataTable.items || []; track $index ){
                    <tr (dblclick)="handleDoubleClick(item)"
                        [ngClass]="{'bg-red-200 dark:bg-red-600': item.isActive === false, 'odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800': item.isActive}"
                        class="cursor-pointer border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">
                        <td class="p-2 font-medium text-gray-800">
                            <a routerLink="/manager/ticket/{{item.id}}"
                                class="cursor-pointer flex w-fit items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-lg  dark:hover:bg-gray-600 dark:hover:text-white">
                                <svg className="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                    viewBox="0 0 24 24">
                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z" />
                                </svg>
                            </a>
                        </td>
                        <td class="p-2 font-medium text-gray-800 text-left">
                            {{item.id}}
                        </td>
                        <td class="p-2 font-medium text-gray-800">
                            @if(item.pnrs){
                            @if(item.pnrs?.includes('Fail')){
                            <span class="text-red-500 bg-red-200 px-2 py-1 rounded-md">{{item.pnrs}}</span>
                            }@else{
                            <span class="text-green-500 bg-green-200 px-2 py-1 rounded-md">{{item.pnrs}}</span>
                            }
                            }
                        </td>
                        <td class="p-2 font-medium text-gray-800">
                            {{item.customerName}}
                        </td>
                        <td class="p-2 text-center">
                            {{item.phoneNumber}}
                        </td>
                        <td class="p-2 text-left">
                            <div class="flex flex-col justify-start">
                                @if(item?.itinerary?.length > 0){
                                @for (itinerary of item?.itinerary; track $index) {
                                <span
                                    class="text-base text-gray-500 dark:text-gray-400 text-left text-nowrap">{{$index+1}}.&nbsp;{{itinerary}}</span>
                                }
                                }
                            </div>
                        </td>
                        <td class="p-2 text-right font-bold text-nowrap">{{formatCurrency(item.totalPrice)}}</td>
                        <td class="p-2 text-center text-nowrap">
                            <div class="text-sm text-right">
                                <div class="text-nowrap">Tạo: {{item?.timeCreate}}</div>
                                @if(item?.timeUpdate){
                                <div class="text-nowrap text-xs text-gray-400">Cập nhật: {{item?.timeUpdate}}</div>
                                <div class="text-nowrap text-xs text-gray-400">Cập nhật bởi: {{item?.updateBy}}</div>
                                }
                            </div>
                        </td>
                        <td class="p-2 text-center">
                            <span [class]="
                            'px-2 py-1 border rounded-full  ' +
                            (item.status === 0 ? 'bg-blue-200 text-blue-500 border-blue-500' : '') +
                            (item.status === 1 ? 'bg-green-200 text-green-500 border-green-500' : '') +
                            (item.status === -1 ? 'bg-red-200 text-red-500 border-red-500' : '') +
                            (item.status === 2 ? 'bg-amber-200 text-amber-500 border-amber-500' : '')
                          ">
                                <span class="text-nowrap" *ngIf="item.status === 0">Đơn mới</span>
                                <span class="text-nowrap" *ngIf="item.status === 1">Đã thanh toán </span>
                                <span class="text-nowrap" *ngIf="item.status === -1">Đóng </span>
                                <span class="text-nowrap" *ngIf="item.status === 2">Đang thực hiện </span>
                            </span>
                        </td>
                    </tr>
                    }
                    }@else {
                    <tr
                        class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800 border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">
                        <td class="px-6 py-4" colspan="9">
                            <div class="flex items-center justify-center">
                                <span class="text-gray-500 dark:text-gray-400">No data found</span>
                            </div>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>

    </div>
    <nav class="pagination absolute bg-white sm:rounded-lg dark:bg-gray-600 w-full bottom-0 flex items-center flex-column flex-wrap md:flex-row justify-between p-4"
        aria-label="Table navigation">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Showing
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable.from}} - {{dataTable.to}}</span> of
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable.totalRecords}}</span></span>
        <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(1)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m17 16-4-4 4-4m-6 8-4-4 4-4" />
                    </svg>
                </a>
            </li>
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(dataTable.pageIndex - 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m14 8-4 4 4 4" />
                    </svg>
                </a>
            </li>

            <li *ngFor="let page of range(startIndex, finishIndex); let i = index"
                [class.active]="page === dataTable.pageIndex">
                <a class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                    (click)="getUrl(page)" [title]="'Page ' + page">
                    {{ page }}
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageIndex + 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m10 16 4-4-4-4" />
                    </svg>
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageCount)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m7 16 4-4-4-4m6 8 4-4-4-4" />
                    </svg>
                </a>
            </li>
        </ul>
    </nav>


</div>