.error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f8f9fa;
    padding: 20px;
}

.error-content {
    text-align: center;
    max-width: 600px;
    padding: 40px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.error-code {
    font-size: 120px;
    font-weight: bold;
    color: #dc3545;
    margin: 0;
    line-height: 1;
}

.error-title {
    font-size: 32px;
    color: #343a40;
    margin: 20px 0;
}

.error-message {
    font-size: 18px;
    color: #6c757d;
    margin-bottom: 30px;
    line-height: 1.5;
}

.home-button {
    padding: 12px 30px;
    font-size: 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
        background-color: #0056b3;
    }
}