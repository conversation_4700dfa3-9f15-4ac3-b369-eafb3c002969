<div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:r2:-trigger-overview"
    id="radix-:r2:-content-overview" tabindex="0"
    class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-4"
    style="animation-duration: 0s;">
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
            <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 class="tracking-tight text-sm font-medium">Total Revenue</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-dollar-sign h-4 w-4 text-muted-foreground">
                    <line x1="12" x2="12" y1="2" y2="22"></line>
                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold">
                    {{dashboardValue?.revenue?.totalRevenue | formatcurrency}}
                </div>
                <p class="text-xs text-muted-foreground">
                    @if(dashboardValue?.revenue?.revenueChangePercentage>=0){
                    +
                    }
                    {{dashboardValue?.revenue?.revenueChangePercentage}}% from last month
                </p>
            </div>
        </div>
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
            <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 class="tracking-tight text-sm font-medium">Bookings</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-credit-card h-4 w-4 text-muted-foreground">
                    <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                    <line x1="2" x2="22" y1="10" y2="10"></line>
                </svg>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold">+{{dashboardValue?.revenue?.bookings}}</div>
                <p class="text-xs text-muted-foreground">
                    @if(dashboardValue?.revenue?.bookingsChangePercentage>=0){
                    +
                    }
                    {{dashboardValue?.revenue?.bookingsChangePercentage}}% from last month</p>
            </div>
        </div>

    </div>
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm col-span-4" data-v0-t="card">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="text-2xl font-semibold leading-none tracking-tight">Booking Overview</h3>
            </div>
            <div class="p-6 pt-0 pl-2">
                <div id="chart">
                    <!-- <apx-chart [series]="series" [chart]="chart"></apx-chart> -->
                    <apx-chart [series]="series" [chart]="chart" [yaxis]="yaxis" [xaxis]="xaxis"
                        [dataLabels]="dataLabels" [autoUpdateSeries]="true">
                    </apx-chart>
                </div>
            </div>
        </div>
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm col-span-3" data-v0-t="card">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Bookings</h3>
            </div>
            <div class="p-6 pt-0">
                <div class="relative w-full overflow-auto ">
                    <table class="w-full caption-bottom text-sm ">
                        <thead class="[&amp;_tr]:border-b">
                            <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                                <th
                                    class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                    Customer</th>
                                <th
                                    class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                    Service</th>
                                <th
                                    class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                    Amount</th>
                                <th
                                    class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                    Status</th>
                            </tr>
                        </thead>
                        <tbody class="[&amp;_tr:last-child]:border-0">
                            @for(booking of dashboardValue?.recentBookings; track booking){
                            <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                                    {{booking?.customer}}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                                    {{booking?.service}}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                                    {{booking?.amount | formatcurrency}}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                                    {{booking?.status}}
                                </td>
                            </tr>
                            }

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>