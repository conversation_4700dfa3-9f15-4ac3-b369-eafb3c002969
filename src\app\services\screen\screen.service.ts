import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { Observable } from 'rxjs';
import { CryptoService } from '../crypto/crypto.service';

const apiUrl = environment.apiUrl;
const publicKey = atob(environment.publicKey);

@Injectable({
  providedIn: 'root'
})
export class ScreenService {
  private readonly http = inject(HttpClient);
  private readonly cryptoService = inject(CryptoService);


  public saveScreen(data: any) {
    if (!data.parentID || data.parentID == '') {
      data.parentID = null;
    }
    return this.http.post(apiUrl + '/api/Screens', data);
  }

  public getItems(): Observable<any> {
    return this.http.get(apiUrl + '/api/Screens/items');
  }

  public getPaging(data: any): Observable<any> {
    return this.http.post(apiUrl + '/api/Screens/paging', data);
  }

  public getTree(roleId: string): Observable<any> {
    return this.http.get(apiUrl + '/api/Screens/tree/' + roleId);
  }

  public saveRoleScreens(roleID: string, data: any): Observable<any> {
    var dataPost = {
      RoleID: roleID,
      ScreenIDs: data
    }
    return this.http.post(apiUrl + '/api/Screens/SetDefaultScreensForRole', dataPost);
  }

  public changePerMissionScreenOfUser(UserId: string, ListScreenIds: any): Observable<any> {
    var dataPost = {
      UserId: UserId,
      ListScreenIds: ListScreenIds
    }
    return this.http.post(apiUrl + '/api/Screens/ChangePerMissionScreenOfUser', dataPost);
  }

  public getById(id: string): Observable<any> {
    return this.http.get(apiUrl + '/api/Screens/' + id);
  }

  public translate(screenCode: any): Observable<any> {
    return this.http.get(apiUrl + `/api/Translate/page/${screenCode}`);
  }
  public saveTranslate(data: any): Observable<any> {
    return this.http.post(apiUrl + '/api/Translate', data);
  }

  public async getPageTranslate(request: any): Promise<Observable<any>> {
    try {
      const requestString = JSON.stringify(request);
      const encryptedText = await this.cryptoService.encryptForge(publicKey, requestString);
      const requestEncrypt = {
        encryptData: encryptedText
      };
      return this.http.post<any>(`${apiUrl}/api/Translate/page`, requestEncrypt);
    } catch (error) {
      throw error;
    }
  }
  public getTreeScreensUser(id: string): Observable<any> {
    return this.http.get(apiUrl + '/api/Screens/treeUser/' + id);
  }
  public getMenu(): Observable<any> {
    return this.http.get(apiUrl + '/api/Screens/menu');
  }
}
