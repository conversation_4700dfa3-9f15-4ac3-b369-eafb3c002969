import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withRouterConfig, withInMemoryScrolling } from '@angular/router';
import { routes } from './app.routes';
import { provideClientHydration, withEventReplay } from '@angular/platform-browser';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientModule, provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { tokenInterceptor } from './interceptor/token/token.interceptor';
import { provideToastr } from 'ngx-toastr';
import { deviceIdInterceptor } from './interceptor/deviceId/device-id.interceptor';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(
      routes,
      withInMemoryScrolling({
        anchorScrolling: 'enabled',
        scrollPositionRestoration: 'enabled',
      })
    ),
    provideClientHydration(withEventReplay()),
    // provideTranslateService({
    //   defaultLanguage: 'vi'
    // }),
    importProvidersFrom(NgbModule),
    provideHttpClient(withFetch(), withInterceptors([tokenInterceptor, deviceIdInterceptor])),
    // provideHttpClient(withFetch()),
    provideAnimations(),
    provideToastr(),
  ]
};
