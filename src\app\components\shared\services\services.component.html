<section class="py-20 bg-white relative overflow-hidden">
    <div class="container mx-auto px-4">
        <div class="text-center max-w-3xl mx-auto mb-16">
            <div
                class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200">
                Dịch vụ của chúng tôi</div>
            <h2 class="text-3xl md:text-4xl font-bold mb-4 tracking-tight text-gray-600">Tại sao chọn chúng tôi</h2>
            <p class="text-gray-600">Chúng tôi cam kết mang đến trải nghiệm đặt vé máy bay tốt nhất cho bạn với những
                dịch
                v<PERSON> chất lư<PERSON> cao</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="group">
                <div class="rounded-lg border text-card-foreground border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden bg-white h-full"
                    data-v0-t="card">
                    <div
                        class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 to-primary-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300">
                    </div>
                    <div class="p-8">
                        <div class="flex flex-col items-center text-center">
                            <div
                                class="h-16 w-16 rounded-2xl bg-primary-100 flex items-center justify-center mb-6 group-hover:bg-primary-200 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-trending-up h-8 w-8 text-primary-600">
                                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                    <polyline points="16 7 22 7 22 13"></polyline>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3 group-hover:text-primary-600 transition-colors">Giá cả
                                cạnh tranh
                            </h3>
                            <p class="text-gray-600">Chúng tôi đảm bảo mang đến mức giá tốt nhất cho mọi chuyến bay với
                                nhiều ưu đãi
                                hấp dẫn</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="group">
                <div class="rounded-lg border text-card-foreground border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden bg-white h-full"
                    data-v0-t="card">
                    <div
                        class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 to-primary-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300">
                    </div>
                    <div class="p-8">
                        <div class="flex flex-col items-center text-center">
                            <div
                                class="h-16 w-16 rounded-2xl bg-primary-100 flex items-center justify-center mb-6 group-hover:bg-primary-200 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-shield h-8 w-8 text-primary-600">
                                    <path
                                        d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z">
                                    </path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3 group-hover:text-primary-600 transition-colors">Đảm
                                bảo
                                an toàn
                            </h3>
                            <p class="text-gray-600">Bảo mật thông tin và thanh toán an toàn là ưu tiên hàng đầu của
                                chúng tôi</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="group">
                <div class="rounded-lg border text-card-foreground border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden bg-white h-full"
                    data-v0-t="card">
                    <div
                        class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 to-primary-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300">
                    </div>
                    <div class="p-8">
                        <div class="flex flex-col items-center text-center">
                            <div
                                class="h-16 w-16 rounded-2xl bg-primary-100 flex items-center justify-center mb-6 group-hover:bg-primary-200 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-headphones h-8 w-8 text-primary-600">
                                    <path
                                        d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3">
                                    </path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3 group-hover:text-primary-600 transition-colors">Hỗ trợ
                                24/24</h3>
                            <p class="text-gray-600">Đội ngũ hỗ trợ chuyên nghiệp luôn sẵn sàng giúp đỡ bạn mọi lúc, mọi
                                nơi</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="group">
                <div class="rounded-lg border text-card-foreground border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden bg-white h-full"
                    data-v0-t="card">
                    <div
                        class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 to-primary-600 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300">
                    </div>
                    <div class="p-8">
                        <div class="flex flex-col items-center text-center">
                            <div
                                class="h-16 w-16 rounded-2xl bg-primary-100 flex items-center justify-center mb-6 group-hover:bg-primary-200 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-credit-card h-8 w-8 text-primary-600">
                                    <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                    <line x1="2" x2="22" y1="10" y2="10"></line>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3 group-hover:text-primary-600 transition-colors">Thanh
                                toán linh
                                hoạt</h3>
                            <p class="text-gray-600">Nhiều phương thức thanh toán khác nhau để bạn lựa chọn với quy
                                trình đơn giản
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="absolute top-1/3 -right-16 w-48 h-48 bg-primary-50 rounded-full opacity-60 blur-3xl"></div>
    <div class="absolute bottom-1/3 -left-16 w-48 h-48 bg-blue-50 rounded-full opacity-60 blur-3xl"></div>
</section>