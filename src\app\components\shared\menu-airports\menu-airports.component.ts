import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { WorldService } from '../../../services/world/world.service';


@Component({
  selector: 'app-menu-airports',
  templateUrl: './menu-airports.component.html',
  styleUrl: './menu-airports.component.css',
  imports: [CommonModule]
})
export class MenuAirportsComponent {
  @Input() tripType: string = 'departure';
  @Input() language: string = 'vi';
  @Input() AirportsDefault: any[] = [];
  @Output() airportClick = new EventEmitter<{ airport: any, tripType: string }>();

  AirportsDefaultFiltered: any[] = [];
  searchTerm: string = '';
  hasSearched: boolean = false;
  isLoading: boolean = false; // Thêm thuộc tính này
  private searchTimeout: any = null;
  private worldService = inject(WorldService);

  async searchAirPorts(event: Event) {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;

    // Hủy timeout trước đó nếu người dùng tiếp tục nhập
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Đợi 500ms trước khi thực hiện tìm kiếm
    this.searchTimeout = setTimeout(async () => {
      this.hasSearched = true;
      this.isLoading = true; // Bắt đầu loading
      if (this.searchTerm === '') {
        this.hasSearched = false;
        this.isLoading = false; // Kết thúc loading
        this.AirportsDefaultFiltered = [];
        return;
      }

      const searchTermLower = this.searchTerm.toLowerCase();
      var filteredAirports = this.AirportsDefault
        .filter((airport: any) =>
          airport.name?.toLowerCase().includes(searchTermLower) ||
          airport.cityName?.toLowerCase().includes(searchTermLower) ||
          airport.code?.toLowerCase() === searchTermLower
        );

      // Loại bỏ các sân bay trùng lặp
      filteredAirports = filteredAirports.filter((airport: any, index: number, self: any) =>
        index === self.findIndex((t: any) => t.code === airport.code)
      );

      if (filteredAirports.length === 0) {
        const request = {
          Language: this.language,
          keyword: this.searchTerm
        };
        var res = await this.worldService.searchAirport(request).toPromise();
        this.AirportsDefaultFiltered = res.resultObj;
      } else {
        this.AirportsDefaultFiltered = filteredAirports;
      }
      //check if only one item and this is parent item then select it
      if (this.AirportsDefaultFiltered.length === 1 && this.AirportsDefaultFiltered[0].isParent) {
        this.AirportsDefaultFiltered[0].selected = true;
      }
      this.isLoading = false; // Kết thúc loading
    }, 500); // Delay 500ms trước khi gọi API
  }
  continentClick(continentCode: string) {
    this.AirportsDefault.forEach((continent: any) => {
      continent.continentCode === continentCode ? continent.selected = true : continent.selected = false;
    });
  }

  itemParentClick(code: string) {
    this.AirportsDefaultFiltered.forEach((continent: any) => {
      continent.code === code ? continent.selected = !continent.selected : continent.selected = false;
    });
  }

  onAirportClick(airport: any) {
    this.airportClick.emit({ airport, tripType: this.tripType });
  }
}
