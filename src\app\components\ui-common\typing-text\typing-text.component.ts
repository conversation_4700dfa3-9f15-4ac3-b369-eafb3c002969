import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, Input, OnInit, OnDestroy, PLATFORM_ID, Inject } from '@angular/core';

@Component({
  selector: 'app-typing-text',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './typing-text.component.html',
  styleUrls: ['./typing-text.component.css']
})
export class TypingTextComponent implements OnInit, OnDestroy {
  @Input() text: string = '';
  @Input() typingSpeed: number = 100; // milliseconds
  @Input() delay: number = 1000; // milliseconds - delay before starting to type

  displayText: string = '';
  isTyping: boolean = false;
  showText: boolean = true;

  private currentIndex: number = 0;
  private intervalId: any;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) { }

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.startTyping();
    } else {
      this.displayText = this.text;
    }
  }

  private startTyping() {
    // Reset state
    this.displayText = '';
    this.currentIndex = 0;
    this.isTyping = true;

    // Clear any existing intervals
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    // Start typing
    this.intervalId = setInterval(() => {
      if (this.currentIndex < this.text.length) {
        this.displayText += this.text[this.currentIndex];
        this.currentIndex++;
      } else {
        this.isTyping = false;
        clearInterval(this.intervalId);
      }
    }, this.typingSpeed);
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }
}