import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

const apiUrl = environment.apiUrl;

@Injectable({
  providedIn: 'root'
})
export class RoleService {

  constructor(
    private http: HttpClient
  ) { }

  saveRole(data: any): Observable<any> {
    return this.http.post(apiUrl + '/api/Roles', data);
  }

  getPaging(data: any): Observable<any> {
    return this.http.post(apiUrl + '/api/Roles/paging', data);
  }

  getRoles(): Observable<any> {
    return this.http.get(apiUrl + '/api/Roles');
  }

}
