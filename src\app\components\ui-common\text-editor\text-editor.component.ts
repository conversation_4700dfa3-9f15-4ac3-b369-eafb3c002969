import { CommonModule } from '@angular/common';
import { Component, HostListener, ViewChild, ElementRef, ViewEncapsulation, OnDestroy, AfterViewInit, Inject, PLATFORM_ID, Input, Output, EventEmitter } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { FormsModule, NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { CustomSelectComponent } from '../custom-select/custom-select.component';

@Component({
  selector: 'app-text-editor',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CustomSelectComponent
  ],
  templateUrl: './text-editor.component.html',
  styleUrls: ['./text-editor.component.css'],
  encapsulation: ViewEncapsulation.None,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TextEditorComponent,
      multi: true
    }
  ]
})
export class TextEditorComponent implements ControlValueAccessor, AfterViewInit, OnD<PERSON>roy {
  @Input() set value(val: string) {
    this.content = val || '';
    if (this.editorRef && this.editorRef.nativeElement) {
      this.editorRef.nativeElement.innerHTML = this.content;
    }
  }
  @Output() valueChange = new EventEmitter<string>();

  @ViewChild('editorRef', { static: true }) editorRef!: ElementRef;
  content: string = '';
  currentFontSize: number = 16;
  currentFontFamily: string = 'Poppins';
  fontFamilies: string[] = [
    'Poppins', 'Arial', 'Tahoma', 'Verdana', 'Times New Roman', 'Georgia', 'Courier New', 'Comic Sans MS', 'Impact', 'Lucida Console', 'Segoe UI', 'Roboto', 'Montserrat', 'Helvetica Neue'
  ];
  fontSizes: number[] = [
    8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
  ]


  private savedRange: Range | null = null;
  private lastListType: 'ul' | 'ol' | null = null;
  private selectedImage: HTMLImageElement | null = null;
  private resizeHandles: HTMLDivElement[] = [];
  private cropping = false;
  private cropOverlay: HTMLDivElement | null = null;
  private cropStartX = 0;
  private cropStartY = 0;
  private cropRect = { left: 0, top: 0, width: 0, height: 0 };

  // Để template truy cập được stack
  undoStack: string[] = [];
  redoStack: string[] = [];
  private isRestoringHistory = false;

  showLinkForm = false;
  linkForm = {
    text: '',
    url: '',
    target: '_blank'
  };

  // Thêm biến trạng thái cho form chèn bảng
  showTableForm = false;
  tableForm = {
    rows: 2,
    cols: 2
  };

  showVideoForm = false;
  videoForm = {
    url: ''
  };

  private tableResizeHandles: HTMLElement[] = [];
  private resizingTable = false;
  private resizingColIdx = -1;
  private resizingRowIdx = -1;
  private resizingTableEl: HTMLTableElement | null = null;
  private startX = 0;
  private startY = 0;
  private startWidth = 0;
  private startHeight = 0;

  private resizingTableTotal = false;
  private tableTotalStartX = 0;
  private tableTotalStartY = 0;
  private tableTotalStartWidth = 0;
  private tableTotalStartHeight = 0;

  // Thêm biến để kiểm soát toolbar ảnh nổi
  showImageToolbar = false;
  imageToolbarPosition = { top: 0, left: 0 };
  imageToolbarTarget: HTMLImageElement | null = null;

  // Thêm các biến mới
  showEmojiPicker = false;
  showSpecialCharPicker = false;
  emojiPickerPosition = { top: 0, left: 0 };
  specialCharPickerPosition = { top: 0, left: 0 };

  // Danh sách emoji phổ biến
  emojis = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
    '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
    '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
    '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
    '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '💩',
    '👻', '👽', '👾', '🤖', '😺', '😸', '😹', '😻', '😼', '😽'
  ];

  // Danh sách ký tự đặc biệt phổ biến
  specialChars = [
    '©', '®', '™', '✓', '€', '£', '¥', '¢', '§', '¶', '†',
    '‡', '•', '–', '—', '…', '′', '″', '‹', '›', '«',
    '»', '‹', '›', '¿', '¡', '§', '¶', '†', '‡', '•',
    '–', '—', '…', '′', '″', '‹', '›', '«', '»', '‹',
    '›', '¿', '¡', '§', '¶', '†', '‡', '•', '–', '—',
    '…', '′', '″', '‹', '›', '«', '»', '‹', '›', '¿',
    '¡', '§', '¶', '†'
  ];

  showTextColorPalette = false;
  textColorPalettePosition = { top: 0, left: 0 };
  textColors = [
    ['#000000', '#FFFFFF', '#F28B82', '#FFF475', '#CCFF90', '#A7FFEB', '#CBF0F8', '#AECBFA'],
    ['#D7AEFB', '#FDCFE8', '#E6C9A8', '#E8EAED', '#B6B6B6', '#F4B400', '#DB4437', '#0F9D58'],
    ['#4285F4', '#5F6368', '#F1F3F4', '#202124', '#F8F9FA', '#DADCE0', '#BDC1C6', '#3C4043']
  ];
  selectedTextColor = '#000000';

  // Thêm các biến cho background color
  showBgColorPalette = false;
  bgColorPalettePosition = { top: 0, left: 0 };
  selectedBgColor = '';

  selectedBulletType: string = '';
  selectedNumberType: string = '';

  bulletIcon = '<svg width="24" height="24" focusable="false"><path d = "M11 5h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2Zm0 6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2Zm0 6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2ZM4.5 6c0-.4.1-.8.4-1 .3-.4.7-.5 1.1-.5.4 0 .8.1 1 .4.4.3.5.7.5 1.1 0 .4-.1.8-.4 1-.3.4-.7.5-1.1.5-.4 0-.8-.1-1-.4-.4-.3-.5-.7-.5-1.1Zm0 6c0-.4.1-.8.4-1 .3-.4.7-.5 1.1-.5.4 0 .8.1 1 .4.4.3.5.7.5 1.1 0 .4-.1.8-.4 1-.3.4-.7.5-1.1.5-.4 0-.8-.1-1-.4-.4-.3-.5-.7-.5-1.1Zm0 6c0-.4.1-.8.4-1 .3-.4.7-.5 1.1-.5.4 0 .8.1 1 .4.4.3.5.7.5 1.1 0 .4-.1.8-.4 1-.3.4-.7.5-1.1.5-.4 0-.8-.1-1-.4-.4-.3-.5-.7-.5-1.1Z" fill-rule="evenodd" > </path></svg>';

  bulletListOptions = [
    { value: 'ul-disc', label: 'Disc', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><circle cx="12" cy="12" r="4" fill="currentColor"/></svg>' },
    { value: 'ul-circle', label: 'Circle', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><circle cx="12" cy="12" r="4" fill="none" stroke="currentColor" stroke-width="2"/></svg>' },
    { value: 'ul-square', label: 'Square', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><rect x="8" y="8" width="8" height="8" fill="currentColor"/></svg>' },
    { value: 'ul-dash', label: 'Dash', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><line x1="6" y1="12" x2="18" y2="12" stroke="currentColor" stroke-width="2"/></svg>' },
    { value: 'ul-arrow', label: 'Arrow', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path d="M6 12h12M12 6l6 6-6 6" stroke="currentColor" stroke-width="2" fill="none"/></svg>' },
    { value: 'ul-check', label: 'Check', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path d="M6 12l4 4 8-8" stroke="currentColor" stroke-width="2" fill="none"/></svg>' },
    { value: 'ul-star', label: 'Star', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path d="M12 2l3 6 6 1-4 4 1 6-6-3-6 3 1-6-4-4 6-1z" fill="currentColor"/></svg>' },
    { value: 'ul-heart', label: 'Heart', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path d="M12 21l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21z" fill="currentColor"/></svg>' }
  ];

  numberIcon = `<svg width="24" height="24" focusable="false"><path d="M10 17h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2Zm0-6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2Zm0-6h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 1 1 0-2ZM6 4v3.5c0 .3-.2.5-.5.5a.5.5 0 0 1-.5-.5V5h-.5a.5.5 0 0 1 0-1H6Zm-1 8.8.2.2h1.3c.3 0 .5.2.5.5s-.2.5-.5.5H4.9a1 1 0 0 1-.9-1V13c0-.4.3-.8.6-1l1.2-.4.2-.3a.2.2 0 0 0-.2-.2H4.5a.5.5 0 0 1-.5-.5c0-.3.2-.5.5-.5h1.6c.5 0 .9.4.9 1v.1c0 .4-.3.8-.6 1l-1.2.4-.2.3ZM7 17v2c0 .6-.4 1-1 1H4.5a.5.5 0 0 1 0-1h1.2c.2 0 .3-.1.3-.3 0-.2-.1-.3-.3-.3H4.4a.4.4 0 1 1 0-.8h1.3c.2 0 .3-.1.3-.3 0-.2-.1-.3-.3-.3H4.5a.5.5 0 1 1 0-1H6c.6 0 1 .4 1 1Z" fill-rule="evenodd"></path></svg>`;
  numberListOptions = [
    { value: 'ol-decimal', label: 'Decimal', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><text x="6" y="18" font-size="16" fill="currentColor">1.</text></svg>' },
    { value: 'ol-upper-alpha', label: 'Upper Alpha', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><text x="6" y="18" font-size="16" fill="currentColor">A.</text></svg>' },
    { value: 'ol-lower-alpha', label: 'Lower Alpha', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><text x="6" y="18" font-size="16" fill="currentColor">a.</text></svg>' },
    { value: 'ol-upper-roman', label: 'Upper Roman', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><text x="6" y="18" font-size="16" fill="currentColor">I.</text></svg>' },
    { value: 'ol-lower-roman', label: 'Lower Roman', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><text x="6" y="18" font-size="16" fill="currentColor">i.</text></svg>' }
  ];

  lineHeightIcon = `<svg width="24" height="24" focusable="false"><path d="M21 5a1 1 0 0 1 .1 2H13a1 1 0 0 1-.1-2H21zm0 4a1 1 0 0 1 .1 2H13a1 1 0 0 1-.1-2H21zm0 4a1 1 0 0 1 .1 2H13a1 1 0 0 1-.1-2H21zm0 4a1 1 0 0 1 .1 2H13a1 1 0 0 1-.1-2H21zM7 3.6l3.7 3.7a1 1 0 0 1-1.3 1.5h-.1L8 7.3v9.2l1.3-1.3a1 1 0 0 1 1.3 0h.1c.4.4.4 1 0 1.3v.1L7 20.4l-3.7-3.7a1 1 0 0 1 1.3-1.5h.1L6 16.7V7.4L4.7 8.7a1 1 0 0 1-1.3 0h-.1a1 1 0 0 1 0-1.3v-.1L7 3.6z"></path></svg>`;

  lineHeightOptions = [
    { value: '1', label: '1', svg: '' },
    { value: '1.1', label: '1.1', svg: '' },
    { value: '1.2', label: '1.2', svg: '' },
    { value: '1.3', label: '1.3', svg: '' },
    { value: '1.4', label: '1.4', svg: '' },
    { value: '1.5', label: '1.5', svg: '' },
    { value: '2', label: '2', svg: '' },
    { value: '2.5', label: '2.5', svg: '' },
  ];
  selectedLineHeight: string = '';
  constructor(@Inject(PLATFORM_ID) private platformId: Object) { }
  private onChange: (value: string) => void = () => { };
  private onTouched: () => void = () => { };

  writeValue(value: string): void {
    this.content = value || '';
    if (this.editorRef && this.editorRef.nativeElement) {
      this.editorRef.nativeElement.innerHTML = this.content;
    }
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    if (this.editorRef && this.editorRef.nativeElement) {
      this.editorRef.nativeElement.contentEditable = (!isDisabled).toString();
    }
  }

  format(command: string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand(command, false, '');
    this.updateValue();
  }

  setStrikethrough() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('strikeThrough', false, '');
    this.updateValue();
  }

  align(command: string, allowBoth = false) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return; // Ensure selection exists

    const range = sel.getRangeAt(0);
    const editor = this.editorRef.nativeElement as HTMLElement;
    const tables: HTMLElement[] = [];
    const walker = document.createTreeWalker(editor, NodeFilter.SHOW_ELEMENT, {
      acceptNode: (node) =>
        node.nodeName === 'TABLE'
          ? NodeFilter.FILTER_ACCEPT
          : NodeFilter.FILTER_SKIP,
    });
    let node = walker.nextNode();
    while (node) {
      const table = node as HTMLElement;
      const tableRange = document.createRange();
      tableRange.selectNode(table);
      // Kiểm tra bảng có giao với vùng chọn hoặc vùng chọn nằm trong bảng
      const selectionInTable =
        range.intersectsNode
          ? range.intersectsNode(table)
          : (function () {
            let startNode: any = range.startContainer;
            let endNode: any = range.endContainer;
            return table.contains(startNode) || table.contains(endNode);
          })();
      if (
        (typeof range.intersectsNode === 'function' && selectionInTable) ||
        (
          range.compareBoundaryPoints(Range.END_TO_START, tableRange) < 0 ||
          range.compareBoundaryPoints(Range.START_TO_END, tableRange) > 0
        ) === false ||
        selectionInTable
      ) {
        tables.push(table);
      }
      node = walker.nextNode();
    }

    if (tables.length > 0) {
      // Tạo một div wrapper để bọc toàn bộ nội dung cần căn giữa
      const wrapper = document.createElement('div');
      wrapper.style.textAlign = command === 'justifyCenter' ? 'center' :
        command === 'justifyRight' ? 'right' :
          command === 'justifyLeft' ? 'left' : 'justify';
      wrapper.style.width = '100%';
      wrapper.style.display = 'block';

      // Lấy nội dung cần căn giữa
      const fragment = range.extractContents();
      wrapper.appendChild(fragment);

      // Chèn wrapper vào vị trí cũ
      range.insertNode(wrapper);

      // Căn giữa cho từng table trong wrapper
      tables.forEach(table => {
        table.style.borderCollapse = 'collapse';
        table.style.tableLayout = 'fixed';


        if (command === 'justifyCenter') {
          table.style.marginLeft = 'auto';
          table.style.marginRight = 'auto';
          // table.style.display = 'block';
        } else if (command === 'justifyRight') {
          table.style.marginLeft = 'auto';
          table.style.marginRight = '0';
          // table.style.display = 'block';
        } else if (command === 'justifyLeft') {
          table.style.marginLeft = '0';
          table.style.marginRight = 'auto';
          // table.style.display = 'block';
        } else if (command === 'justifyFull') {
          table.style.marginLeft = '0';
          table.style.marginRight = '0';
          // table.style.display = 'block';
          table.style.width = '100%';
        }
      });

      // Đưa con trỏ vào cuối wrapper
      range.setStartAfter(wrapper);
      range.collapse(true);
      sel.removeAllRanges();
      sel.addRange(range);
      this.updateValue();
      return;
    }
    document.execCommand(command, false, '');
    this.updateValue();
  }

  setHighlight() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('hiliteColor', false, '#fde047');
    this.updateValue();
  }

  setBackgroundColor(color: string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('styleWithCSS', false, 'true');
    document.execCommand('hiliteColor', false, color);
    this.selectedBgColor = color;
    this.updateValue();
  }

  clearBackgroundColor() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('styleWithCSS', false, 'true');
    document.execCommand('hiliteColor', false, 'transparent');
    this.selectedBgColor = '';
    this.updateValue();
  }

  onBgColorPaletteMouseDown(event: MouseEvent, color: string) {
    event.preventDefault();
    event.stopPropagation();
    this.setBackgroundColor(color);
    this.showBgColorPalette = false;
  }

  openBgColorPalette(event: MouseEvent) {
    const button = event.currentTarget as HTMLElement;
    const rect = button.getBoundingClientRect();
    this.bgColorPalettePosition = {
      top: button.offsetHeight,
      left: 0
    };
    this.showBgColorPalette = true;
  }

  closeBgColorPalette() {
    this.showBgColorPalette = false;
  }

  onInput(event: Event) {
    const target = event.target as HTMLElement;
    const selection = window.getSelection();
    let cursorPosition = 0;
    let textNode: Node | null = null;

    // Lưu vị trí con trỏ hiện tại
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      cursorPosition = range.startOffset;
      textNode = range.startContainer;
    }

    // Cập nhật content và emit giá trị mới
    this.content = target.innerHTML;
    this.valueChange.emit(this.content);
    this.onChange(this.content);
    this.onTouched();

    if (!this.isRestoringHistory) {
      this.undoStack.push(this.content);
      this.redoStack = [];
    }

    // Khôi phục vị trí con trỏ (chỉ khi gõ text, không phải khi undo/redo)
    if (!this.isRestoringHistory && selection && selection.rangeCount > 0 && textNode && textNode.nodeType === Node.TEXT_NODE) {
      try {
        const range = selection.getRangeAt(0);
        range.setStart(textNode, cursorPosition);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      } catch (e) {
        console.warn("Failed to restore cursor position:", e);
        // Fallback: try to restore cursor at the end
        const editor = this.editorRef.nativeElement;
        if (editor) {
          const newRange = document.createRange();
          newRange.selectNodeContents(editor);
          newRange.collapse(false);
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
      }
    }
  }

  undo() {
    if (this.undoStack.length > 1) {
      this.isRestoringHistory = true;
      // Lấy trạng thái hiện tại cho redo
      const current = this.undoStack.pop()!;
      this.redoStack.push(current);
      const prev = this.undoStack[this.undoStack.length - 1];
      this.setEditorContent(prev);
      this.isRestoringHistory = false;
      this.updateValue();
      // Sau khi undo, đặt con trỏ ở cuối nội dung mới
      this.placeCursorAtEnd();
    }
  }

  redo() {
    if (this.redoStack.length > 0) {
      this.isRestoringHistory = true;
      const next = this.redoStack.pop()!;
      this.undoStack.push(next);
      this.setEditorContent(next);
      this.isRestoringHistory = false;
      this.updateValue();
      // Sau khi redo, đặt con trỏ ở cuối nội dung mới
      this.placeCursorAtEnd();
    }
  }

  private setEditorContent(html: string) {
    if (this.editorRef && this.editorRef.nativeElement) {
      this.editorRef.nativeElement.innerHTML = html;
      this.content = html;
      this.valueChange.emit(this.content);
      this.onChange(this.content);
    }
  }

  private placeCursorAtEnd() {
    if (this.editorRef && this.editorRef.nativeElement) {
      const editor = this.editorRef.nativeElement;
      const range = document.createRange();
      const selection = window.getSelection();
      if (selection) {
        range.selectNodeContents(editor);
        range.collapse(false); // Collapse range to the end
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  }


  insertImage(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.restoreSelectionOrUseCurrent(); // Use helper
        document.execCommand('insertImage', false, e.target.result);
        this.updateValue();
      };
      reader.readAsDataURL(input.files[0]);
      // Reset input để có thể chọn lại cùng 1 file nếu muốn
      input.value = '';
    }
  }

  insertLink() {
    this.saveSelection(); // Explicitly save before opening form
    // Lưu selection để sau khi nhập xong sẽ chèn link đúng vị trí
    const sel = window.getSelection();
    if (sel && sel.rangeCount > 0) {
      // Nếu có vùng chọn thì lấy text mặc định
      let text = '';
      if (!sel.getRangeAt(0).collapsed) {
        text = sel.toString();
      }
      this.linkForm = {
        text: text,
        url: '',
        target: '_blank'
      };
      this.showLinkForm = true;
    } else {
      // Nếu không có vùng chọn, không mở form hoặc xử lý khác tùy ý
      console.warn('No selection to insert link');
    }
  }

  confirmInsertLink() {
    // Khôi phục selection
    this.restoreSelection && this.restoreSelection();
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) {
      this.showLinkForm = false;
      console.error('No selection to insert link after restoring');
      return;
    }

    let { text, url, target } = this.linkForm;
    url = (url || '').trim();
    text = (text || '').trim();
    target = target || '';

    if (!url) {
      this.showLinkForm = false;
      return;
    }
    if (!/^https?:\/\//i.test(url)) {
      url = 'https://' + url;
    }

    const range = sel.getRangeAt(0);

    // Nếu có vùng chọn, dùng text vùng chọn, nếu không thì dùng text nhập
    if (!range.collapsed && range.toString().trim()) {
      text = range.toString();
      range.deleteContents();
    } else if (!text) {
      // Nếu không có vùng chọn và text form rỗng, dùng url làm text
      text = url;
    }


    // Tạo thẻ a với style gạch chân và màu xanh
    const a = document.createElement('a');
    a.href = url;
    a.textContent = text;
    a.style.textDecoration = 'underline';
    a.style.color = '#2563eb'; // blue-600
    if (target && (target === '_blank' || target === '_self')) {
      a.target = target;
      if (target === '_blank') a.rel = 'noopener noreferrer';
    }
    range.insertNode(a);

    // Đưa con trỏ ra sau thẻ a
    range.setStartAfter(a);
    range.collapse(true);
    sel.removeAllRanges();
    sel.addRange(range);

    this.showLinkForm = false;
    this.updateValue();
  }

  cancelInsertLink() {
    this.showLinkForm = false;
  }

  setFontSize(event: Event) {
    const value = (event.target as HTMLSelectElement).value;
    if (value) {
      this.currentFontSize = parseInt(value, 10);
      this.applyFontSizeToSelection(this.currentFontSize);
      this.updateValue();
    }
  }

  setFontFamily(event: Event) {
    const value = (event.target as HTMLSelectElement).value;
    if (value) {
      this.restoreSelectionOrUseCurrent(); // Use helper
      document.execCommand('fontName', false, value);
      this.currentFontFamily = value;
      this.updateValue();
    }
  }

  onBlur() {
    // Use requestAnimationFrame to ensure activeElement is correctly identified after the blur
    requestAnimationFrame(() => {
      const active = document.activeElement as HTMLElement;
      const editor = this.editorRef.nativeElement as HTMLElement;

      // If focus moves within the editor or to an element related to the editor/toolbar, do not clear savedRange
      if (
        editor.contains(active) ||
        (active && active.closest('.toolbar')) || // Check if the active element is inside an element with class 'toolbar'
        (active && active.classList.contains('toolbar')) // Check if the active element itself has class 'toolbar'
      ) {
        // Do not clear savedRange, the selection might still be relevant
        // Optionally, save the current selection if it's within the editor and not collapsed
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0 && !selection.getRangeAt(0).collapsed && editor.contains(selection.getRangeAt(0).commonAncestorContainer)) {
          this.savedRange = selection.getRangeAt(0).cloneRange();
        }
        return;
      }

      // If focus is completely outside the editor and toolbar, clear savedRange
      this.savedRange = null;
      this.onTouched();
    });
  }

  // Helper function to get the current selection range, or restore the saved one
  private getSelectionRange(): Range | null {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      // If no current selection, try to restore the saved one
      this.restoreSelection();
      const updatedSelection = window.getSelection();
      if (updatedSelection && updatedSelection.rangeCount > 0) {
        return updatedSelection.getRangeAt(0);
      }
      return null;
    }
    // If there is a current selection, return it
    return selection.getRangeAt(0);
  }

  // Helper function to restore saved selection or use current one if saved does not exist/is invalid
  private restoreSelectionOrUseCurrent() {
    const selection = window.getSelection();
    // If there is a current selection and it's within the editor, use it and save it
    if (selection && selection.rangeCount > 0 && this.editorRef.nativeElement.contains(selection.getRangeAt(0).commonAncestorContainer)) {
      this.savedRange = selection.getRangeAt(0).cloneRange();
      return; // Use current selection
    }

    // If no current selection or it's outside the editor, try to restore saved one
    if (
      this.savedRange &&
      this.editorRef &&
      this.editorRef.nativeElement &&
      this.savedRange.commonAncestorContainer &&
      this.editorRef.nativeElement.contains(
        this.savedRange.commonAncestorContainer.nodeType === 1
          ? this.savedRange.commonAncestorContainer
          : this.savedRange.commonAncestorContainer.parentNode
      )
    ) {
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(this.savedRange);
      }
    } else {
      // If savedRange is also invalid, clear it
      this.savedRange = null;
    }
  }

  // Modified restoreSelection - simplified to just add the saved range if it exists and is valid
  restoreSelection() {
    const selection = window.getSelection();
    if (
      this.savedRange &&
      this.editorRef &&
      this.editorRef.nativeElement &&
      this.savedRange.commonAncestorContainer &&
      this.editorRef.nativeElement.contains(
        this.savedRange.commonAncestorContainer.nodeType === 1
          ? this.savedRange.commonAncestorContainer
          : this.savedRange.commonAncestorContainer.parentNode
      )
    ) {
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(this.savedRange);
      }
    } else {
      // If savedRange is also invalid, clear it
      this.savedRange = null;
    }
  }

  saveSelection() {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      this.savedRange = selection.getRangeAt(0).cloneRange();
    } else {
      this.savedRange = null;
    }
  }


  setCustomFontSize(value: number | string) {
    let size = typeof value === 'string' ? parseInt(value, 10) : value;
    if (isNaN(size)) return;
    this.currentFontSize = size;
    this.applyFontSizeToSelection(size); // Should use newSize here
    this.updateValue();
  }

  increaseFontSize() {
    const newSize = Math.min(this.currentFontSize + 1, 100);
    this.currentFontSize = newSize;
    this.applyFontSizeToSelection(newSize);
    this.updateValue();
  }

  decreaseFontSize() {
    const newSize = Math.max(this.currentFontSize - 1, 8);
    this.currentFontSize = newSize;
    this.applyFontSizeToSelection(newSize);
    this.updateValue();
  }

  applyFontSizeToSelection(size: number) {
    if (!this.editorRef || !this.editorRef.nativeElement) return;

    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return;

    const range = sel.getRangeAt(0);
    const editor = this.editorRef.nativeElement as HTMLElement;

    if (!range.collapsed) {
      // Clone nội dung được chọn để xử lý trước
      const selectedContent = range.cloneContents();
      const tempDiv = document.createElement('div');
      tempDiv.appendChild(selectedContent);

      // Loại bỏ các thẻ <font> và style fontSize cũ
      const walker = document.createTreeWalker(tempDiv, NodeFilter.SHOW_ELEMENT, null);
      let node = walker.currentNode as HTMLElement;
      while (node) {
        if (node.tagName === 'FONT') {
          const parent = node.parentElement;
          while (node.firstChild) {
            parent?.insertBefore(node.firstChild, node);
          }
          parent?.removeChild(node);
        } else if (node.style.fontSize) {
          node.style.fontSize = '';
        }
        node = walker.nextNode() as HTMLElement;
      }

      // Gói lại nội dung trong một <span> mới với font-size
      const span = document.createElement('span');
      span.style.fontSize = `${size}px`;
      span.innerHTML = tempDiv.innerHTML;

      // Xóa vùng chọn thật khỏi DOM và chèn lại span đã xử lý
      this.restoreSelectionOrUseCurrent(); // Call again to ensure range is correct
      const updatedSel = window.getSelection();
      if (updatedSel && updatedSel.rangeCount > 0) {
        const updatedRange = updatedSel.getRangeAt(0);
        updatedRange.deleteContents();
        updatedRange.insertNode(span);

        // Đặt lại con trỏ ngay sau thẻ span
        const newRange = document.createRange();
        newRange.selectNodeContents(span);
        newRange.collapse(false);
        updatedSel.removeAllRanges();
        updatedSel.addRange(newRange);
      } else {
        console.warn("Could not get updated selection after applying font size.");
      }


    } else {
      // Nếu không có vùng chọn, áp dụng font-size cho node hiện tại hoặc tạo span mới
      let node = sel.focusNode as HTMLElement | null;
      if (node && node.nodeType === 3) node = node.parentElement;

      if (node && editor.contains(node)) { // Check if node is within editor
        // Check if parent is already a span with font-size, update it
        let parentSpan = node.parentElement;
        while (parentSpan && parentSpan !== editor) {
          if (parentSpan.tagName === 'SPAN' && parentSpan.style.fontSize) {
            parentSpan.style.fontSize = `${size}px`;
            this.currentFontSize = size;
            this.updateValue();
            return; // Done if updated existing span
          }
          parentSpan = parentSpan.parentElement;
        }


        // Otherwise, wrap the current selection/cursor position in a new span
        const span = document.createElement('span');
        span.style.fontSize = `${size}px`;

        // Insert the span at the current cursor position
        range.insertNode(span);

        // Move cursor inside the new span
        range.setStart(span, 0);
        range.collapse(true);
        sel.removeAllRanges();
        sel.addRange(range);

      } else {
        console.warn("Could not find a valid node within the editor to apply font size.");
      }

    }

    this.currentFontSize = size;
  }



  insertCustomList(type: 'square' | 'circle') {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('insertUnorderedList');
    // Đổi kiểu list-style cho list vừa tạo
    setTimeout(() => {
      const editor = this.editorRef.nativeElement as HTMLElement;
      const sel = window.getSelection();
      if (!sel || sel.rangeCount === 0) return;

      let listEl: HTMLElement | null = null;
      if (sel.anchorNode) {
        let node = sel.anchorNode as HTMLElement | null;
        // Find the nearest UL ancestor
        while (node && node !== editor) {
          if (node.nodeName.toLowerCase() === 'ul') {
            listEl = node as HTMLElement;
            break;
          }
          node = node.parentElement;
        }
      }

      // If not found near the selection, try finding the last UL in the editor
      if (!listEl) {
        const lists = editor.querySelectorAll('ul');
        if (lists.length > 0) {
          listEl = lists[lists.length - 1] as HTMLElement;
        }
      }

      if (listEl) {
        listEl.style.listStyleType = type;
        this.updateValue();
      } else {
        console.warn("Could not find a UL element to apply custom list style.");
      }
    });
  }

  onListSelectMouseDown(event: MouseEvent, type: 'ul' | 'ol') {
    // Lưu lại loại list đang chọn để xử lý chuyển đổi mượt mà
    this.lastListType = type;
  }

  onListTypeChange(value: string, listType: 'ul' | 'ol') {
    console.log('onListTypeChange', value);
    this.restoreSelectionOrUseCurrent(); // Use helper
    const editor = this.editorRef.nativeElement as HTMLElement;
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0 || !editor) return;

    const range = sel.getRangeAt(0);

    const isUl = value.startsWith('ul-');
    const isOl = value.startsWith('ol-');

    // Find the nearest UL or OL ancestor of the selection
    let listEl: HTMLElement | null = null;
    let node = range.commonAncestorContainer as HTMLElement | null;
    if (node && node.nodeType === 3) node = node.parentElement;

    while (node && node !== editor) {
      if (node.nodeName.toLowerCase() === 'ul' || node.nodeName.toLowerCase() === 'ol') {
        listEl = node as HTMLElement;
        break;
      }
      node = node.parentElement;
    }


    if (listEl) {
      // If an existing list element is found

      if (isUl && listEl.tagName.toLowerCase() === 'ol') {
        // Change OL to UL
        const ul = document.createElement('ul');
        // Copy list items from OL to new UL
        Array.from(listEl.children).forEach(li => ul.appendChild(li.cloneNode(true)));
        listEl.parentElement?.replaceChild(ul, listEl);
        listEl = ul; // Update listEl to the new UL
      } else if (isOl && listEl.tagName.toLowerCase() === 'ul') {
        // Change UL to OL
        const ol = document.createElement('ol');
        // Copy list items from UL to new OL
        Array.from(listEl.children).forEach(li => ol.appendChild(li.cloneNode(true)));
        listEl.parentElement?.replaceChild(ol, listEl);
        listEl = ol; // Update listEl to the new OL
      } else if (!isUl && !isOl && (listEl.tagName.toLowerCase() === 'ul' || listEl.tagName.toLowerCase() === 'ol')) {
        // Remove list formatting (convert LIs to paragraphs or divs)
        const fragment = document.createDocumentFragment();
        Array.from(listEl.children).forEach(li => {
          const p = document.createElement('p');
          p.innerHTML = li.innerHTML || '&nbsp;'; // Use innerHTML to preserve rich content
          fragment.appendChild(p);
        });
        listEl.parentElement?.replaceChild(fragment, listEl);
        this.updateValue();
        return; // Exit after removing list
      }


      // Apply the new list style type
      if (listEl) { // Re-check listEl in case it was replaced
        const type = value.replace('ul-', '').replace('ol-', '');
        (listEl as HTMLUListElement | HTMLOListElement).style.listStyleType = type;
        this.updateValue();
      }


    } else {
      // If no existing list is found, apply the default list command
      if (isUl) {
        document.execCommand('insertUnorderedList');
        setTimeout(() => {
          // After command, find the newly created list and apply the custom style
          const lists = editor.querySelectorAll('ul');
          if (lists.length > 0) {
            const lastList = lists[lists.length - 1] as HTMLElement;
            const type = value.replace('ul-', '');
            lastList.style.listStyleType = type;
            this.updateValue();
          }
        });
      } else if (isOl) {
        document.execCommand('insertOrderedList');
        setTimeout(() => {
          // After command, find the newly created list and apply the custom style
          const lists = editor.querySelectorAll('ol');
          if (lists.length > 0) {
            const lastList = lists[lists.length - 1] as HTMLElement;
            const type = value.replace('ol-', '');
            lastList.style.listStyleType = type;
            this.updateValue();
          }
        });
      } else {
        // If value is empty and no list found, do nothing or maybe convert to paragraph
        document.execCommand('formatBlock', false, 'p');
        this.updateValue();
      }
    }
  }


  decreaseIndent() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('outdent');
    this.updateValue();
  }

  increaseIndent() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('indent');
    this.updateValue();
  }

  indentFirstLine(remove = false) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return;
    const range = sel.getRangeAt(0);

    const editor = this.editorRef.nativeElement as HTMLElement;
    let blocks: HTMLElement[] = [];

    // Lấy tất cả các block element trong vùng chọn (bao gồm cả div, p, li, h1-6)
    if (!range.collapsed) {
      // Lấy các node nằm trong vùng chọn
      let startNode = range.startContainer;
      let endNode = range.endContainer;
      const walker = document.createTreeWalker(editor, NodeFilter.SHOW_ELEMENT, {
        acceptNode: (node) =>
          /^(P|DIV|LI|BLOCKQUOTE|H[1-6])$/i.test(node.nodeName) && editor.contains(node) // Ensure node is within editor
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_SKIP,
      });
      let node = walker.nextNode();
      let foundStart = false;
      while (node) {
        const block = node as HTMLElement;
        // Check if the block intersects with the selection range
        const blockRange = document.createRange();
        try {
          blockRange.selectNodeContents(block);
          if (range.intersectsNode(block)) {
            blocks.push(block);
          }
        } catch (e) {
          // Fallback check if intersectsNode is not available or fails
          if (editor.contains(block)) { // Simple check if block is in editor
            blocks.push(block);
          }
        }
        node = walker.nextNode();
      }

    }

    // Nếu không có block nào được chọn, tìm block cha gần nhất của vùng chọn (ưu tiên p, div, li, blockquote, h1-6)
    if (blocks.length === 0) {
      let node = range.commonAncestorContainer as HTMLElement | null;
      if (node && node.nodeType === 3) node = node.parentElement;
      while (node && node !== editor) {
        if (
          node.nodeType === 1 &&
          /^(p|div|li|blockquote|h[1-6])$/i.test(node.nodeName)
        ) {
          blocks.push(node);
          break;
        }
        node = node.parentElement;
      }
    }

    // If still no blocks and cursor is in a text node directly under editor, wrap in p
    if (blocks.length === 0 && range.startContainer.nodeType === 3 && range.startContainer.parentElement === editor) {
      const p = document.createElement('p');
      const textNode = range.startContainer;
      textNode.parentElement?.insertBefore(p, textNode);
      p.appendChild(textNode);
      blocks.push(p);
      // Update range to be inside the new paragraph
      range.setStart(textNode, range.startOffset);
      range.collapse(true);
      sel.removeAllRanges();
      sel.addRange(range);
    }


    // Áp dụng hoặc bỏ text-indent cho tất cả các block tìm được
    blocks.forEach((block) => {
      if (remove) {
        block.style.textIndent = '';
      } else {
        // Kiểm tra nếu đã có text-indent rồi thì toggle off
        if (block.style.textIndent === '2em') {
          block.style.textIndent = '';
        } else {
          block.style.textIndent = '2em';
        }
      }
    });
    this.updateValue();
  }

  alignImage(position: 'left' | 'center' | 'right' | 'inline' | 'inline-left' | 'inline-right' | 'inline-center' | 'inline-none' = 'inline') {
    this.restoreSelectionOrUseCurrent(); // Use helper

    let img: HTMLImageElement | null = this.selectedImage;

    // If no selectedImage, try to find an image at the current selection/cursor
    if (!img) {
      const sel = window.getSelection();
      if (sel && sel.rangeCount > 0) {
        let node = sel.focusNode as HTMLElement | null;
        if (node && node.nodeType === 3) node = node.parentElement;
        if (node) {
          img = node.closest('img');
        }
      }
    }

    // If still no image, try to find the first image in the editor
    if (!img && this.editorRef && this.editorRef.nativeElement) {
      const imgs = this.editorRef.nativeElement.querySelectorAll('img');
      if (imgs.length > 0) img = imgs[0];
    }


    console.log('Align image', img, position);
    if (img && this.editorRef.nativeElement.contains(img)) { // Ensure image is in editor
      // Remove existing alignment styles first
      img.style.display = '';
      img.style.verticalAlign = '';
      img.style.marginLeft = '';
      img.style.marginRight = '';
      img.style.float = '';

      // Remove any wrapping div created by previous block alignment
      const parent = img.parentElement;
      if (parent && parent.childNodes.length === 1 && parent.tagName === 'DIV' && parent.style.textAlign) {
        parent.replaceWith(img);
      }


      if (position === 'inline') {
        // Đặt lại display về inline-block để ảnh có thể nằm cùng nhiều dòng text
        img.style.display = 'inline-block';
        img.style.verticalAlign = 'middle';

      } else if (position === 'inline-left') {
        // Ảnh nằm cùng text nhiều dòng, bên trái
        img.style.display = 'inline-block';
        img.style.verticalAlign = 'middle';
        img.style.float = 'left';
        img.style.marginRight = '12px';
      } else if (position === 'inline-right') {
        // Ảnh nằm cùng text nhiều dòng, bên phải
        img.style.display = 'inline-block';
        img.style.verticalAlign = 'middle';
        img.style.float = 'right';
        img.style.marginLeft = '12px';
      } else if (position === 'inline-center') {
        // Ảnh nằm giữa text nhiều dòng (không float, căn giữa)
        // Wrap image in a div and center the div
        const wrapper = document.createElement('div');
        wrapper.style.textAlign = 'center';
        wrapper.style.width = '100%';
        wrapper.style.display = 'block'; // Make the wrapper a block element
        img.parentElement?.insertBefore(wrapper, img);
        wrapper.appendChild(img);
        img.style.display = 'inline-block'; // Keep img as inline-block inside the centered div
      } else if (position === 'inline-none') {
        // Tắt chế độ ảnh cùng text nhiều dòng, trả về mặc định
        img.style.display = '';
        img.style.verticalAlign = '';
        img.style.marginLeft = '';
        img.style.marginRight = '';
        img.style.float = '';
      } else {
        // Block alignment (left, center, right)
        // Wrap image in a div and align the div's text
        const wrapper = document.createElement('div');
        wrapper.style.width = '100%';
        wrapper.style.display = 'block'; // Make the wrapper a block element
        img.parentElement?.insertBefore(wrapper, img);
        wrapper.appendChild(img);

        img.style.display = 'block'; // Make the image a block element inside the wrapper
        img.style.verticalAlign = '';

        switch (position) {
          case 'left':
            wrapper.style.textAlign = 'left';
            img.style.marginLeft = '0';
            img.style.marginRight = 'auto';
            break;
          case 'center':
            wrapper.style.textAlign = 'center';
            img.style.marginLeft = 'auto';
            img.style.marginRight = 'auto';
            break;
          case 'right':
            wrapper.style.textAlign = 'right';
            img.style.marginLeft = 'auto';
            img.style.marginRight = '0';
            break;
        }
      }
      // Reselect the image to update handles if needed
      this.selectImage(img);

    } else {
      console.warn('Không tìm thấy ảnh để align');
    }
    this.updateValue();
  }


  setImagePadding(padding: number | string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    let img: HTMLImageElement | null = this.selectedImage;

    // If no selectedImage, try to find an image at the current selection/cursor
    if (!img) {
      const sel = window.getSelection();
      if (sel && sel.rangeCount > 0) {
        let node = sel.focusNode as HTMLElement | null;
        if (node && node.nodeType === 3) node = node.parentElement;
        if (node) {
          img = node.closest('img');
        }
      }
    }

    // If still no img, try taking the first image in the editor (optional)
    if (!img && this.editorRef && this.editorRef.nativeElement) {
      const imgs = this.editorRef.nativeElement.querySelectorAll('img');
      if (imgs.length > 0) img = imgs[0];
    }

    console.log('Set image padding', img, padding);
    if (img && this.editorRef.nativeElement.contains(img)) { // Ensure image is in editor
      // Đảm bảo padding luôn có đơn vị px nếu là số hoặc chuỗi số
      let pad = padding;
      if (typeof pad === 'number' || (/^\d+(\.\d+)?$/.test(pad))) { // Check for numbers or string numbers
        pad = pad + 'px';
      } else if (typeof pad !== 'string' || pad === '') { // Handle other invalid inputs
        pad = '0px'; // Default to 0 padding
      }
      img.style.padding = pad;
      this.updateValue();
    } else {
      console.warn('Không tìm thấy ảnh để set padding');
    }
  }

  setTextTransform(transform: 'uppercase' | 'lowercase' | 'capitalize' | 'none') {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return;
    let range = sel.getRangeAt(0);
    const editor = this.editorRef.nativeElement as HTMLElement;


    if (!range.collapsed) {
      // Nếu có vùng chọn, áp dụng cho toàn bộ nội dung trong vùng chọn (bằng cách bọc hoặc chỉnh sửa style của block)

      // Check if the selection fully contains a block element (like P, DIV, Hx)
      let commonAncestor = range.commonAncestorContainer as HTMLElement;
      if (commonAncestor.nodeType === 3) commonAncestor = commonAncestor.parentElement!;

      const blocksInSelection: HTMLElement[] = [];
      const walker = document.createTreeWalker(editor, NodeFilter.SHOW_ELEMENT, {
        acceptNode: (node) =>
          /^(P|DIV|LI|BLOCKQUOTE|H[1-6]|SPAN)$/i.test(node.nodeName) && range.intersectsNode(node) && editor.contains(node)
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_SKIP,
      });

      let node = walker.nextNode() as HTMLElement;
      while (node) {
        blocksInSelection.push(node);
        node = walker.nextNode() as HTMLElement;
      }

      if (blocksInSelection.length > 0) {
        // If blocks are found, apply style to them
        blocksInSelection.forEach(block => {
          if (block.style.textTransform === transform) {
            block.style.textTransform = ''; // Toggle off if already applied
          } else {
            block.style.textTransform = transform;
          }
        });
        this.updateValue();
        return;
      }


      // If no blocks are fully contained, wrap the selection in a span
      const span = document.createElement('span');
      span.style.textTransform = transform;
      try {
        range.surroundContents(span);
      } catch (e) {
        console.warn("Could not surround contents with span for text transform:", e);
        // Fallback: extract and insert
        const fragment = range.extractContents();
        span.appendChild(fragment);
        range.insertNode(span);
      }


    } else {
      // Nếu không chọn gì, tìm span gần nhất có text-transform hoặc bọc node hiện tại
      let container = range.commonAncestorContainer as HTMLElement;
      if (container.nodeType === 3) container = container.parentElement!;

      let transformElement: HTMLElement | null = null;
      // Search for an existing SPAN or block element with text-transform near cursor
      let checkNode = container;
      while (checkNode && checkNode !== editor) {
        if (checkNode.style.textTransform || /^(P|DIV|LI|BLOCKQUOTE|H[1-6])$/i.test(checkNode.tagName)) {
          transformElement = checkNode;
          break;
        }
        checkNode = checkNode.parentElement!;
      }


      if (transformElement) {
        // If an element with text-transform or a block element is found
        if (transformElement.style.textTransform === transform) {
          transformElement.style.textTransform = ''; // Toggle off
          // If it was a span and now has no style, unwrap it
          if (transformElement.tagName === 'SPAN' && !transformElement.getAttribute('style')) {
            const parent = transformElement.parentNode;
            while (transformElement.firstChild) {
              parent?.insertBefore(transformElement.firstChild, transformElement);
            }
            parent?.removeChild(transformElement);
          }
        } else {
          transformElement.style.textTransform = transform; // Apply new transform
        }
      } else {
        // If no relevant element found, create a new span at cursor position
        const span = document.createElement('span');
        span.style.textTransform = transform;
        range.insertNode(span);
        // Move cursor inside the new span
        range.setStart(span, 0);
        range.collapse(true);
        sel.removeAllRanges();
        sel.addRange(range);
      }
    }
    this.updateValue();
  }

  setTextColor(color: string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('styleWithCSS', false, 'true');
    document.execCommand('foreColor', false, color);
    this.selectedTextColor = color; // Update the state for the palette button
    this.updateValue();
  }

  clearFormatting() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return;
    const range = sel.getRangeAt(0);
    const editor = this.editorRef.nativeElement as HTMLElement;

    // Check if the selection is within the editor
    if (!editor.contains(range.commonAncestorContainer)) {
      console.warn("Selection is outside the editor, cannot clear formatting.");
      return;
    }


    // Use execCommand for basic formatting removal
    try {
      document.execCommand('removeFormat', false, '');
      // This removes basic inline styles and tags like <b>, <i>, <u>, <font>, etc.
    } catch (e) {
      console.warn("removeFormat execCommand failed:", e);
      // Fallback or additional manual removal might be needed for complex cases
    }


    // Manual removal for styles/elements not handled by removeFormat (like custom spans, divs with styles)
    // Iterate through nodes in the selection and remove inline styles/classes
    if (!range.collapsed) {
      const fragment = range.cloneContents();
      const elements = fragment.querySelectorAll('*');
      elements.forEach(el => {
        const htmlEl = el as HTMLElement;
        htmlEl.removeAttribute('style');
        htmlEl.removeAttribute('class');
        // Remove specific unwanted tags if necessary (e.g., SPANs with only style)
        if (htmlEl.tagName === 'SPAN' && !htmlEl.attributes.length) { // If span has no attributes left
          const parent = htmlEl.parentElement;
          while (htmlEl.firstChild) parent?.insertBefore(htmlEl.firstChild, htmlEl);
          parent?.removeChild(htmlEl);
        }
      });

      // Replace the content in the editor with the cleaned fragment
      range.deleteContents();
      range.insertNode(fragment);


    } else {
      // If collapsed selection, remove styles from parent elements near cursor
      let node = sel.focusNode as HTMLElement | null;
      if (node && node.nodeType === 3) node = node.parentElement!;

      let checkNode = node;
      while (checkNode && checkNode !== editor) {
        if (checkNode.nodeType === 1) {
          const htmlEl = checkNode as HTMLElement;
          htmlEl.removeAttribute('style');
          htmlEl.removeAttribute('class');
          // Remove specific unwanted tags if necessary (e.g., SPANs with only style)
          if (htmlEl.tagName === 'SPAN' && !htmlEl.attributes.length) {
            const parent = htmlEl.parentElement;
            while (htmlEl.firstChild) parent?.insertBefore(htmlEl.firstChild, htmlEl);
            parent?.removeChild(htmlEl);
            // After removing the node, update checkNode to parent to continue upwards
            checkNode = parent;
            continue; // Continue the loop with the parent
          }
        }
        checkNode = checkNode.parentElement!;
      }
    }

    this.updateValue();
  }

  cropSelectedImage() {
    // If no selectedImage, try to auto-select the first image in the editor (if any)
    if (!this.selectedImage) {
      const editor = this.editorRef?.nativeElement as HTMLElement;
      if (editor) {
        const imgs = editor.querySelectorAll('img');
        if (imgs.length > 0) {
          this.selectImage(imgs[0]);
        }
      }
    }
    if (!this.selectedImage) {
      console.warn("No image selected or found to crop.");
      return;
    }
    console.log('Crop selected image', this.selectedImage);
    this.removeCropOverlay();
    this.cropping = true;

    const img = this.selectedImage;
    // Đảm bảo ảnh đã được render (có width/height > 0)
    if (img.width === 0 || img.height === 0) {
      img.onload = () => this.cropSelectedImage();
      return;
    }
    const parent = img.parentElement as HTMLElement;
    if (!parent || !this.editorRef.nativeElement.contains(parent)) { // Ensure parent is valid and in editor
      console.error("Image parent not found or not in editor.");
      return;
    }
    if (getComputedStyle(parent).position === 'static') {
      parent.style.position = 'relative';
    }

    // Đảm bảo overlay không bị che bởi các handle resize
    this.resizeHandles.forEach(h => h.style.display = 'none');

    // Xóa overlay cũ nếu còn tồn tại (phòng trường hợp DOM lỗi)
    const oldOverlay = parent.querySelector('.img-crop-overlay');
    console.log('Old overlay', oldOverlay);
    if (oldOverlay) oldOverlay.remove();

    const overlay = document.createElement('div');
    overlay.className = 'img-crop-overlay';
    overlay.style.position = 'absolute';
    overlay.style.left = `${img.offsetLeft}px`;
    overlay.style.top = `${img.offsetTop}px`;
    overlay.style.width = `${img.width}px`;
    overlay.style.height = `${img.height}px`;
    overlay.style.background = 'rgba(0,0,0,0.15)';
    overlay.style.cursor = 'crosshair';
    overlay.style.zIndex = '1002';
    overlay.style.pointerEvents = 'auto';

    // Đảm bảo overlay nằm trên ảnh và trong parent
    parent.appendChild(overlay);

    const cropRectDiv = document.createElement('div');
    cropRectDiv.className = 'img-crop-rect';
    cropRectDiv.style.position = 'absolute';
    cropRectDiv.style.border = '2px dashed #3182ce';
    cropRectDiv.style.background = 'rgba(255,255,255,0.2)';
    cropRectDiv.style.display = 'none';
    cropRectDiv.style.pointerEvents = 'auto';
    overlay.appendChild(cropRectDiv);

    let isDrawing = false;
    overlay.onmousedown = (e: MouseEvent) => {
      // Nếu click vào button thì không xử lý
      if ((e.target as HTMLElement).classList.contains('img-crop-btn')) {
        return;
      }
      if (e.button !== 0) return;
      e.preventDefault();
      e.stopPropagation();
      isDrawing = true;
      const overlayRect = overlay.getBoundingClientRect();
      this.cropStartX = e.clientX - overlayRect.left;
      this.cropStartY = e.clientY - overlayRect.top;
      cropRectDiv.style.display = 'block';
      const moveHandler = (ev: MouseEvent) => {
        if (!isDrawing) return;
        const moveX = ev.clientX - overlayRect.left;
        const moveY = ev.clientY - overlayRect.top;
        let x1 = this.cropStartX;
        let y1 = this.cropStartY;
        let x2 = moveX;
        let y2 = moveY;
        let left = Math.min(x1, x2);
        let top = Math.min(y1, y2);
        let width = Math.abs(x2 - x1);
        let height = Math.abs(y2 - y1);
        cropRectDiv.style.left = `${left}px`;
        cropRectDiv.style.top = `${top}px`;
        cropRectDiv.style.width = `${width}px`;
        cropRectDiv.style.height = `${height}px`;
        this.cropRect = { left, top, width, height };
      };
      const upHandler = (ev: MouseEvent) => {
        isDrawing = false;
        document.removeEventListener('mousemove', moveHandler);
        document.removeEventListener('mouseup', upHandler);
        // Only show confirm button if a valid crop area was drawn
        if (this.cropRect.width > 5 && this.cropRect.height > 5) {
          this.showCropConfirmButton(parent, overlay, cropRectDiv);
        } else {
          // If crop area is too small, just remove overlay
          this.removeCropOverlay();
        }

      };
      document.addEventListener('mousemove', moveHandler);
      document.addEventListener('mouseup', upHandler);
    };

    this.cropOverlay = overlay;
  }

  private showCropConfirmButton(parent: HTMLElement, overlay: HTMLDivElement, cropRectDiv: HTMLDivElement) {
    // Xóa các nút cũ nếu có
    Array.from(overlay.querySelectorAll('.img-crop-btn')).forEach(btn => btn.remove());

    // Lưu lại reference ảnh tại thời điểm crop
    const img = this.selectedImage;
    if (!img) {
      console.error("Image reference lost for crop confirm.");
      this.removeCropOverlay();
      return;
    }

    // Nút xác nhận crop
    let btn = document.createElement('button');
    btn.textContent = 'Cắt ảnh';
    btn.className = 'img-crop-btn';
    btn.style.position = 'absolute';
    // Position relative to the cropRectDiv
    btn.style.left = `${this.cropRect.left + this.cropRect.width - 60}px`;
    btn.style.top = `${this.cropRect.top + this.cropRect.height + 8}px`;
    btn.style.zIndex = '1003';
    btn.style.background = '#3182ce';
    btn.style.color = '#fff';
    btn.style.border = 'none';
    btn.style.borderRadius = '4px';
    btn.style.padding = '2px 10px';
    btn.style.cursor = 'pointer';
    btn.style.pointerEvents = 'auto';
    btn.onmouseover = () => { btn.style.backgroundColor = '#2b6cb0'; }
    btn.onmouseout = () => { btn.style.backgroundColor = '#3182ce'; }

    btn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (this.cropRect.width > 0 && this.cropRect.height > 0 && img) {
        this.doCropImageWithRef(img, this.cropRect);
        this.removeCropOverlay();
      }
    });

    overlay.appendChild(btn);

    // Nút hủy
    let btnCancel = document.createElement('button');
    btnCancel.textContent = 'Hủy';
    btnCancel.className = 'img-crop-btn';
    btnCancel.style.position = 'absolute';
    // Position relative to the cropRectDiv
    btnCancel.style.left = `${this.cropRect.left + 4}px`;
    btnCancel.style.top = `${this.cropRect.top + this.cropRect.height + 8}px`;
    btnCancel.style.zIndex = '1003';
    btnCancel.style.background = '#aaa';
    btnCancel.style.color = '#fff';
    btnCancel.style.border = 'none';
    btnCancel.style.borderRadius = '4px';
    btnCancel.style.padding = '2px 10px';
    btnCancel.style.cursor = 'pointer';
    btnCancel.style.pointerEvents = 'auto';
    btnCancel.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.removeCropOverlay();
    });

    overlay.appendChild(btnCancel);

    // Allow interactions with buttons on the overlay
    overlay.style.pointerEvents = 'auto';
    cropRectDiv.style.pointerEvents = 'none'; // Disable pointer events on the crop rectangle itself after drawing
  }

  private doCropImageWithRef(img: HTMLImageElement, cropRect: { left: number, top: number, width: number, height: number }) {
    if (!img || !img.parentElement || !this.editorRef.nativeElement.contains(img)) {
      console.error('Invalid image element for cropping');
      return;
    }
    const { left, top, width, height } = cropRect;
    if (width < 5 || height < 5) {
      console.error('Crop dimensions too small:', { width, height });
      return;
    }

    const tempImg = new Image();
    tempImg.crossOrigin = 'anonymous'; // This is needed to avoid CORS issues when drawing to canvas
    tempImg.onload = () => {
      try {
        const displayWidth = img.width || img.naturalWidth;
        const displayHeight = img.height || img.naturalHeight;
        const naturalWidth = tempImg.naturalWidth;
        const naturalHeight = tempImg.naturalHeight;

        if (displayWidth === 0 || displayHeight === 0 || naturalWidth === 0 || naturalHeight === 0) {
          console.error("Image dimensions are zero, cannot crop.");
          return;
        }


        const scaleX = naturalWidth / displayWidth;
        const scaleY = naturalHeight / displayHeight;

        const cropX = Math.round(left * scaleX);
        const cropY = Math.round(top * scaleY);
        const cropW = Math.round(width * scaleX);
        const cropH = Math.round(height * scaleY);

        // Ensure crop dimensions are within natural image bounds
        const safeCropX = Math.max(0, cropX);
        const safeCropY = Math.max(0, cropY);
        const safeCropW = Math.max(1, Math.min(cropW, naturalWidth - safeCropX));
        const safeCropH = Math.max(1, Math.min(cropH, naturalHeight - safeCropY));


        const canvas = document.createElement('canvas');
        canvas.width = safeCropW;
        canvas.height = safeCropH;
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          console.error("Could not get canvas context.");
          return;
        }

        // Draw the cropped section of the image onto the canvas
        ctx.drawImage(
          tempImg,
          safeCropX, safeCropY, safeCropW, safeCropH, // Source rectangle
          0, 0, safeCropW, safeCropH // Destination rectangle
        );

        // Update the original image's source and dimensions
        img.src = canvas.toDataURL('image/png'); // Use png to preserve transparency if any
        img.width = safeCropW;
        img.height = safeCropH;

        // After cropping, reselect the image to show updated handles
        this.selectImage(img);

        this.updateValue();

      } catch (e) {
        console.error("Error during image cropping:", e);
      }

    };
    // Handle potential errors during image loading
    tempImg.onerror = (e) => {
      console.error("Error loading image for cropping:", e);
    };

    // Start loading the image
    tempImg.src = img.src;
  }


  private removeCropOverlay() {
    if (this.cropOverlay && this.cropOverlay.parentElement) {
      this.cropOverlay.parentElement.removeChild(this.cropOverlay);
    }
    // Hiện lại các handle resize nếu có
    this.resizeHandles.forEach(h => h.style.display = '');
    this.cropOverlay = null;
    this.cropping = false;
    // No need to updateValue here, it's done after cropping
  }


  // Khi click vào vùng soạn thảo, cập nhật currentFontSize, currentFontFamily, selectedTextColor theo vị trí con trỏ
  onEditorClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const img = target.closest('img');

    if (img && this.editorRef.nativeElement.contains(img)) { // Ensure image is within editor
      this.selectedImage = img as HTMLImageElement;
      this.selectImage(img as HTMLImageElement);
    } else {
      this.selectedImage = null;
      this.removeImageSelection();
    }

    // Use a timeout to allow selection to update after the click
    setTimeout(() => {
      this.updateSelectedValues(); // Reuse the existing method to update toolbar state
    }, 50); // Small delay might help with selection updates
  }

  // Helper: chuyển rgb/rgba sang hex
  private rgbToHex(color: string): string {
    // Handle non-rgb/rgba cases (like keyword colors or empty strings)
    if (!color || typeof color !== 'string' || !color.startsWith('rgb')) return '#000000'; // Default to black

    // Nếu đã là hex thì trả về luôn
    if (color.startsWith('#')) return color;
    // Nếu là rgb/rgba
    const rgbMatch = color.match(/\d+/g);
    if (!rgbMatch || rgbMatch.length < 3) return '#000000'; // Default to black if not enough color components

    let r = parseInt(rgbMatch[0], 10);
    let g = parseInt(rgbMatch[1], 10);
    let b = parseInt(rgbMatch[2], 10);
    // ignore alpha

    // Ensure values are within 0-255 range
    r = Math.max(0, Math.min(255, r));
    g = Math.max(0, Math.min(255, g));
    b = Math.max(0, Math.min(255, b));


    return (
      '#' +
      [r, g, b]
        .map(x => {
          const hex = x.toString(16);
          return hex.length === 1 ? '0' + hex : hex;
        })
        .join('')
    );
  }

  setColumns(event: Event) {
    const value = +(event.target as HTMLSelectElement).value;
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return;
    const range = sel.getRangeAt(0);
    const editor = this.editorRef.nativeElement as HTMLElement;


    if (!range.collapsed) {
      // Nếu có vùng chọn, bọc vùng chọn bằng 1 div và set column
      const wrapper = document.createElement('div');
      if (value && value > 1) {
        wrapper.style.columnCount = value.toString();
        wrapper.style.columnGap = '32px';
        wrapper.style.outline = '1px dashed #ccc'; // Add visual indicator for columns

        try {
          // Extract contents might fail if selection is complex
          const fragment = range.extractContents();
          wrapper.appendChild(fragment);
          range.insertNode(wrapper);

          // Place cursor at the end of the inserted wrapper
          const newRange = document.createRange();
          newRange.selectNodeContents(wrapper);
          newRange.collapse(false);
          sel.removeAllRanges();
          sel.addRange(newRange);


        } catch {
          console.warn("Could not extractContents for column formatting. Falling back or doing nothing.");
          // document.execCommand('styleWithCSS', false, 'true'); // This might not be sufficient for columns
          // Consider a more robust manual DOM manipulation or informing the user
          wrapper.remove(); // Clean up the created wrapper
        }
      } else {
        // Nếu chọn lại 1 cột, bỏ chia cột cho vùng chọn (unwrap div nếu có)
        // Tìm div wrapper gần nhất trong vùng chọn và bỏ style column
        let node = range.commonAncestorContainer as HTMLElement | null;
        if (node && node.nodeType === 3) node = node.parentElement!;
        while (node && node !== editor) {
          if (node.tagName === 'DIV' && node.style.columnCount) {
            node.style.columnCount = '';
            node.style.columnGap = '';
            node.style.outline = ''; // Remove outline

            // If div has no other styles or attributes, unwrap
            if (!node.getAttribute('style') && !node.getAttribute('class')) { // Also check for class
              const parent = node.parentNode;
              while (node.firstChild) parent?.insertBefore(node.firstChild, node);
              parent?.removeChild(node);
              // After unwrapping, update node to parent to continue check upwards
              node = parent as HTMLElement | null;
              continue; // Continue the loop with the parent
            }
            break; // Stop at the nearest relevant div
          }
          node = node.parentElement!;
        }
      }
    } else {
      // Nếu không chọn gì, áp dụng cho toàn bộ editor (simplified)
      // This might not be ideal as it affects the whole contenteditable area
      const editor = this.editorRef.nativeElement as HTMLElement;
      if (value && value > 1) {
        editor.style.columnCount = value.toString();
        editor.style.columnGap = '32px';
        editor.style.outline = '1px dashed #ccc'; // Add visual indicator
      } else {
        editor.style.columnCount = '';
        editor.style.columnGap = '';
        editor.style.outline = ''; // Remove outline
      }
    }
    this.updateValue();
  }

  onHeadingChange(event: Event) {
    const value = (event.target as HTMLSelectElement).value;
    if (value) {
      this.insertHeading(Number(value));
      // Reset select về mặc định
      (event.target as HTMLSelectElement).value = '';
    }
  }

  private mousedownHandler = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    // If cropping, do not remove selection when clicking crop overlay or crop button
    if (this.cropping) {
      // If click on crop overlay or crop rect or crop button, do not clear selection
      if (
        target.classList.contains('img-crop-overlay') ||
        target.classList.contains('img-crop-rect') ||
        (target.tagName === 'BUTTON' && (target.textContent === 'Cắt ảnh' || target.textContent === 'Hủy'))
      ) {
        return;
      }
    }
    // Remove old image selection if click outside the image and resize handles
    if (this.selectedImage && target !== this.selectedImage && !this.isResizeHandle(target) && !target.closest('.editor-image-toolbar')) {
      this.removeImageSelection();
    }
    // Select image if an image is clicked within the editor
    if (target && target.tagName === 'IMG' && this.editorRef.nativeElement.contains(target)) {
      this.selectImage(target as HTMLImageElement);
    } else {
      // If click outside image, also hide the toolbar
      this.showImageToolbar = false;
      this.imageToolbarTarget = null;
    }

    // Hide table resize handles if click outside a table
    if (!target.closest('table')) {
      this.removeTableResizeHandles();
    }
  };

  private blurHandler = (e: FocusEvent) => {
    // This blur handler might be less relevant with the new onBlur logic using requestAnimationFrame
    // Keep it for now but review if it's still needed or causing conflicts
    // const target = e.target as HTMLElement;
    // if (target && target.tagName === 'IMG') {
    //   // delay removal to allow selecting toolbar buttons
    //   setTimeout(() => {
    //     if (document.activeElement !== target.closest('.editor-image-toolbar')) {
    //        this.removeImageSelection();
    //     }
    //   }, 50);
    // }
  };

  private isResizeHandle(el: HTMLElement): boolean {
    return el.classList.contains('img-resize-handle');
  }

  private selectImage(img: HTMLImageElement) {
    this.removeImageSelection(); // Remove any previously selected image handles
    if (!this.editorRef.nativeElement.contains(img)) {
      console.warn("Attempted to select image not within the editor.");
      this.selectedImage = null;
      return;
    }

    this.selectedImage = img;
    img.classList.add('img-selected');
    img.setAttribute('contenteditable', 'false'); // Prevent editing image as text
    img.style.cursor = 'nwse-resize'; // Default cursor for resize
    img.style.resize = 'none'; // disable native resize

    // Ensure the image's parent is relatively positioned to correctly position handles
    const parent = img.parentElement as HTMLElement;
    if (parent && getComputedStyle(parent).position === 'static') {
      parent.style.position = 'relative';
    }


    // Create 4 handles
    const positions: Array<'nw' | 'ne' | 'sw' | 'se'> = ['nw', 'ne', 'sw', 'se'];
    positions.forEach(pos => {
      const handle = document.createElement('div');
      handle.className = `img-resize-handle img-resize-handle-${pos}`;
      handle.style.position = 'absolute';
      handle.style.width = '10px';
      handle.style.height = '10px';
      handle.style.background = '#fff';
      handle.style.border = '2px solid #3182ce';
      handle.style.borderRadius = '2px';
      handle.style.boxSizing = 'border-box';
      handle.style.zIndex = '10'; // Ensure handles are above image
      handle.style.cursor =
        pos === 'nw' ? 'nwse-resize' :
          pos === 'se' ? 'nwse-resize' :
            pos === 'ne' ? 'nesw-resize' :
              'nesw-resize';
      handle.dataset['position'] = pos;
      // Position handle
      this.positionHandle(img, handle, pos);
      // Mouse events
      handle.addEventListener('mousedown', this.handleResizeStart);
      // Insert handle to DOM
      parent?.appendChild(handle); // Append to the same parent as the image
      this.resizeHandles.push(handle);
    });

    // Listen for window resize and scroll to reposition handles
    window.addEventListener('resize', this.handleWindowResize);
    document.addEventListener('scroll', this.handleWindowResize, true); // Use capture phase for scroll
  }

  private removeImageSelection() {
    if (this.selectedImage) {
      this.selectedImage.classList.remove('img-selected');
      this.selectedImage.removeAttribute('contenteditable');
      this.selectedImage.style.cursor = ''; // Reset cursor
      // Remove handles
      this.resizeHandles.forEach(h => {
        h.removeEventListener('mousedown', this.handleResizeStart);
        if (h.parentElement) { // Check if handle is still attached before removing
          h.parentElement.removeChild(h);
        }
      });
      this.resizeHandles = [];
      this.selectedImage = null; // Clear selected image reference
      // No need to updateValue here
    }
    window.removeEventListener('resize', this.handleWindowResize);
    document.removeEventListener('scroll', this.handleWindowResize, true);
    this.showImageToolbar = false; // Hide toolbar when selection is removed
    this.imageToolbarTarget = null; // Clear toolbar target
  }

  private positionHandle(img: HTMLImageElement, handle: HTMLDivElement, pos: string) {
    // Đảm bảo parent là relative để handle định vị đúng
    const parent = img.parentElement as HTMLElement;
    if (!parent || !this.editorRef.nativeElement.contains(parent)) {
      console.warn("Cannot position handle, image parent is invalid or not in editor.");
      return;
    }

    if (getComputedStyle(parent).position === 'static') {
      parent.style.position = 'relative';
    }
    // Get actual image position and dimensions relative to the parent
    const imgRect = img.getBoundingClientRect();
    const parentRect = parent.getBoundingClientRect();

    const imgLeft = imgRect.left - parentRect.left;
    const imgTop = imgRect.top - parentRect.top;
    const imgWidth = img.offsetWidth; // Use offsetWidth/Height for rendered size
    const imgHeight = img.offsetHeight;


    // Đặt vị trí handle theo góc, tính cả handle size (10px) and border (2px)
    const handleSize = 14; // handle width + border
    const handleOffset = handleSize / 2; // offset from corner


    switch (pos) {
      case 'nw':
        handle.style.left = `${imgLeft - handleOffset}px`;
        handle.style.top = `${imgTop - handleOffset}px`;
        break;
      case 'ne':
        handle.style.left = `${imgLeft + imgWidth - handleOffset}px`;
        handle.style.top = `${imgTop - handleOffset}px`;
        break;
      case 'sw':
        handle.style.left = `${imgLeft - handleOffset}px`;
        handle.style.top = `${imgTop + imgHeight - handleOffset}px`;
        break;
      case 'se':
        handle.style.left = `${imgLeft + imgWidth - handleOffset}px`;
        handle.style.top = `${imgTop + imgHeight - handleOffset}px`;
        break;
    }
    // Ensure handle positions are updated relative to the parent
    handle.style.position = 'absolute';
  }

  private handleWindowResize = () => {
    if (this.selectedImage) {
      // Update image selection handles positions
      this.resizeHandles.forEach(handle => {
        if (this.selectedImage) { // Ensure selectedImage is still valid
          this.positionHandle(this.selectedImage, handle, handle.dataset['position']!);
        }
      });
      // Update image toolbar position if visible
      if (this.showImageToolbar && this.imageToolbarTarget) {
        const container = this.editorRef.nativeElement.parentElement;
        const rect = this.imageToolbarTarget.getBoundingClientRect();
        if (!container) return;
        const containerRect = container.getBoundingClientRect();
        this.imageToolbarPosition = this.getSafeToolbarPosition(rect, containerRect, 220, 40); // Adjust toolbar size if needed
      }

      // No need to call updateValue here, this is just positioning
    }
  };

  private resizing = false;
  private resizeStartX = 0;
  private resizeStartY = 0;
  private resizeStartWidth = 0;
  private resizeStartHeight = 0;
  private resizeDirection: string = '';

  private handleResizeStart = (e: MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!this.selectedImage) return;
    const handle = e.target as HTMLDivElement;
    this.resizing = true;
    this.resizeStartX = e.clientX;
    this.resizeStartY = e.clientY;
    // Use offsetWidth/Height for current rendered size
    this.resizeStartWidth = this.selectedImage.offsetWidth;
    this.resizeStartHeight = this.selectedImage.offsetHeight;
    this.resizeDirection = handle.dataset['position']!;
    document.addEventListener('mousemove', this.handleResizing);
    document.addEventListener('mouseup', this.handleResizeEnd);
  };

  private handleResizing = (e: MouseEvent) => {
    if (!this.resizing || !this.selectedImage) return;
    const editor = this.editorRef.nativeElement as HTMLElement;
    const img = this.selectedImage;

    let dx = e.clientX - this.resizeStartX;
    let dy = e.clientY - this.resizeStartY;
    let newWidth = this.resizeStartWidth;
    let newHeight = this.resizeStartHeight;

    // Adjust delta based on resize direction
    if (this.resizeDirection.includes('e')) newWidth += dx;
    if (this.resizeDirection.includes('w')) newWidth -= dx;
    if (this.resizeDirection.includes('s')) newHeight += dy;
    if (this.resizeDirection.includes('n')) newHeight -= dy;


    newWidth = Math.max(20, newWidth); // Minimum size
    newHeight = Math.max(20, newHeight);


    // Optionally maintain aspect ratio (if shift key is pressed)
    const maintainAspectRatio = e.shiftKey;
    if (maintainAspectRatio && this.resizeStartWidth > 0 && this.resizeStartHeight > 0) {
      const aspectRatio = this.resizeStartWidth / this.resizeStartHeight;
      if (Math.abs(dx) > Math.abs(dy)) { // Based on horizontal movement
        newHeight = newWidth / aspectRatio;
      } else { // Based on vertical movement
        newWidth = newHeight * aspectRatio;
      }
      newWidth = Math.max(20, newWidth);
      newHeight = Math.max(20, newHeight);
    }


    img.width = newWidth;
    img.height = newHeight;


    this.handleWindowResize(); // Update handle positions
    this.updateValue(); // Update the editor content value
  };

  private handleResizeEnd = () => {
    this.resizing = false;
    document.removeEventListener('mousemove', this.handleResizing);
    document.removeEventListener('mouseup', this.handleResizeEnd);
    // After resizing, ensure the selectedImage is still valid and handles are positioned
    if (this.selectedImage) {
      this.selectImage(this.selectedImage); // Re-select to refresh handles and state
    }
  };

  insertHeading(level: number) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    if (!level || isNaN(+level) || level < 1 || level > 6) { // Validate level
      console.warn("Invalid heading level:", level);
      return;
    }
    // Check if the current selection/block is already the target heading level
    const sel = window.getSelection();
    if (sel && sel.rangeCount > 0) {
      let node = sel.getRangeAt(0).commonAncestorContainer as HTMLElement | null;
      if (node && node.nodeType === 3) node = node.parentElement!;

      while (node && node !== this.editorRef.nativeElement) {
        if (node.tagName === `H${level}`) {
          // If already the target heading, convert back to paragraph
          document.execCommand('formatBlock', false, 'p');
          this.updateValue();
          return;
        }
        // If it's a different heading, apply the new one
        if (/^H[1-6]$/i.test(node.tagName)) {
          document.execCommand('formatBlock', false, 'H' + level);
          this.updateValue();
          return;
        }
        node = node.parentElement!;
      }
    }

    // If not already a heading, apply the command
    document.execCommand('formatBlock', false, 'H' + level);
    this.updateValue();
  }

  insertTable(rows?: number, cols?: number) {
    console.log('insertTable');
    if (!rows || !cols) {
      this.saveSelection(); // Save selection before potentially opening form
    }

    if (!rows || !cols || rows <= 0 || cols <= 0) { // Validate input
      this.tableForm = { rows: 2, cols: 2 };
      this.showTableForm = true;
      return; // Exit if input is invalid or form is shown
    }

    this.restoreSelection && this.restoreSelection(); // Restore selection before inserting HTML

    const sel = window.getSelection();
    const range = sel?.getRangeAt(0);

    if (!range || !this.editorRef.nativeElement.contains(range.commonAncestorContainer)) {
      console.warn("Cannot insert table, invalid selection or selection outside editor.");
      this.showTableForm = false; // Close form if it was open
      return;
    }


    // Thêm div bọc với overflow-x để hỗ trợ bảng rộng hơn editor
    let html = `
      <div style="overflow-x: auto; width: 100%; padding: 10px">
        <table style="border-collapse: collapse; border: 1px solid #ccc; min-width: ${cols * 80}px;">
    `; // Calculate initial min-width based on columns

    for (let i = 0; i < rows; i++) {
      html += '<tr>';
      for (let j = 0; j < cols; j++) {
        html += '<td style="border: 1px solid #ccc; padding: 8px 12px; min-width: 80px; min-height: 24px;">&nbsp;</td>'; // Added padding
      }
      html += '</tr>';
    }

    html += `
        </table>
      </div>
    `;

    // Using insertHTML can be tricky with selections, insert as text first then convert to HTML?
    // Or rely on execCommand which might be inconsistent. Let's stick to insertHTML for now but ensure selection is active.
    try {
      document.execCommand('insertHTML', false, html);
      // Need a small delay to ensure DOM is updated before adding handles
      setTimeout(() => {
        this.addTableResizeHandlesToLastTable();
        this.updateValue();
        this.showTableForm = false; // Close form after successful insertion
      });

    } catch (e) {
      console.error("Error inserting table HTML:", e);
      this.showTableForm = false; // Close form on error
    }

  }


  // Xác nhận chèn bảng với số dòng/cột do người dùng nhập
  confirmInsertTable() {
    // The logic is now directly in insertTable, just call it with form values
    this.insertTable(this.tableForm.rows, this.tableForm.cols);
    // The form closing is handled within insertTable now
  }

  // Hủy form chèn bảng
  cancelInsertTable() {
    this.showTableForm = false;
    this.savedRange = null; // Clear saved range if cancelling form
  }

  private addTableResizeHandlesToLastTable() {
    const editor = this.editorRef?.nativeElement as HTMLElement;
    if (!editor) return;
    // Find the last added table within an overflow div, or just the last table
    const overflowDivs = editor.querySelectorAll('div[style*="overflow-x: auto"]');
    let table: HTMLTableElement | null = null;
    if (overflowDivs.length > 0) {
      const lastDiv = overflowDivs[overflowDivs.length - 1];
      table = lastDiv.querySelector('table');
    }
    // Fallback to finding the last table if no overflow div found or table inside not found
    if (!table) {
      const tables = editor.querySelectorAll('table');
      if (tables.length > 0) {
        table = tables[tables.length - 1] as HTMLTableElement;
      }
    }

    if (table && editor.contains(table)) { // Ensure the table is in the editor
      this.addTableResizeHandles(table);
    } else {
      console.warn("Could not find a table in the editor to add resize handles.");
    }
  }

  private addTableResizeHandles(table: HTMLTableElement) {
    this.removeTableResizeHandles(); // Remove any existing handles first
    if (!this.editorRef.nativeElement.contains(table)) {
      console.warn("Attempted to add handles to table not within the editor.");
      return;
    }

    this.resizingTableEl = table;
    const rows = Array.from(table.rows);
    if (rows.length === 0) return;

    // Ensure table's parent (or table itself if no wrapper) is relative for handle positioning
    let positionContainer = table.parentElement;
    if (!positionContainer || getComputedStyle(positionContainer).position === 'static') {
      positionContainer = table; // Position handles relative to table if parent is static
      if (getComputedStyle(positionContainer).position === 'static') {
        positionContainer.style.position = 'relative';
      }
    }


    // Add column resize handles to the first row
    const firstRow = rows[0];
    for (let col = 0; col < firstRow.cells.length; col++) {
      const cell = firstRow.cells[col];
      if (!cell) continue; // Skip if cell doesn't exist (shouldn't happen in a valid table row)

      const handle = document.createElement('div');
      handle.className = 'table-col-resize-handle';
      handle.style.position = 'absolute';
      handle.style.width = '6px';
      handle.style.top = '0';
      handle.style.right = '-3px'; // Position relative to cell right edge
      handle.style.bottom = '0';
      handle.style.cursor = 'col-resize';
      handle.style.zIndex = '100';
      handle.style.background = 'rgba(49,130,206,0.15)';
      handle.dataset['colIdx'] = col.toString();
      handle.onmousedown = (e: MouseEvent) => this.startColResize(e, col, table);

      // Position handle relative to the cell (which should be relative)
      if (getComputedStyle(cell).position === 'static') {
        cell.style.position = 'relative';
      }
      cell.appendChild(handle);
      this.tableResizeHandles.push(handle);
    }
    // Add row resize handles to the last column of each row
    // This might be complex, let's simplify and add handles to the right edge of the table container instead
    // or add handles to the bottom edge of the table container for total height resize


    // Add handle for total table resize (bottom right corner)
    const resizeTableHandle = document.createElement('div');
    resizeTableHandle.className = 'table-total-resize-handle';
    resizeTableHandle.style.position = 'absolute';
    resizeTableHandle.style.width = '14px';
    resizeTableHandle.style.height = '14px';
    resizeTableHandle.style.right = '-7px'; // Position relative to container right edge
    resizeTableHandle.style.bottom = '-7px'; // Position relative to container bottom edge
    resizeTableHandle.style.background = '#3182ce';
    resizeTableHandle.style.border = '2px solid #fff';
    resizeTableHandle.style.borderRadius = '4px';
    resizeTableHandle.style.cursor = 'nwse-resize';
    resizeTableHandle.style.zIndex = '200';
    resizeTableHandle.title = 'Resize toàn bảng';
    resizeTableHandle.onmousedown = (e: MouseEvent) => this.startTableTotalResize(e, table);
    // Append total resize handle to the position container (parent or table itself)
    positionContainer.appendChild(resizeTableHandle);
    this.tableResizeHandles.push(resizeTableHandle);

    // Note: Row specific resizing is more complex and might require a different approach
    // or adding handles to the bottom edge of each row (last cell). Let's stick to column and total resize for now.

  }

  private removeTableResizeHandles() {
    this.tableResizeHandles.forEach(h => {
      if (h.parentElement) { // Check if handle is attached before removing
        h.parentElement.removeChild(h);
      }
    });
    this.tableResizeHandles = [];
    this.resizingTableEl = null;
    // No need to call updateValue here
  }

  private startColResize(e: MouseEvent, colIdx: number, table: HTMLTableElement) {
    console.log('startColResize');
    e.preventDefault();
    e.stopPropagation(); // Stop propagation to prevent onEditorClick from removing handles immediately
    this.resizingTable = true;
    this.resizingColIdx = colIdx;
    this.resizingRowIdx = -1;
    this.resizingTableEl = table;
    this.startX = e.clientX;
    // Use offsetWidth of the cell in the first row for the starting width
    const cell = table.rows[0]?.cells[colIdx];
    if (!cell) {
      console.error("Could not find cell for column resize start.");
      this.stopTableResize();
      return;
    }
    this.startWidth = cell.offsetWidth;
    document.addEventListener('mousemove', this.handleColResizing);
    document.addEventListener('mouseup', this.stopTableResize);
  }

  private handleColResizing = (e: MouseEvent) => {
    if (!this.resizingTable || !this.resizingTableEl || this.resizingColIdx === -1) return;
    const dx = e.clientX - this.startX;
    const newWidth = Math.max(24, this.startWidth + dx); // Minimum column width
    for (let row of Array.from(this.resizingTableEl.rows)) {
      const cell = row.cells[this.resizingColIdx];
      if (cell) {
        cell.style.width = newWidth + 'px';
        cell.style.minWidth = newWidth + 'px'; // Set min-width to prevent shrinking below resized size
      }
    }
    this.updateValue();
  };

  // Row resizing logic remains complex, skipping for now to focus on selection issues.
  // private startRowResize(e: MouseEvent, rowIdx: number, table: HTMLTableElement) { ... }
  // private handleRowResizing = (e: MouseEvent) => { ... }


  private stopTableResize = () => {
    this.resizingTable = false;
    this.resizingColIdx = -1;
    this.resizingRowIdx = -1;
    document.removeEventListener('mousemove', this.handleColResizing);
    document.removeEventListener('mouseup', this.stopTableResize);
    // After resizing, re-add handles to ensure correct positioning
    if (this.resizingTableEl) {
      this.addTableResizeHandles(this.resizingTableEl);
    }
    this.resizingTableEl = null; // Clear the element reference
  };

  private startTableTotalResize(e: MouseEvent, table: HTMLTableElement) {
    e.preventDefault();
    e.stopPropagation();
    this.resizingTableTotal = true;
    this.resizingTableEl = table;
    this.tableTotalStartX = e.clientX;
    this.tableTotalStartY = e.clientY;
    // Use offsetWidth/Height for current rendered size
    this.tableTotalStartWidth = table.offsetWidth;
    this.tableTotalStartHeight = table.offsetHeight;
    document.addEventListener('mousemove', this.handleTableTotalResizing);
    document.addEventListener('mouseup', this.stopTableTotalResize);
  }

  private handleTableTotalResizing = (e: MouseEvent) => {
    if (!this.resizingTableTotal || !this.resizingTableEl) return;
    const dx = e.clientX - this.tableTotalStartX;
    const dy = e.clientY - this.tableTotalStartY;
    const newWidth = Math.max(60, this.tableTotalStartWidth + dx);
    const newHeight = Math.max(32, this.tableTotalStartHeight + dy);

    // Apply width and height to the table
    this.resizingTableEl.style.width = newWidth + 'px';
    this.resizingTableEl.style.height = newHeight + 'px'; // Note: height on table might not work as expected, might need to adjust row heights

    // Optionally, if table is wrapped in overflow div, resize the div instead/as well
    // const wrapper = this.resizingTableEl.parentElement;
    // if (wrapper && wrapper.tagName === 'DIV' && wrapper.style.overflowX === 'auto') {
    //   wrapper.style.width = newWidth + 'px';
    //   // Height on wrapper might not be necessary unless overflow-y is used
    // }


    this.updateValue();
  };

  private stopTableTotalResize = () => {
    this.resizingTableTotal = false;
    document.removeEventListener('mousemove', this.handleTableTotalResizing);
    document.removeEventListener('mouseup', this.stopTableTotalResize);
    // After resizing, re-add handles to ensure correct positioning
    if (this.resizingTableEl) {
      this.addTableResizeHandles(this.resizingTableEl);
    }
    this.resizingTableEl = null; // Clear the element reference
  };

  private onEditorTableClick = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    const table = target.closest('table'); // Find the nearest table ancestor

    if (table && this.editorRef.nativeElement.contains(table)) { // Ensure table is in editor
      // Xóa các handle cũ trước khi thêm handle mới
      this.removeTableResizeHandles();
      // Thêm handle mới cho table được click
      this.addTableResizeHandles(table);
      e.stopPropagation(); // Stop propagation to prevent document click from removing handles immediately
    } else {
      this.removeTableResizeHandles(); // Remove handles if click is outside a table
    }
  };

  // Ẩn handle resize bảng khi click ra ngoài bảng (already handled by mousedownHandler)
  // private handleTableResizeBlur = (e: MouseEvent) => { ... };


  insertCodeBlock() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    // Check if the current selection/block is already a pre block
    const sel = window.getSelection();
    if (sel && sel.rangeCount > 0) {
      let node = sel.getRangeAt(0).commonAncestorContainer as HTMLElement | null;
      if (node && node.nodeType === 3) node = node.parentElement!;

      while (node && node !== this.editorRef.nativeElement) {
        if (node.tagName === 'PRE') {
          // If already a pre, convert back to paragraph
          document.execCommand('formatBlock', false, 'p');
          this.updateValue();
          return;
        }
        node = node.parentElement!;
      }
    }
    document.execCommand('formatBlock', false, 'pre');
    this.updateValue();
  }

  insertSpecialChar(char: string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('insertText', false, char);
    this.closePickers();
    this.updateValue();
  }

  insertBlockquote() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return;
    const range = sel.getRangeAt(0);
    const editor = this.editorRef.nativeElement as HTMLElement;


    // Check if the selection is within the editor
    if (!editor.contains(range.commonAncestorContainer)) {
      console.warn("Selection is outside the editor, cannot insert blockquote.");
      return;
    }


    // Check if the current selection/block is already in a blockquote
    let node = range.commonAncestorContainer as HTMLElement | null;
    if (node && node.nodeType === 3) node = node.parentElement!;
    let blockquote = null;
    while (node && node !== editor) {
      if (node.tagName === 'BLOCKQUOTE') {
        blockquote = node;
        break;
      }
      node = node.parentElement!;
    }

    if (blockquote) {
      // If already a blockquote, convert its content back to paragraphs
      const parent = blockquote.parentElement;
      if (parent) {
        // Create a temporary div to hold and process the content
        const temp = document.createElement('div');
        temp.innerHTML = blockquote.innerHTML;

        // Convert child blockquotes within the temporary content to paragraphs
        const childBlockquotes = temp.querySelectorAll('blockquote');
        childBlockquotes.forEach(child => {
          const p = document.createElement('p');
          p.innerHTML = child.innerHTML || '&nbsp;'; // Preserve content, add nbsp if empty
          child.parentNode?.replaceChild(p, child);
        });

        // Extract the processed content from the temporary div
        const fragment = document.createDocumentFragment();
        while (temp.firstChild) {
          fragment.appendChild(temp.firstChild);
        }

        // Replace the original blockquote with the processed content
        parent.insertBefore(fragment, blockquote);
        parent.removeChild(blockquote);

        // Attempt to place cursor after the inserted content
        const newRange = document.createRange();
        newRange.setStartAfter(parent.lastChild || parent); // Place after the last inserted node or parent
        newRange.collapse(true);
        sel.removeAllRanges();
        sel.addRange(newRange);


      }
    } else {
      // If not a blockquote, convert the selection or current block into a blockquote
      if (!range.collapsed) {
        // If there is a selection, wrap it in a blockquote
        const newBlockquote = document.createElement('blockquote');
        newBlockquote.style.margin = '1em 0';
        newBlockquote.style.padding = '0 1em';
        newBlockquote.style.borderLeft = '4px solid #e2e8f0';
        newBlockquote.style.color = '#4a5568';
        newBlockquote.style.fontStyle = 'italic'; // Often blockquotes are italicized


        try {
          // Extract selection contents and put into blockquote
          const fragment = range.extractContents();
          newBlockquote.appendChild(fragment);
          range.insertNode(newBlockquote);

          // Place cursor at the end of the inserted blockquote
          const newRange = document.createRange();
          newRange.selectNodeContents(newBlockquote);
          newRange.collapse(false);
          sel.removeAllRanges();
          sel.addRange(newRange);


        } catch (e) {
          console.warn("Could not surround contents with blockquote:", e);
          // Fallback if surrounding fails - might just insert empty blockquote
          newBlockquote.innerHTML = '&nbsp;'; // Insert with placeholder
          range.insertNode(newBlockquote);

          // Place cursor inside the new blockquote
          const newRange = document.createRange();
          newRange.selectNodeContents(newBlockquote);
          newRange.collapse(true);
          sel.removeAllRanges();
          sel.addRange(newRange);

        }
      } else {
        // If no selection, apply formatBlock to the current block
        document.execCommand('formatBlock', false, 'blockquote');
        // After execCommand, find the newly created blockquote and apply styles
        setTimeout(() => { // Use timeout as execCommand might be async
          const blockquotes = editor.getElementsByTagName('blockquote');
          // Find the blockquote that contains the current selection/cursor
          let targetBlockquote: HTMLElement | null = null;
          const currentRange = window.getSelection()?.getRangeAt(0);
          if (currentRange) {
            let checkNode = currentRange.commonAncestorContainer as HTMLElement | null;
            if (checkNode && checkNode.nodeType === 3) checkNode = checkNode.parentElement!;
            while (checkNode && checkNode !== editor) {
              if (checkNode.tagName === 'BLOCKQUOTE') {
                targetBlockquote = checkNode;
                break;
              }
              checkNode = checkNode.parentElement!;
            }
          }


          if (targetBlockquote) {
            targetBlockquote.style.margin = '1em 0';
            targetBlockquote.style.padding = '0 1em';
            targetBlockquote.style.borderLeft = '4px solid #e2e8f0';
            targetBlockquote.style.color = '#4a5568';
            targetBlockquote.style.fontStyle = 'italic';
          } else {
            console.warn("Could not find the newly created blockquote to style.");
          }
          this.updateValue();
        });

      }
    }
    // updateValue is called within the success/error paths
  }

  onLineHeightChange(value: string) {
    this.selectedLineHeight = value;
    this.setLineHeight(value);
  }

  setLineHeight(lineHeight: string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return;

    const range = sel.getRangeAt(0);
    const editor = this.editorRef.nativeElement as HTMLElement;


    // Apply line height to the block element(s) containing the selection
    if (!range.collapsed) {
      // If there is a selection, find all block elements (P, DIV, LI, Hx, Blockquote) that intersect the range
      const blocks: HTMLElement[] = [];
      const walker = document.createTreeWalker(editor, NodeFilter.SHOW_ELEMENT, {
        acceptNode: (node) =>
          /^(P|DIV|LI|BLOCKQUOTE|H[1-6])$/i.test(node.nodeName) && range.intersectsNode(node) && editor.contains(node)
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_SKIP,
      });

      let node = walker.nextNode() as HTMLElement;
      while (node) {
        blocks.push(node);
        node = walker.nextNode() as HTMLElement;
      }

      if (blocks.length > 0) {
        blocks.forEach(block => block.style.lineHeight = lineHeight);
      } else {
        // If no blocks found in selection, try applying to the common ancestor's block
        let commonAncestor = range.commonAncestorContainer as HTMLElement;
        if (commonAncestor.nodeType === 3) commonAncestor = commonAncestor.parentElement!;
        const parentBlock = commonAncestor.closest('p, div, li, blockquote, h1, h2, h3, h4, h5, h6') as HTMLElement;
        if (parentBlock && editor.contains(parentBlock)) {
          parentBlock.style.lineHeight = lineHeight;
        } else {
          console.warn("Could not find a block element in the selection or near common ancestor to set line height.");
        }
      }

    } else {
      // If no selection (collapsed range), apply line height to the parent block element
      let node = range.startContainer as HTMLElement | null;
      if (node && node.nodeType === 3) node = node.parentElement;

      const parentBlock = node?.closest('p, div, li, blockquote, h1, h2, h3, h4, h5, h6') as HTMLElement;
      if (parentBlock && editor.contains(parentBlock)) {
        parentBlock.style.lineHeight = lineHeight;
      } else {
        console.warn("Could not find a parent block element to set line height.");
      }
    }
    this.updateValue();
  }


  insertAutoNumber() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('insertOrderedList');
    // No need for setTimeout and styling here as execCommand handles default numbering
    this.updateValue();
  }

  insertCurrentDateTime() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const now = new Date();
    const str = now.toLocaleString();
    document.execCommand('insertText', false, str);
    this.updateValue();
  }

  insertVideo(url: string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    const range = sel?.getRangeAt(0);

    if (!range || !this.editorRef.nativeElement.contains(range.commonAncestorContainer)) {
      console.warn("Cannot insert video, invalid selection or selection outside editor.");
      return;
    }


    let embed = '';
    // Use a wrapper div to center the video with max width and responsive sizing
    const style = 'margin: 0px auto; max-width: 100%; width: 100%; display: flex; justify-content: center;';
    let videoUrl = (url || '').trim(); // Trim the URL


    if (!videoUrl) {
      console.warn("Video URL is empty.");
      return;
    }


    if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
      let videoId = '';
      if (videoUrl.includes('youtu.be/')) {
        videoId = videoUrl.split('youtu.be/')[1].split(/[?&]/)[0];
      } else {
        const match = videoUrl.match(/[?&]v=([^&]+)/);
        if (match) videoId = match[1];
      }
      if (videoId) {
        embed = `<div style="${style}"><iframe width="560" height="315" src="https://www.youtube.com/embed/${videoId}" frameborder="0" allowfullscreen></iframe></div>`; // Increased size slightly
      } else {
        console.warn("Could not extract YouTube video ID from URL:", videoUrl);
      }
    } else {
      // For other URLs, just embed directly in an iframe, wrapped in the centering div
      embed = `<div style="${style}"><iframe src="${videoUrl}" width="560" height="315" frameborder="0" allowfullscreen></iframe></div>`; // Increased size
      console.warn("Non-YouTube video URL provided. Embedding directly, but compatibility may vary:", videoUrl);
    }


    if (embed) {
      document.execCommand('insertHTML', false, embed);
      this.updateValue();
    }

    // Clear the video input field
    // This might be done in the template or the calling component, but clearing here is also an option
    this.videoForm.url = '';
  }

  openVideoForm() {
    this.videoForm.url = '';
    this.showVideoForm = true;
    // Lưu selection để chèn đúng vị trí sau khi nhập xong
    this.saveSelection(); // Explicitly save before opening form
  }

  confirmInsertVideo() {
    // The logic is now directly in insertVideo, just call it with form value
    this.insertVideo(this.videoForm.url);
    this.showVideoForm = false;
    this.savedRange = null; // Clear saved range after insertion/cancel
  }

  cancelInsertVideo() {
    this.showVideoForm = false;
    this.savedRange = null; // Clear saved range if cancelling form
  }

  countWordsAndChars(): { words: number, chars: number } {
    const text = (this.editorRef?.nativeElement as HTMLElement)?.innerText || '';
    const words = text.trim().split(/\s+/).filter(Boolean).length;
    const chars = text.replace(/\s/g, '').length;
    return { words, chars };
  }

  insertChecklist() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return;
    const editor = this.editorRef.nativeElement as HTMLElement;

    // Find the nearest UL or OL ancestor
    let node = sel.focusNode as HTMLElement | null;
    if (node && node.nodeType === 3) node = node.parentElement;
    let listNode: HTMLElement | null = null;
    while (node && node !== editor && node.tagName !== 'UL' && node.tagName !== 'OL') {
      node = node.parentElement;
    }
    listNode = node;


    if (listNode && (listNode.tagName === 'UL' || listNode.tagName === 'OL')) {
      const list = listNode as HTMLElement; // Can be UL or OL
      const isChecklist = list.querySelector('li input[type="checkbox"]');

      if (isChecklist) {
        // If already a checklist (or contains checkboxes), remove checkboxes and reset list style
        list.querySelectorAll('li').forEach(li => {
          const input = li.querySelector('input[type="checkbox"]');
          if (input) {
            input.remove();
            // Clean up empty LIs or ensure they have content
            if (!li.textContent?.trim() && li.children.length === 0) {
              li.innerHTML = '&nbsp;';
            }
          }
        });
        list.style.listStyleType = list.tagName === 'UL' ? 'disc' : 'decimal'; // Restore default list style
        this.updateValue();
        return;
      } else {
        // If it's a regular list (UL/OL) but not a checklist, convert it to checklist
        list.querySelectorAll('li').forEach(li => {
          if (!li.querySelector('input[type="checkbox"]')) {
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.style.marginRight = '8px';
            checkbox.addEventListener('click', (e) => e.stopPropagation()); // Prevent checkbox click from affecting editor selection immediately
            li.insertBefore(checkbox, li.firstChild);
          }
        });
        list.style.listStyleType = 'none'; // Remove bullets/numbers
        this.updateValue();
        return;
      }
    } else {
      // If not inside a list, convert the current selection/block into a checklist
      // This is similar to inserting an unordered list, then adding checkboxes
      document.execCommand('insertUnorderedList');
      setTimeout(() => {
        // After execCommand, find the newly created UL (it should contain the selection)
        const updatedSel = window.getSelection();
        if (updatedSel && updatedSel.rangeCount > 0) {
          let currentFocusNode = updatedSel.focusNode as HTMLElement | null;
          if (currentFocusNode && currentFocusNode.nodeType === 3) currentFocusNode = currentFocusNode.parentElement!;

          const newUl = currentFocusNode?.closest('ul');

          if (newUl && editor.contains(newUl)) { // Ensure the new UL is in the editor
            newUl.querySelectorAll('li').forEach(li => {
              if (!li.querySelector('input[type="checkbox"]')) {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.style.marginRight = '8px';
                checkbox.addEventListener('click', (e) => e.stopPropagation()); // Prevent checkbox click from affecting editor selection immediately
                li.insertBefore(checkbox, li.firstChild);
              }
            });
            newUl.style.listStyleType = 'none'; // Remove bullets
            this.updateValue();
          } else {
            console.warn("Could not find the newly created UL to convert to checklist.");
          }
        } else {
          console.warn("Could not get selection after inserting unordered list for checklist conversion.");
        }
      });
    }
  }

  ngAfterViewInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      // Use a single mousedown listener on the document for handling various clicks
      document.addEventListener('mousedown', this.mousedownHandler);

      // The blur handler might be redundant or needs careful review with the new onBlur logic
      // document.addEventListener('blur', this.blurHandler, true); // capture phase

      // Initialize undoStack with initial content
      if (this.editorRef && this.editorRef.nativeElement) {
        // Use a timeout to ensure initial content is rendered before capturing it
        setTimeout(() => {
          this.undoStack = [this.editorRef.nativeElement.innerHTML];
        });

        // Listen for clicks on the editor content itself for image/table selection and updates
        this.editorRef.nativeElement.addEventListener('click', this.onEditorContentClick);


        // Lắng nghe chuột phải trên ảnh để hiện toolbar nổi (already handled by onEditorContentClick)
        // this.editorRef.nativeElement.addEventListener('contextmenu', this.onEditorContextMenu);
      }

      // Add drag & drop for selected images (already implemented)
      this.setupImageDragDrop();

      // Handle clicks outside image toolbar to hide it (already implemented)
      document.addEventListener('mousedown', this.hideImageToolbarOnClick, true); // Use capture phase

      // Add a general keydown listener on the editor for handling specific keys like Delete/Backspace
      this.editorRef.nativeElement.addEventListener('keydown', this.onEditorKeydown);
    }
  }

  // New handler for clicks inside the editor content
  private onEditorContentClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    const editor = this.editorRef.nativeElement as HTMLElement;

    // Handle Image Selection and Toolbar
    const img = target.closest('img');
    if (img && editor.contains(img)) {
      this.selectImage(img as HTMLImageElement);
      // Show image toolbar on image click (not just right click)
      this.showImageToolbar = true;
      this.imageToolbarTarget = img as HTMLImageElement;
      // Position toolbar - use requestAnimationFrame to ensure image position is stable
      requestAnimationFrame(() => {
        const container = editor.parentElement;
        const rect = img.getBoundingClientRect();
        if (!container) return;
        const containerRect = container.getBoundingClientRect();
        this.imageToolbarPosition = this.getSafeToolbarPosition(rect, containerRect, 220, 40); // Adjust toolbar size if needed
      });
      event.stopPropagation(); // Stop propagation to prevent document mousedown from removing selection
    } else {
      this.selectedImage = null;
      this.removeImageSelection();
      this.showImageToolbar = false;
      this.imageToolbarTarget = null;
    }

    // Handle Table Selection and Handles
    const table = target.closest('table');
    if (table && editor.contains(table)) {
      this.removeTableResizeHandles(); // Remove existing before adding
      this.addTableResizeHandles(table as HTMLTableElement);
      event.stopPropagation(); // Stop propagation
    } else {
      this.removeTableResizeHandles(); // Remove handles if click outside table
    }

    // Update toolbar state based on selection/cursor position
    // Use a small delay to allow browser selection to update
    setTimeout(() => this.updateSelectedValues(), 50);

  }

  // New handler for keydown events in the editor
  private onEditorKeydown = (e: KeyboardEvent) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const editor = this.editorRef.nativeElement as HTMLElement;

    // Handle Delete/Backspace on selected Image
    if ((e.key === 'Delete' || e.key === 'Backspace') && this.selectedImage) {
      if (editor.contains(this.selectedImage)) {
        e.preventDefault(); // Prevent default delete behavior
        this.selectedImage.remove();
        this.removeImageSelection(); // Remove handles and clear reference
        this.updateValue(); // Update content value
      } else {
        // If selectedImage is somehow not in the editor, clear the reference
        this.selectedImage = null;
        this.removeImageSelection();
      }
    }
    // Consider adding more keydown handlers here if needed (e.g., Enter for new list item, Tab for table navigation)
  }


  private setupImageDragDrop() {
    const editor = this.editorRef?.nativeElement as HTMLElement;
    if (!editor) return;

    // Allow images within the editor to be draggable
    editor.querySelectorAll('img').forEach(img => {
      img.setAttribute('draggable', 'true');
    });

    // Event listener for dragstart on images within the editor
    editor.addEventListener('dragstart', (e: DragEvent) => {
      const target = e.target as HTMLElement;
      if (target && target.tagName === 'IMG' && editor.contains(target)) { // Ensure target is an image in the editor
        // Set data to be transferred (outerHTML of the image)
        e.dataTransfer?.setData('text/html', target.outerHTML);
        // Indicate that this is a move operation
        e.dataTransfer!.effectAllowed = 'move';
        // Add a class to visually indicate dragging
        target.classList.add('dragging');
        // Store the image being dragged (optional, but can be useful)
        this.selectedImage = target as HTMLImageElement;
        // Remove handles during drag
        this.removeImageSelection(); // This will also hide the toolbar

      } else {
        // Do not allow dragging of other elements
        e.preventDefault();
      }
    });

    // Event listener for dragover to allow dropping in the editor
    editor.addEventListener('dragover', (e: DragEvent) => {
      e.preventDefault(); // Allow drop
      // Indicate the drop effect
      if (e.dataTransfer) {
        e.dataTransfer.dropEffect = 'move';
      }
    });

    // Event listener for drop in the editor
    editor.addEventListener('drop', (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation(); // Stop propagation to prevent other drop handlers

      // Get the dropped data
      const data = e.dataTransfer?.getData('text/html');
      if (data) {
        // Create a temporary element to parse the HTML string
        const temp = document.createElement('div');
        temp.innerHTML = data;
        const droppedImage = temp.querySelector('img');

        if (droppedImage) {
          // If an image was dropped

          // Remove the original image if it was dragged from within the editor
          if (this.selectedImage && this.selectedImage.classList.contains('dragging') && this.selectedImage.parentElement) {
            this.selectedImage.parentElement.removeChild(this.selectedImage);
          }
          this.selectedImage = null; // Clear reference to the old image


          // Get the range at the drop point
          const range = document.caretRangeFromPoint
            ? document.caretRangeFromPoint(e.clientX, e.clientY)
            : (function () {
              const sel = window.getSelection();
              if (sel && sel.rangeCount > 0) return sel.getRangeAt(0);
              return null;
            })();

          if (range && editor.contains(range.commonAncestorContainer)) { // Ensure drop point is in the editor
            // Insert the dropped image at the drop point
            range.insertNode(droppedImage);

            // Optionally, select the newly dropped image
            this.selectImage(droppedImage);
            this.updateValue(); // Update content value

            // Prevent default behavior that might insert as a link
            return;
          } else {
            console.warn("Invalid drop point for image.");
          }

        } else {
          console.warn("Dropped data does not contain an image.");
        }

      } else {
        console.warn("No data transferred during drop.");
      }
      // Clear dragging class from any image that might still have it
      editor.querySelectorAll('img.dragging').forEach(img => img.classList.remove('dragging'));
    });

    // Event listener for dragend to clean up
    editor.addEventListener('dragend', (e: DragEvent) => {
      const target = e.target as HTMLElement;
      if (target && target.tagName === 'IMG') {
        // Remove the dragging class
        target.classList.remove('dragging');
        // After drag ends (whether successful drop or not), re-select the image if it's still in the DOM
        if (editor.contains(target)) {
          this.selectImage(target as HTMLImageElement);
        } else {
          this.removeImageSelection(); // Remove handles if image was moved out
        }
      }
    });

    // Ensure all existing and newly added images are draggable
    // This might require observing DOM changes if images are added dynamically after init
  }
  private getSafeToolbarPosition(rect: DOMRect, containerRect: DOMRect, toolbarWidth = 220, toolbarHeight = 40) {
    let left = rect.left - containerRect.left + rect.width / 2 - toolbarWidth / 2;
    let top = rect.top - containerRect.top - toolbarHeight - 8; // 8px above the image

    // Adjust if it goes out of bounds horizontally relative to the container
    if (left < 0) left = 0;
    if (left + toolbarWidth > containerRect.width) left = containerRect.width - toolbarWidth;

    // Adjust if it goes out of bounds vertically relative to the container
    // If there's not enough space above, position it below
    if (top < 0) {
      top = rect.bottom - containerRect.top + 8; // 8px below the image
      // Check if positioning below also goes out of bounds below
      if (top + toolbarHeight > containerRect.height) {
        // If it doesn't fit below either, try positioning at the top edge of the container
        top = 0;
      }
    }


    // Ensure the toolbar stays within the visible part of the container's scroll area (basic check)
    const editorScrollTop = this.editorRef.nativeElement.parentElement?.scrollTop || 0;
    const editorScrollLeft = this.editorRef.nativeElement.parentElement?.scrollLeft || 0;

    // Adjust based on scroll position
    // This is a simplified approach and might need refinement for complex scrolling
    // The current positioning is relative to the top-left of the containerRect, which should account for container scroll
    // However, if the image is scrolled out of view *within* the editable area's scroll container, the toolbar might still appear outside.
    // More complex logic would involve checking visibility and adjusting relative to the viewport or the visible part of the container.

    // For now, let's ensure it's not negative relative to container top/left
    left = Math.max(0, left);
    top = Math.max(0, top);


    return { top, left };
  }
  private onEditorContextMenu = (event: MouseEvent) => {
    // This is now handled by onEditorContentClick for left clicks
    // Keep this for right-click context menu if different behavior is needed
    // For now, prevent default context menu on images and potentially show custom toolbar
    const target = event.target as HTMLElement;
    if (target && target.tagName === 'IMG' && this.editorRef.nativeElement.contains(target)) {
      event.preventDefault(); // Prevent default browser context menu
      this.selectImage(target as HTMLImageElement);
      // Optionally, show a custom context menu or the same toolbar
      this.showImageToolbar = true;
      this.imageToolbarTarget = target as HTMLImageElement;

      const container = this.editorRef.nativeElement.parentElement;
      const rect = target.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();

      // Position toolbar - use event clientX/Y for context menu position
      this.imageToolbarPosition = {
        top: event.clientY - containerRect.top,
        left: event.clientX - containerRect.left
      };
      // Adjust position to ensure it stays within container bounds
      this.imageToolbarPosition = this.getSafeToolbarPositionFromPoint(
        event.clientX, event.clientY, containerRect, 220, 40
      );


      console.log('Image toolbar position (context menu):', this.imageToolbarPosition);
    } else {
      this.showImageToolbar = false;
      this.imageToolbarTarget = null;
    }
  };

  // Helper to get safe toolbar position based on a point (like mouse click)
  private getSafeToolbarPositionFromPoint(clientX: number, clientY: number, containerRect: DOMRect, toolbarWidth = 220, toolbarHeight = 40) {
    let left = clientX - containerRect.left;
    let top = clientY - containerRect.top;

    // Adjust if it goes out of bounds horizontally
    if (left + toolbarWidth > containerRect.width) left = containerRect.width - toolbarWidth;
    if (left < 0) left = 0;


    // Adjust if it goes out of bounds vertically
    if (top + toolbarHeight > containerRect.height) top = containerRect.height - toolbarHeight;
    if (top < 0) top = 0;


    return { top, left };
  }


  private hideImageToolbarOnClick = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    // Check if the click is outside the toolbar and outside an image
    if (
      !target.closest('.editor-image-toolbar') && // Click is not inside the toolbar
      !(target.tagName === 'IMG') && // Click is not directly on an image
      !target.closest('.img-resize-handle') // Click is not on a resize handle
    ) {
      this.showImageToolbar = false;
      this.imageToolbarTarget = null;
    }
  };

  ngOnDestroy(): void {
    if (isPlatformBrowser(this.platformId)) {
      // Remove event listeners added to the document
      document.removeEventListener('mousedown', this.mousedownHandler);
      // document.removeEventListener('blur', this.blurHandler, true); // Review if still needed
      document.removeEventListener('mousedown', this.hideImageToolbarOnClick, true);

      // Remove event listeners added to the editor element
      if (this.editorRef && this.editorRef.nativeElement) {
        this.editorRef.nativeElement.removeEventListener('click', this.onEditorContentClick);
        // this.editorRef.nativeElement.removeEventListener('contextmenu', this.onEditorContextMenu); // If context menu is handled
        this.editorRef.nativeElement.removeEventListener('keydown', this.onEditorKeydown);

        // Clean up any draggable attributes added to images
        this.editorRef.nativeElement.querySelectorAll('img').forEach((img: HTMLImageElement) => {
          img.removeAttribute('draggable');
        });
        // Remove drag/drop listeners from the editor
        this.editorRef.nativeElement.removeEventListener('dragstart', (e: DragEvent) => { }); // Needs to be the exact same function reference
        this.editorRef.nativeElement.removeEventListener('dragover', (e: DragEvent) => { });
        this.editorRef.nativeElement.removeEventListener('drop', (e: DragEvent) => { });
        this.editorRef.nativeElement.removeEventListener('dragend', (e: DragEvent) => { });

      }


      // Ensure all dynamically created elements (handles, overlays) are removed
      this.removeImageSelection();
      this.removeCropOverlay();
      this.removeTableResizeHandles();


      // Clear saved range and other state
      this.savedRange = null;
      this.selectedImage = null;
      this.resizingTableEl = null;
      this.imageToolbarTarget = null;

    }
  }

  // ====== TABLE ROW/COLUMN/DELETE FUNCTIONS ======
  insertTableRowAbove() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const cell = this.getCurrentTableCell();
    if (cell && cell.parentElement && cell.parentElement.parentElement) {
      const row = cell.parentElement as HTMLTableRowElement;
      const tableBody = row.parentElement as HTMLTableSectionElement; // This should be TBODY or THEAD/TFOOT
      const newRow = row.cloneNode(true) as HTMLTableRowElement;

      // Xóa nội dung các ô và style
      Array.from(newRow.cells).forEach(td => {
        td.innerHTML = '&nbsp;';
        // Giữ lại border style từ cell gốc
        const originalCell = row.cells[td.cellIndex];
        if (originalCell) {
          const borderStyle = originalCell.style.border;
          if (borderStyle) {
            td.style.border = borderStyle;
          }
        }
      });

      // Giữ lại style của row gốc
      const originalRowStyle = row.getAttribute('style');
      if (originalRowStyle) {
        newRow.setAttribute('style', originalRowStyle);
      }

      tableBody.insertBefore(newRow, row);
      this.updateValue();
      // Re-add table resize handles as the table structure changed
      const table = tableBody.parentElement as HTMLTableElement;
      if (table) this.addTableResizeHandles(table);

    } else {
      console.warn("Could not find current table cell to insert row above.");
    }
  }

  insertTableRowBelow() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const cell = this.getCurrentTableCell();
    if (cell && cell.parentElement && cell.parentElement.parentElement) {
      const row = cell.parentElement as HTMLTableRowElement;
      const tableBody = row.parentElement as HTMLTableSectionElement; // This should be TBODY
      const newRow = row.cloneNode(true) as HTMLTableRowElement;
      // Xóa nội dung các ô và style
      Array.from(newRow.cells).forEach(td => {
        td.innerHTML = '&nbsp;';
        const originalCell = row.cells[td.cellIndex];
        if (originalCell) {
          const borderStyle = originalCell.style.border;
          if (borderStyle) {
            td.style.border = borderStyle;
          }
        }
      });


      if (row.nextSibling) {
        tableBody.insertBefore(newRow, row.nextSibling);
      } else {
        tableBody.appendChild(newRow);
      }
      this.updateValue();
      // Re-add table resize handles
      const table = tableBody.parentElement as HTMLTableElement;
      if (table) this.addTableResizeHandles(table);
    } else {
      console.warn("Could not find current table cell to insert row below.");
    }
  }

  insertTableColLeft() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const cell = this.getCurrentTableCell();
    if (!cell) {
      console.warn("Could not find current table cell to insert column left.");
      return;
    }
    const colIdx = cell.cellIndex;
    const row = cell.parentElement as HTMLTableRowElement;
    const tableBody = row.parentElement as HTMLTableSectionElement;
    const tableEl = tableBody.parentElement as HTMLTableElement; // This should be TABLE

    if (!tableEl || !this.editorRef.nativeElement.contains(tableEl)) {
      console.warn("Could not find parent table element for column insertion.");
      return;
    }


    Array.from(tableEl.rows).forEach(r => {
      // Find the reference cell in each row at the target column index
      const refCell = r.cells[colIdx];
      // Create a new cell (clone refCell to copy its type (TD/TH) and some properties)
      const newCell = refCell ? refCell.cloneNode(false) as HTMLTableCellElement : document.createElement('td'); // Clone false to not copy children
      newCell.innerHTML = '&nbsp;'; // Add placeholder content



      if (refCell) {
        // Insert the new cell before the reference cell
        r.insertBefore(newCell, refCell);
      } else {
        // If refCell doesn't exist (shouldn't happen in a valid table), append to the row
        r.appendChild(newCell);
      }
    });
    this.updateValue();
    // Re-add table resize handles as columns changed
    this.addTableResizeHandles(tableEl);
  }

  insertTableColRight() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const cell = this.getCurrentTableCell();
    if (!cell) {
      console.warn("Could not find current table cell to insert column right.");
      return;
    }
    const colIdx = cell.cellIndex;
    const row = cell.parentElement as HTMLTableRowElement;
    const tableBody = row.parentElement as HTMLTableSectionElement;
    const tableEl = tableBody.parentElement as HTMLTableElement; // This should be TABLE

    if (!tableEl || !this.editorRef.nativeElement.contains(tableEl)) {
      console.warn("Could not find parent table element for column insertion.");
      return;
    }


    Array.from(tableEl.rows).forEach(r => {
      // Find the reference cell in each row at the target column index
      const refCell = r.cells[colIdx];
      // Create a new cell (clone refCell)
      const newCell = refCell ? refCell.cloneNode(false) as HTMLTableCellElement : document.createElement('td'); // Clone false to not copy children
      newCell.innerHTML = '&nbsp;'; // Add placeholder content


      if (refCell && refCell.nextSibling) {
        // Insert the new cell before the next sibling of the reference cell
        r.insertBefore(newCell, refCell.nextSibling);
      } else {
        // If no next sibling or refCell doesn't exist, append to the row
        r.appendChild(newCell);
      }
    });
    this.updateValue();
    // Re-add table resize handles
    this.addTableResizeHandles(tableEl);
  }

  deleteTableRow() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const cell = this.getCurrentTableCell();
    if (cell && cell.parentElement && cell.parentElement.parentElement) {
      const row = cell.parentElement as HTMLTableRowElement;
      const tableBody = row.parentElement as HTMLTableSectionElement; // This should be TBODY
      const table = tableBody.parentElement as HTMLTableElement; // Get the table before removing the row

      if (!table || !this.editorRef.nativeElement.contains(table)) {
        console.warn("Could not find parent table element for row deletion.");
        return;
      }


      tableBody.removeChild(row);

      // If the table becomes empty after deleting the row, remove the table too
      if (tableBody.rows.length === 0) {
        this.deleteTable(table); // Use helper to delete the table and its wrapper
      } else {
        this.updateValue();
        // Re-add table resize handles as row count changed
        this.addTableResizeHandles(table);
      }

    } else {
      console.warn("Could not find current table cell to delete row.");
    }
  }

  deleteTableCol() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    const cell = this.getCurrentTableCell();
    if (!cell) {
      console.warn("Could not find current table cell to delete column.");
      return;
    }
    const colIdx = cell.cellIndex;
    const row = cell.parentElement as HTMLTableRowElement;
    const tableBody = row.parentElement as HTMLTableSectionElement;
    const tableEl = tableBody.parentElement as HTMLTableElement; // This should be TABLE

    if (!tableEl || !this.editorRef.nativeElement.contains(tableEl)) {
      console.warn("Could not find parent table element for column deletion.");
      return;
    }

    // Check if it's the last column
    const isLastCol = tableEl.rows.length > 0 && tableEl.rows[0].cells.length === 1;


    Array.from(tableEl.rows).forEach(r => {
      if (r.cells[colIdx]) r.deleteCell(colIdx);
    });

    // If it was the last column, delete the entire table
    if (isLastCol) {
      this.deleteTable(tableEl); // Use helper to delete the table and its wrapper
    } else {
      this.updateValue();
      // Re-add table resize handles as column count changed
      this.addTableResizeHandles(tableEl);
    }
  }

  // Modified deleteTable to accept the table element directly
  deleteTable(tableToDelete?: HTMLTableElement | null) {
    const cell = this.getCurrentTableCell();
    let table: HTMLElement | null = tableToDelete ?? cell?.closest('table') ?? null; // Use provided table or find from cell

    if (table && this.editorRef.nativeElement.contains(table)) { // Ensure table is in editor
      let wrapper = table.parentElement;
      // If TABLE is inside a DIV with overflow-x, remove the wrapper
      if (wrapper && wrapper.tagName === 'DIV' && wrapper.style.overflowX === 'auto' && this.editorRef.nativeElement.contains(wrapper)) {
        if (wrapper.parentElement) {
          wrapper.parentElement.removeChild(wrapper);
          this.updateValue();
          this.removeTableResizeHandles(); // Remove handles after deleting table
          return;
        }
      }
      // If no wrapper or wrapper is not the overflow div, just remove the TABLE
      if (table.parentElement) {
        table.parentElement.removeChild(table);
        this.updateValue();
        this.removeTableResizeHandles(); // Remove handles after deleting table
      }
    } else {
      console.warn("Could not find a table in the editor to delete.");
    }
  }


  // Helper: Lấy ô hiện tại (td/th) theo selection
  private getCurrentTableCell(): HTMLTableCellElement | null {
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return null;
    // Use commonAncestorContainer to find a cell that contains the entire selection (or cursor)
    let node = sel.getRangeAt(0).commonAncestorContainer as HTMLElement | null;
    if (node && node.nodeType === 3) node = node.parentElement; // Move up to the element node

    // Traverse up the DOM tree to find the nearest TD or TH ancestor
    while (node && node.tagName !== 'TD' && node.tagName !== 'TH') {
      node = node.parentElement;
    }
    // Ensure the found cell is within the editor
    if (node && this.editorRef.nativeElement.contains(node)) {
      return node as HTMLTableCellElement;
    }
    return null; // Return null if no cell is found or it's outside the editor
  }

  // Thêm các phương thức mới
  private adjustPickerPosition(rect: DOMRect, containerRect: DOMRect, pickerWidth: number = 300, pickerHeight: number = 400): { top: number, left: number } {
    // Position picker relative to the container
    let left = rect.left - containerRect.left;
    let top = rect.bottom - containerRect.top + 8; // Position 8px below the button

    // Ensure picker stays within container bounds
    // Check horizontal bounds
    if (left + pickerWidth > containerRect.width) {
      left = containerRect.width - pickerWidth; // Align right edge with container right edge
    }
    if (left < 0) left = 0; // Prevent going beyond left edge


    // Check vertical bounds
    if (top + pickerHeight > containerRect.height) {
      // If it doesn't fit below, try positioning above the button
      top = rect.top - containerRect.top - pickerHeight - 8; // Position 8px above the button
      // If it still doesn't fit above (e.g., button is at the very top), position at container top edge
      if (top < 0) top = 0;
    }


    return { top, left };
  }

  openEmojiPicker(event: MouseEvent) {
    const button = event.currentTarget as HTMLElement;
    const container = button.parentElement; // Assuming toolbar is the container
    if (!container) {
      console.warn("Emoji button container not found.");
      return;
    }
    const rect = button.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();


    this.emojiPickerPosition = this.adjustPickerPosition(rect, containerRect, 300, 300); // Assuming emoji picker size
    this.showEmojiPicker = true;
    this.showSpecialCharPicker = false;
    event.stopPropagation(); // Prevent document click from closing immediately
  }

  openSpecialCharPicker(event: MouseEvent) {
    const button = event.currentTarget as HTMLElement;
    const container = button.parentElement; // Assuming toolbar is the container
    if (!container) {
      console.warn("Special char button container not found.");
      return;
    }
    const rect = button.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();


    this.specialCharPickerPosition = this.adjustPickerPosition(rect, containerRect, 300, 300); // Assuming special char picker size
    this.showSpecialCharPicker = true;
    this.showEmojiPicker = false;
    event.stopPropagation(); // Prevent document click from closing immediately
  }

  closePickers() {
    this.showEmojiPicker = false;
    this.showSpecialCharPicker = false;
  }



  insertEmoji(emoji: string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('insertText', false, emoji);
    this.closePickers();
    this.updateValue();
  }


  onPaletteColorMouseDown(event: MouseEvent, color: string) {
    event.preventDefault(); // Prevent blur/selection loss
    event.stopPropagation(); // Prevent document click
    this.setTextColorFromPalette(color); // Use the modified function
    // Palette will be closed by setTextColorFromPalette
  }

  setTextColorFromPalette(color: string) {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('styleWithCSS', false, 'true');
    document.execCommand('foreColor', false, color);
    this.selectedTextColor = color;
    this.closeTextColorPalette(); // Close palette here
    this.updateValue();
  }

  clearTextColor() {
    this.restoreSelectionOrUseCurrent(); // Use helper
    document.execCommand('styleWithCSS', false, 'true');
    document.execCommand('foreColor', false, '#000'); // Set to black or default text color
    this.selectedTextColor = '#000000'; // Update state
    this.updateValue();
  }

  openAdvancedColorPicker(event: MouseEvent, input: HTMLInputElement) {
    this.saveSelection(); // Save selection before opening native color picker
    // The actual color picker opening is handled by the input element itself when clicked
    // No code needed here other than saving the selection
  }

  // saveSelection() { // Already implemented and modified above
  //   const selection = window.getSelection();
  //   if (selection && selection.rangeCount > 0) {
  //     this.savedRange = selection.getRangeAt(0).cloneRange();
  //   } else {
  //      this.savedRange = null;
  //   }
  // }
  onAdvancedColorPicked(event: Event) {
    // This is triggered when the native color picker value changes
    const color = (event.target as HTMLInputElement).value;
    this.restoreSelection && this.restoreSelection(); // Restore selection before applying color
    const selection = window.getSelection(); // Get selection after restoring
    if (!selection || selection.rangeCount === 0) {
      console.warn("No selection available to apply advanced color.");
      // Optionally, apply to the current cursor position if possible
      document.execCommand('styleWithCSS', false, 'true');
      document.execCommand('foreColor', false, color);
      this.selectedTextColor = color;
      this.updateValue();
      return;
    }


    this.setTextColor(color); // Apply the selected color
    this.selectedTextColor = color;
    // No need to close a palette here as it's a native picker
    this.updateValue();
  }

  closeTextColorPalette() {
    this.showTextColorPalette = false;
  }

  openTextColorPalette(event: MouseEvent) {
    const button = event.currentTarget as HTMLElement;
    const container = button.parentElement; // Assuming toolbar is the container
    if (!container) {
      console.warn("Text color button container not found.");
      return;
    }
    const rect = button.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    this.textColorPalettePosition = this.adjustPickerPosition(rect, containerRect, 250, 200); // Adjust size as needed
    this.showTextColorPalette = true;
    this.showBgColorPalette = false; // Close background color palette
    // No need to call updateValue here
  }

  // onDocumentClick is important for closing palettes/toolbars
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const editor = this.editorRef?.nativeElement as HTMLElement;

    // Close Text Color Palette if click is outside palette and its trigger button
    if (!target.closest('.text-color-palette') && !target.closest('.text-color-button') && !(target.tagName === 'INPUT' && (target as HTMLInputElement).type === 'color')) {
      this.closeTextColorPalette();
    }

    // Close Background Color Palette if click is outside palette and its trigger button
    if (!target.closest('.bg-color-palette') && !target.closest('.bg-color-button') && !(target.tagName === 'INPUT' && (target as HTMLInputElement).type === 'color')) {
      this.closeBgColorPalette();
    }

    // Close Emoji and Special Char Pickers if click is outside them and their trigger buttons
    if (!target.closest('.emoji-picker') && !target.closest('.emoji-button') &&
      !target.closest('.special-char-picker') && !target.closest('.special-char-button')) {
      this.closePickers();
    }


    // Hide image toolbar if click is outside image, resize handles, and toolbar (handled in mousedownHandler)
    // Hide table resize handles if click is outside table (handled in mousedownHandler)

    // If click is outside the editor area entirely, ensure selections/handles are cleaned up
    // Note: The onBlur logic handles clearing savedRange. Additional cleanup here might be redundant or need careful thought.
    // If the click is outside the editor, the blur event should have already fired.
    // Let's rely on onBlur and mousedownHandler for now.

  }


  @HostListener('mouseup')
  @HostListener('keyup')
  updateSelectedValues() {
    // Use a small delay to allow the browser to update the selection after mouseup/keyup
    requestAnimationFrame(() => { // Use requestAnimationFrame for next paint, might be better than setTimeout
      const selection = window.getSelection();
      const editor = this.editorRef.nativeElement as HTMLElement;

      if (!selection || selection.rangeCount === 0 || !editor) return;

      const range = selection.getRangeAt(0);

      // Ensure the selection is within the editor before trying to get styles
      if (!editor.contains(range.commonAncestorContainer)) {
        // Clear states if selection is outside the editor
        this.currentFontSize = 16; // Default size
        this.currentFontFamily = 'Poppins'; // Default font
        this.selectedTextColor = '#000000'; // Default color
        this.selectedBgColor = ''; // Default background color
        this.selectedBulletType = '';
        this.selectedNumberType = '';
        this.selectedLineHeight = '';
        // Also hide image/table toolbars if they are shown and selection is outside
        if (!this.selectedImage || !editor.contains(this.selectedImage)) {
          this.showImageToolbar = false;
          this.imageToolbarTarget = null;
        }
        // Table handles are removed by mousedownHandler
        return;
      }


      let node = range.startContainer;

      // If node is a text node, get parent element
      if (node.nodeType === 3 && node.parentElement) {
        node = node.parentElement;
      }

      // If node is null or outside editor after adjustment, use editor itself or return
      if (!node || !editor.contains(node)) {
        node = editor; // Fallback to editor element
      }


      // Find the computed styles (font size, family, color, background color, line height)
      const computedStyle = window.getComputedStyle(node as Element);

      // Update Font Size
      const fontSizePx = parseInt(computedStyle.fontSize, 10);
      if (!isNaN(fontSizePx)) {
        this.currentFontSize = fontSizePx;
      } else {
        this.currentFontSize = 16; // Default
      }


      // Update Font Family
      const fontFamily = computedStyle.fontFamily;
      if (fontFamily) {
        this.currentFontFamily = fontFamily.split(',')[0].replace(/['"]/g, '').trim();
      } else {
        this.currentFontFamily = 'Poppins'; // Default
      }


      // Update Text Color
      const color = computedStyle.color;
      if (color && color !== 'rgba(0, 0, 0, 0)' && color !== 'transparent') {
        this.selectedTextColor = this.rgbToHex(color);
      } else {
        this.selectedTextColor = '#000000'; // Default
      }


      // Update Background Color
      const bgColor = computedStyle.backgroundColor;
      if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
        this.selectedBgColor = this.rgbToHex(bgColor);
      } else {
        this.selectedBgColor = ''; // No background color selected
      }


      // Update Line Height
      const lineHeight = computedStyle.lineHeight;
      // Check if lineHeight is a number (unitless) or a pixel value, convert to string number for select
      if (lineHeight && lineHeight !== 'normal') {
        if (lineHeight.endsWith('px')) {
          const lineHeightPx = parseFloat(lineHeight);
          const elementFontSizePx = parseInt(computedStyle.fontSize, 10);
          if (!isNaN(lineHeightPx) && !isNaN(elementFontSizePx) && elementFontSizePx > 0) {
            // Calculate unitless line height
            this.selectedLineHeight = (lineHeightPx / elementFontSizePx).toFixed(1); // Keep one decimal
          } else {
            this.selectedLineHeight = ''; // Could not calculate
          }
        } else {
          this.selectedLineHeight = parseFloat(lineHeight).toString(); // Should be a number string
        }
      } else {
        this.selectedLineHeight = ''; // Default or 'normal'
      }


      // Update Bullet/Number List Type
      const listElement = (node as Element).closest('ul, ol');
      if (listElement) {
        const listStyle = window.getComputedStyle(listElement);
        const listStyleType = listStyle.listStyleType;
        if (listElement.tagName === 'UL') {
          this.selectedBulletType = `ul-${listStyleType}`;
          this.selectedNumberType = ''; // Clear number type if inside UL
        } else { // OL
          this.selectedNumberType = `ol-${listStyleType}`;
          this.selectedBulletType = ''; // Clear bullet type if inside OL
        }
      } else {
        this.selectedBulletType = '';
        this.selectedNumberType = '';
      }

      // Check if it's a checklist item (LI with input[type=checkbox])
      const listItem = (node as Element).closest('li');
      if (listItem && listItem.querySelector('input[type="checkbox"]')) {
        // It's a checklist item, set appropriate bullet/number state if needed
        // For checklist, list-style-type is usually 'none', but we might have a custom state
        // For simplicity, we can maybe indicate it's a list, but the specific type might not be easily determined from style alone.
        // Let's just update the list type based on the UL/OL ancestor as done above.
      }


    }); // End requestAnimationFrame
  }


  onAdvancedBgColorPicked(event: Event) {
    // This is triggered when the native background color picker value changes
    const color = (event.target as HTMLInputElement).value;
    this.restoreSelection && this.restoreSelection(); // Restore selection
    const selection = window.getSelection(); // Get selection after restoring
    if (!selection || selection.rangeCount === 0) {
      console.warn("No selection available to apply advanced background color.");
      // Optionally, apply to the current cursor's block if possible
      const range = window.getSelection()?.getRangeAt(0);
      const node = range?.startContainer.nodeType === 3 ? range.startContainer.parentElement : range?.startContainer as HTMLElement;
      const parentBlock = node?.closest('p, div, li, blockquote, h1, h2, h3, h4, h5, h6') as HTMLElement;
      if (parentBlock && this.editorRef.nativeElement.contains(parentBlock)) {
        parentBlock.style.backgroundColor = color;
        this.selectedBgColor = color;
        this.updateValue();
      } else {
        // Fallback to document.execCommand if block not found/valid
        document.execCommand('styleWithCSS', false, 'true');
        document.execCommand('hiliteColor', false, color);
        this.selectedBgColor = color;
        this.updateValue();
      }
      return;
    }


    this.setBackgroundColor(color); // Apply the selected background color
    // setBackgroundColor already updates selectedBgColor and calls updateValue and closes palette (if it were a custom palette)
    this.showBgColorPalette = false; // Ensure custom palette is closed if using native picker
  }

  // Thêm phương thức updateValue (Already implemented above)
  private updateValue() {
    if (this.editorRef && this.editorRef.nativeElement) {
      this.content = this.editorRef.nativeElement.innerHTML;
      // Sanitize content before emitting? Depends on requirements.
      this.valueChange.emit(this.content);
      this.onChange(this.content);
      // No need to call onTouched here, it's handled by onInput and onBlur
    }
  }
}
