import { Routes } from '@angular/router';

export const routes: Routes = [
    {
        path: '',
        title: 'Trang chủ | Phòng vé Indeed Travel',
        loadComponent: async () => (await import('./components/shared/layout-client-component/layout-client-component.component')).LayoutClientComponentComponent,
        children: [
            {
                path: '',
                title: 'Trang chủ | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/home/<USER>')).HomeComponent
            },
            {
                path: 'news',
                title: 'Tin tức | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/route-new/news/news.component')).NewsComponent
            },
            {
                path: 'news/:type',
                title: 'Tin tức | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/route-new/category-news/category-news.component')).CategoryNewsComponent
            },
            {
                path: 'news/:type/:seoTitle',
                title: '<PERSON> tức | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/route-new/news-detais/news-detais.component')).NewsDetaisComponent
            },
            // {
            //     path: 'editor',
            //     title: 'Soạn thảo | Phòng vé Indeed Travel',
            //     loadComponent: async () => (await import('./components/test-editor/test-editor.component')).TestEditorComponent
            // },
            {
                path: 'contact',
                title: 'Liên hệ | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/contact/contact.component')).ContactComponent
            },
            {
                path: 'faq',
                title: 'Câu hỏi thường gặp | Mai Trí Vương',
                loadComponent: async () => (await import('./components/faq/faq.component')).FaqComponent
            },
            {
                path: 'about-us',
                title: 'Giới thiệu | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/about-us/about-us.component')).AboutUsComponent
            },
            {
                path: 'login',
                title: 'Đăng nhập | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/login/login.component')).LoginComponent
            },
            {
                path: 'TripSelection',
                title: 'Chọn vé máy bay | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/ticket/trip-selection/trip-selection.component')).TripSelectionComponent
            },
            {
                path: 'TripPassengers',
                title: 'Thông tin khách hàng | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/ticket/trip-passengers/trip-passengers.component')).TripPassengersComponent
            },
            {
                path: 'TripPayment',
                title: 'Phương thức thanh toán | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/ticket/trip-payment/trip-payment.component')).TripPaymentComponent
            },
            {
                path: 'TripResult',
                title: 'Kết quả đơn hàng | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/ticket/trip-result/trip-result.component')).TripResultComponent
            },
            {
                path: 'TripRePayment',
                title: 'Tiếp tục thanh toán đặt chỗ | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/ticket/trip-re-payment/trip-re-payment.component')).TripRePaymentComponent
            },
            {
                path: 'TripAvailable',
                title: 'Đặt chỗ của tôi | Phòng vé Indeed Travel',
                loadComponent: async () => (await import('./components/ticket/trip-available/trip-available.component')).TripAvailableComponent
            },
        ]
    },
    {
        path: 'manager',
        title: 'Quản lý | Phòng vé Indeed Travel',
        loadComponent: async () => (await import('./components/manager/manager/manager.component')).ManagerComponent,
        loadChildren: async () => (await import('./components/manager/manager.module')).ManagerModule
    },
    {
        path: '404',
        title: 'Không tìm thấy trang | Phòng vé Indeed Travel',
        loadComponent: async () => (await import('./components/page404/page404.component')).Page404Component
    },
    {
        path: '**',
        redirectTo: ''
    },


];