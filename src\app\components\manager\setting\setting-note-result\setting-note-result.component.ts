import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { FeatureService } from '../../../../services/feature/feature.service';

@Component({
  selector: 'app-setting-note-result',
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './setting-note-result.component.html',
  styleUrl: './setting-note-result.component.css'
})
export class SettingNoteResultComponent {
  public tabSelected: string = 'edit';
  public newIdentityDocText: string = '';
  public newSpecialRuleText: string = '';

  public NoteModel: {
    TimeCheckIn: {
      Domestic: string,
      International: string
    },
    IdentityDocuments: { value: string }[],
    SpecialRules: { value: string }[],
  } = {
      TimeCheckIn: {
        Domestic: '90 phút',
        International: '3 tiếng'
      },
      IdentityDocuments: [],
      SpecialRules: []
    };


  constructor(
    private readonly featureService: FeatureService
  ) { }

  ngOnInit(): void {
    this.loadNoteResult();
  }

  public setTabSelected(tab: string) {
    this.tabSelected = tab;
  }

  loadNoteResult() {
    this.featureService.getPartnerPaymentInfo('note-result').subscribe(
      (res: any) => {
        if (res.isSuccessed) {
          this.NoteModel = JSON.parse(res.resultObj);
        }
      }
    );
  }

  removeIdentityDoc(index: number) {
    this.NoteModel.IdentityDocuments.splice(index, 1);
  }
  addIdentityDoc() {
    if (this.newIdentityDocText.trim() === '') {
      return;
    }
    this.NoteModel.IdentityDocuments.push({ value: this.newIdentityDocText });
    this.newIdentityDocText = '';
  }

  removeSpecialRule(index: number) {
    this.NoteModel.SpecialRules.splice(index, 1);
  }
  addSpecialRule() {
    if (this.newSpecialRuleText.trim() === '') {
      return;
    }
    this.NoteModel.SpecialRules.push({ value: this.newSpecialRuleText });
    this.newSpecialRuleText = '';
  }

  public saveNote() {
    var comfirm = window.confirm("Bạn có chắc chắn muốn lưu không?");
    if (comfirm) {
      this.featureService.PartnerUpdatePaymentInfo(this.NoteModel, 'note-result').subscribe(
        (res: any) => {
          if (res.isSuccessed) {
            alert('Cập nhật thành công!');
          } else {
            alert('Cập nhật thất bại!');
          }
        },
        (error: any) => {
          alert('Error: Cập nhật thất bại!');
        }
      );
    }
  }
}
