<div class="container relative w-full md:p-4 p-2 shadow-lg bg-white dark:bg-gray-900 rounded-lg h-full min-w-full">

    <div class="w-full h-full relative flex flex-col pb-4  sm:rounded-lg  md:gap-6 gap-2">
        <div class="w-full shadow rounded-xl flex flex-col p-2 md:gap-4 gap-2">
            <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div class="flex items-center">
                    <a routerLink="/manager/news" class="flex items-center gap-2 text-lg font-semibold md:text-xl">
                        <span>Quản lý tà<PERSON> kho<PERSON>n</span>
                    </a>

                </div>
                <button (click)="openCreateDialog()"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary-400 text-white hover:bg-primary/90 h-10 px-4 py-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-circle-plus mr-2 h-4 w-4">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M8 12h8"></path>
                        <path d="M12 8v8"></path>
                    </svg>Thêm tài khoản mới
                </button>
            </div>
            <div class="flex flex-row flex-wrap justify-between md:gap-6 gap-2 relative">
                <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between w-full">
                    <div class="flex w-full max-w-sm items-center space-x-2">
                        <input
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder: focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            placeholder="Tìm kiếm tài khoản..." type="search" [(ngModel)]="searchModel.Keyword"
                            (keyup.enter)="onSubmitSearch()">
                        <button
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input  hover:bg-primary-400 hover:text-white h-10 w-10"
                            type="button" (click)="onSubmitSearch()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-search h-4 w-4">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.3-4.3"></path>
                            </svg>
                            <span class="sr-only">Tìm kiếm</span>
                        </button>
                    </div>
                    <div class="flex items-center gap-2 flex-wrap">
                        <select class="rounded-md border border-input bg-background px-3 py-2 text-sm cursor-pointer"
                            [(ngModel)]="searchModel.Type" (change)="onSubmitSearch()">
                            <option value="">Tất cả vai trò</option>
                            @for (role of Roles; track $index) {
                            <option [value]="role.normalizedName">{{role.name}}</option>
                            }
                        </select>
                        <select class="rounded-md border border-input bg-background px-3 py-2 text-sm cursor-pointer"
                            [(ngModel)]="searchModel.FilterStatus" (change)="onSubmitSearch()">
                            <option [ngValue]="null">Tất cả trạng thái</option>
                            <option [ngValue]="2">Online</option>
                            <option [ngValue]="1">Offline</option>
                            <option [ngValue]="0">Khóa</option>
                        </select>
                        <select [(ngModel)]="searchModel.PageSize" (change)="changePageSize($event)"
                            class="rounded-md border border-input bg-background px-3 py-2 text-sm cursor-pointer">
                            <option value="10" selected>10 dòng</option>
                            <option value="20">20 dòng</option>
                            <option value="50">50 dòng</option>
                            <option value="100">100 dòng</option>
                            <option value="200">200 dòng</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="w-full h-full overflow-auto">
            <table
                class="w-full h-fill-available text-sm text-left sm:mb-28 max-sm:mb-32 rtl:text-right text-gray-500 dark:text-white">
                <thead
                    class="sticky -top-1 text-xs text-white uppercase bg-gradient-to-r from-[#fb6340] via-[#fb6340] to-[#fbb140] dark:bg-gray-700  dark:bg-none">
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                ID

                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Người dùng
                                <a (click)="sortTable('FullName')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Email
                                <a (click)="sortTable('Email')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Vai trò
                                <a (click)="sortTable('Roles')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center text-nowrap">
                                Trạng thái
                                <a (click)="sortTable('Status')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>

                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Đăng nhập cuối
                                <a (click)="sortTable('LastActivity')">
                                    <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                    </svg>
                                </a>
                            </div>
                        </th>
                        <th scope="col" class="h-12 px-4 text-left align-middle font-medium text-white text-nowrap">
                            <div class="flex items-center justify-center text-nowrap">
                                Thao tác
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if(dataTable.items.length > 0){
                    @for(item of dataTable.items || []; track $index ){
                    <tr [ngClass]="{'bg-red-200 dark:bg-red-600': item.isActive === false, 'odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800': item.isActive}"
                        class="cursor-pointer border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">

                        <td class="p-2 font-medium text-gray-800 text-left">
                            {{item.index}}
                        </td>
                        <td class="p-2 font-medium text-gray-800">

                            <div class="flex items-center gap-3">
                                <img [alt]="item.name" class="h-8 w-8 rounded-full object-cover"
                                    [src]="item.pathImage || '/assets/img/avatar/avatar_default.png'">
                                <div>
                                    <div class="font-medium">{{item.fullName}}</div>
                                    <div class="text-sm font-normal text-gray-400">&#64;{{item.userName}}</div>
                                </div>
                            </div>
                        </td>
                        <td class="p-2 font-medium text-gray-800">
                            {{item.email}}
                        </td>
                        <td class="p-2 text-left text-nowrap">
                            @for (role of item.roles; track $index) {
                            <span
                                [class]="'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ' +
                                (role === 'Partner Admin' ? 'bg-red-100 text-red-800' : '') +
                                (role === 'Partner Staff' ? 'bg-purple-100 text-purple-800' : '') +
                                (role !== 'Partner Admin' && role !== 'Partner Staff' ? 'bg-gray-100 text-gray-800' : '')">{{role}}
                            </span>
                            }
                        </td>
                        <td class="p-2 text-left">
                            <span [class]="
                            'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ' +
                            (item.status === 2 ? ' bg-green-100 text-green-800' : '') +
                            (item.status === 1 ? ' bg-gray-100 text-gray-800' : '') +
                            (item.status === 0 ? ' bg-red-100 text-red-800' : '')
                          ">
                                <span class="text-nowrap" *ngIf="item.status === 2">Đang online</span>
                                <span class="text-nowrap" *ngIf="item.status === 1">Offline</span>
                                <span class="text-nowrap" *ngIf="item.status === 0">Tài khoản khóa</span>
                            </span>
                        </td>

                        <td class="p-2 text-center">
                            {{item.lastActivity }}

                        </td>
                        <td class="p-4 align-middle  text-right">
                            <div class="flex justify-end gap-2">

                                <button (click)="openEditDialog(item)"
                                    class="inline-flex items-center hover:bg-gray-300 justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground h-10 w-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-square-pen h-4 w-4">
                                        <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                                    </svg>
                                    <span class="sr-only">Chỉnh sửa</span>
                                </button>
                                <button (click)="openDeleteDialog(item)"
                                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground h-10 w-10"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-trash2 h-4 w-4">
                                        <path d="M3 6h18"></path>
                                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                        <line x1="10" x2="10" y1="11" y2="17"></line>
                                        <line x1="14" x2="14" y1="11" y2="17"></line>
                                    </svg>
                                    <span class="sr-only">Xóa</span>
                                </button>
                            </div>
                        </td>
                    </tr>
                    }
                    }@else {
                    <tr
                        class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800 border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">
                        <td class="px-6 py-4" colspan="9">
                            <div class="flex items-center justify-center">
                                <span class="text-gray-500 dark:text-gray-400">
                                    Không có dữ liệu
                                </span>
                            </div>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>

    </div>
    <nav class="pagination absolute bg-white sm:rounded-lg dark:bg-gray-600 w-full bottom-0 flex items-center flex-column flex-wrap md:flex-row justify-between p-4"
        aria-label="Table navigation">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Showing
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable.from}} - {{dataTable.to}}</span> of
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable.totalRecords}}</span></span>
        <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(1)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m17 16-4-4 4-4m-6 8-4-4 4-4" />
                    </svg>
                </a>
            </li>
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(dataTable.pageIndex - 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m14 8-4 4 4 4" />
                    </svg>
                </a>
            </li>

            <li *ngFor="let page of range(startIndex, finishIndex); let i = index"
                [class.active]="page === dataTable.pageIndex">
                <a class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                    (click)="getUrl(page)" [title]="'Page ' + page">
                    {{ page }}
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageIndex + 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m10 16 4-4-4-4" />
                    </svg>
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageCount)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m7 16 4-4-4-4m6 8 4-4-4-4" />
                    </svg>
                </a>
            </li>
        </ul>
    </nav>


</div>

<div *ngIf="showEditDialog || showDeleteDialog || showCreateDialog" class="fixed inset-0 bg-black/50 z-40"></div>
<div *ngIf="showEditDialog"
    class="bg-white fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg sm:max-w-[600px]"
    style="pointer-events: auto;">
    <form [formGroup]="editForm" (ngSubmit)="submitEditForm()">
        <div class="flex flex-col space-y-1.5 text-center sm:text-left">
            <h2 class="text-lg font-semibold leading-none tracking-tight">Chỉnh sửa tài khoản</h2>
            <p class="text-sm text-muted-foreground">Chỉnh sửa thông tin tài khoản người dùng</p>
        </div>
        <div class="grid gap-4 py-4">
            <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="fullName">Họ và tên</label>
                    <input
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        id="fullName" formControlName="fullName" name="fullName">
                    <div *ngIf="editForm.get('fullName')?.invalid && editForm.get('fullName')?.touched"
                        class="text-red-500 text-xs mt-1">
                        <span *ngIf="editForm.get('fullName')?.errors?.['required']">Họ và tên là bắt buộc</span>
                        <span *ngIf="editForm.get('fullName')?.errors?.['maxlength']">Họ và tên không được vượt quá 200
                            ký tự</span>
                    </div>
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="username">Tên đăng nhập</label>
                    <input
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        id="username" formControlName="userName" name="username">
                    <div *ngIf="editForm.get('userName')?.invalid && editForm.get('userName')?.touched"
                        class="text-red-500 text-xs mt-1">
                        <span *ngIf="editForm.get('userName')?.errors?.['required']">Tên đăng nhập là bắt buộc</span>
                        <span *ngIf="editForm.get('userName')?.errors?.['maxlength']">Tên đăng nhập không được vượt quá
                            200 ký tự</span>
                    </div>
                </div>
            </div>
            <div class="space-y-2">
                <label
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    for="email">Email</label>
                <input
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    id="email" type="email" formControlName="email" name="email">
                <div *ngIf="editForm.get('email')?.invalid && editForm.get('email')?.touched"
                    class="text-red-500 text-xs mt-1">
                    <span *ngIf="editForm.get('email')?.errors?.['required']">Email là bắt buộc</span>
                    <span *ngIf="editForm.get('email')?.errors?.['email']">Email không hợp lệ</span>
                    <span *ngIf="editForm.get('email')?.errors?.['maxlength']">Email không được vượt quá 200 ký
                        tự</span>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="role">Vai trò</label>
                    <div class="relative role-dropdown">
                        <button type="button" (click)="showRoleDropdown = !showRoleDropdown"
                            class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background flex items-center justify-between">
                            <span>{{editForm.get('roles')?.value?.length > 0 ? editForm.get('roles')?.value?.length + '
                                vai trò được chọn' : 'Chọn vai trò'}}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4">
                                <path d="m6 9 6 6 6-6" />
                            </svg>
                        </button>
                        <div *ngIf="showRoleDropdown"
                            class="absolute bg-white z-50 mt-1 w-full rounded-md border border-input bg-background shadow-lg">
                            <div class="max-h-60 overflow-auto p-1">
                                @for (role of Roles; track $index) {
                                <div class="flex items-center px-2 py-1.5 hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer"
                                    (click)="toggleRole(role.normalizedName)">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox" [checked]="isRoleSelected(role.normalizedName)"
                                            class="h-4 w-4 rounded border-gray-300 text-primary-400 focus:ring-primary-400">
                                    </div>
                                    <span class="ml-2 text-sm">{{role.name}}</span>
                                </div>
                                }
                            </div>
                        </div>
                    </div>
                    <div *ngIf="editForm.get('roles')?.invalid && editForm.get('roles')?.touched"
                        class="text-red-500 text-xs mt-1">
                        <span *ngIf="editForm.get('roles')?.errors?.['required']">Vai trò là bắt buộc</span>
                    </div>
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="status">Trạng thái</label>
                    <select id="status" name="status" formControlName="status"
                        class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
                        <option [ngValue]="1">Hoạt động</option>
                        <option [ngValue]="0">Tạm khóa</option>
                    </select>
                </div>
            </div>
            <div class="space-y-2">
                <label
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    for="avatar">Ảnh đại diện</label>
                <input
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    id="avatar" accept="image/*" type="file" (change)="handleFileInput($event)" name="avatar">
            </div>
        </div>
        <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <button type="button" (click)="closeEditDialog()"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                Hủy
            </button>
            <button type="submit" [disabled]="editForm.invalid"
                class="inline-flex items-center justify-center bg-primary-400 text-white gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                Lưu thay đổi
            </button>
        </div>
    </form>
    <button type="button" (click)="closeEditDialog()"
        class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-x h-4 w-4">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
        </svg>
        <span class="sr-only">Close</span>
    </button>
</div>

<div *ngIf="showCreateDialog"
    class="bg-white fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg sm:max-w-[600px]"
    style="pointer-events: auto;">
    <form [formGroup]="createForm" (ngSubmit)="submitCreateForm()">
        <div class="flex flex-col space-y-1.5 text-center sm:text-left">
            <h2 class="text-lg font-semibold leading-none tracking-tight">Tạo tài khoản mới</h2>
            <p class="text-sm text-muted-foreground">Nhập thông tin để tạo tài khoản người dùng mới</p>
        </div>
        <div class="grid gap-4 py-4">
            <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="create-fullName">Họ và tên</label>
                    <input
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        id="create-fullName" formControlName="fullName" name="fullName">
                    <div *ngIf="createForm.get('fullName')?.invalid && createForm.get('fullName')?.touched"
                        class="text-red-500 text-xs mt-1">
                        <span *ngIf="createForm.get('fullName')?.errors?.['required']">Họ và tên là bắt buộc</span>
                        <span *ngIf="createForm.get('fullName')?.errors?.['maxlength']">Họ và tên không được vượt quá
                            200 ký tự</span>
                    </div>
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="create-userName">Tên đăng nhập</label>
                    <input
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        id="create-userName" formControlName="userName" name="userName">
                    <div *ngIf="createForm.get('userName')?.invalid && createForm.get('userName')?.touched"
                        class="text-red-500 text-xs mt-1">
                        <span *ngIf="createForm.get('userName')?.errors?.['required']">Tên đăng nhập là bắt buộc</span>
                        <span *ngIf="createForm.get('userName')?.errors?.['maxlength']">Tên đăng nhập không được vượt
                            quá 200 ký tự</span>
                    </div>
                </div>
            </div>
            <div class="space-y-2">
                <label
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    for="create-email">Email</label>
                <input
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    id="create-email" type="email" formControlName="email" name="email">
                <div *ngIf="createForm.get('email')?.invalid && createForm.get('email')?.touched"
                    class="text-red-500 text-xs mt-1">
                    <span *ngIf="createForm.get('email')?.errors?.['required']">Email là bắt buộc</span>
                    <span *ngIf="createForm.get('email')?.errors?.['email']">Email không hợp lệ</span>
                    <span *ngIf="createForm.get('email')?.errors?.['maxlength']">Email không được vượt quá 200 ký
                        tự</span>
                </div>
            </div>
            <div class="space-y-2">
                <label
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    for="create-password">Mật khẩu</label>
                <input
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    id="create-password" type="password" formControlName="password" name="password">
                <div *ngIf="createForm.get('password')?.invalid && createForm.get('password')?.touched"
                    class="text-red-500 text-xs mt-1">
                    <span *ngIf="createForm.get('password')?.errors?.['required']">Mật khẩu là bắt buộc</span>
                    <span *ngIf="createForm.get('password')?.errors?.['maxlength']">Mật khẩu không được vượt quá 200 ký
                        tự</span>
                    <span *ngIf="createForm.get('password')?.errors?.['strongPassword']">Mật khẩu phải có ít nhất 6 ký
                        tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt.</span>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="create-role">Vai trò</label>
                    <div class="relative role-dropdown">
                        <button type="button" (click)="showRoleDropdown = !showRoleDropdown"
                            class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background flex items-center justify-between">
                            <span>{{createForm.get('roles')?.value?.length > 0 ? createForm.get('roles')?.value?.length
                                + ' vai trò được chọn' : 'Chọn vai trò'}}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4">
                                <path d="m6 9 6 6 6-6" />
                            </svg>
                        </button>
                        <div *ngIf="showRoleDropdown"
                            class="absolute bg-white z-50 mt-1 w-full rounded-md border border-input bg-background shadow-lg">
                            <div class="max-h-60 overflow-auto p-1">
                                @for (role of Roles; track $index) {
                                <div class="flex items-center px-2 py-1.5 hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer"
                                    (click)="toggleRoleForCreate(role.normalizedName)">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox" [checked]="isRoleSelectedForCreate(role.normalizedName)"
                                            class="h-4 w-4 rounded border-gray-300 text-primary-400 focus:ring-primary-400">
                                    </div>
                                    <span class="ml-2 text-sm">{{role.name}}</span>
                                </div>
                                }
                            </div>
                        </div>
                    </div>
                    <div *ngIf="createForm.get('roles')?.invalid && createForm.get('roles')?.touched"
                        class="text-red-500 text-xs mt-1">
                        <span *ngIf="createForm.get('roles')?.errors?.['required']">Vai trò là bắt buộc</span>
                    </div>
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="create-status">Trạng thái</label>
                    <select id="create-status" name="status" formControlName="status"
                        class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
                        <option [ngValue]="1">Hoạt động</option>
                        <option [ngValue]="0">Tạm khóa</option>
                    </select>
                </div>
            </div>
            <div class="space-y-2">
                <label
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    for="create-avatar">Ảnh đại diện</label>
                <input
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    id="create-avatar" accept="image/*" type="file" (change)="handleFileInputForCreate($event)"
                    name="avatar">
            </div>
        </div>
        <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <button type="button" (click)="closeCreateDialog()"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                Hủy
            </button>
            <button type="submit" [disabled]="createForm.invalid"
                class="inline-flex items-center justify-center bg-primary-400 text-white gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                Tạo tài khoản
            </button>
        </div>
    </form>
    <button type="button" (click)="closeCreateDialog()"
        class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-x h-4 w-4">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
        </svg>
        <span class="sr-only">Close</span>
    </button>
</div>

<div *ngIf="showDeleteDialog"
    class="fixed bg-white left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg"
    tabindex="-1" style="pointer-events: auto;">
    <div class="flex flex-col space-y-2 text-center sm:text-left">
        <h2 class="text-lg font-semibold">Xác nhận xóa tài khoản</h2>
        <p class="text-sm text-muted-foreground">Bạn có chắc chắn muốn xóa tài khoản của
            <strong class="text-red-500">"{{userToDelete?.fullName}}"</strong>?<br><br><strong>Cảnh báo:</strong> Hành
            động này không thể
            hoàn tác và sẽ:
        <ul class="list-disc list-inside mt-2 space-y-1">
            <li>Xóa vĩnh viễn tài khoản người dùng</li>
            <li>Xóa tất cả bài viết của người dùng này</li>
            <li>Xóa tất cả bình luận và tương tác</li>
            <li>Không thể khôi phục dữ liệu sau khi xóa</li>
        </ul>
    </div>
    <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
        <button type="button" (click)="closeDeleteDialog()"
            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 mt-2 sm:mt-0">Hủy</button>
        <button type="button" (click)="confirmDelete()"
            class="inline-flex items-center text-white justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  text-primary-foreground h-10 px-4 py-2 bg-red-500 hover:bg-red-600">Xóa
            tài khoản</button>
    </div>
</div>