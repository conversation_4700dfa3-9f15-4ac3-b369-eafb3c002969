.editor-viewer ul,
.editor-viewer ol {
    list-style-type: disc;
    list-style-position: outside;
    margin-left: 1.5rem;
    padding-left: 1.5rem;
}

.editor-viewer ul {
    list-style-type: disc;
}

.editor-viewer ol {
    list-style-type: decimal;
}



pre {
    background: #f5f5f5;
    color: #222;
    font-family: '<PERSON>ra Mono', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Monaco', monospace;
    font-size: 14px;
    padding: 12px 16px;
    border-radius: 6px;
    margin: 8px 0;
    overflow-x: auto;
    white-space: pre-wrap;
    /* Cho phép xuống dòng trong <pre> thay vì tạo nhiều <pre> */
    word-break: break-word;
    line-height: 1.6;
    border: 1px solid #e5e7eb;
}

pre code {
    background: none;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    padding: 0;
    border: none;
}


ul[style*="list-style-type: box"] li::marker {
    content: "\25A1 ";
    /* □ */
}

ul[style*="list-style-type: dash"] li::marker {
    content: "\2014 ";
    /* — */
}

ul[style*="list-style-type: dot"] li::marker {
    content: "\00B7 ";
    /* · */
}

ul[style*="list-style-type: filled"] li::marker {
    content: "\25CF ";
    /* ● */
}

ul[style*="list-style-type: ring"] li::marker {
    content: "\25CB ";
    /* ○ */
}

ul[style*="list-style-type: black-square"] li::marker {
    content: "\25A0 ";
    /* ■ */
}

ul[style*="list-style-type: diamond"] li::marker {
    content: "\25C6 ";
    /* ◆ */
}

ul[style*="list-style-type: arrow"] li::marker {
    content: "\2192 ";
    /* → */
}

ul[style*="list-style-type: check"] li::marker {
    content: "\2713 ";
    /* ✓ */
}

/* Checklist style: ẩn bullet khi li có checkbox */
.editor-viewer ul[style*="list-style-type: none"] li input[type="checkbox"] {
    accent-color: #22c55e;
    /* màu xanh lá cho checkbox, tuỳ ý */
}

.editor-viewer ul[style*="list-style-type: none"] li input[type="checkbox"] {
    vertical-align: middle;
    margin-right: 8px;
}

.editor-viewer ul[style*="list-style-type: none"] li {
    list-style-type: none;
    /* ẩn bullet nếu là checklist */
    position: relative;
    padding-left: 0;
}

/* Đảm bảo checkbox không bị to quá */
.editor-viewer ul li input[type="checkbox"] {
    width: 1.1em;
    height: 1.1em;
    cursor: pointer;
}

/* Optional: style cho blockquote */
.editor-viewer blockquote {
    border-left: 4px solid #a3a3a3;
    margin: 1em 0;
    padding: 0.5em 1em;
    color: #555;
    /* background: #f9f9f9; */
    font-style: italic;
}


.editor-viewer h1 {
    font-size: 2.2em;
    font-weight: bold;
    color: #1e293b;
    margin: 1em 0 0.5em 0;
}

.editor-viewer h2 {
    font-size: 1.8em;
    font-weight: bold;
    color: #334155;
    margin: 1em 0 0.5em 0;
}

.editor-viewer h3 {
    font-size: 1.5em;
    font-weight: bold;
    color: #475569;
    margin: 1em 0 0.5em 0;
}

.editor-viewer h4 {
    font-size: 1.2em;
    font-weight: bold;
    color: #64748b;
    margin: 1em 0 0.5em 0;
}

.editor-viewer h5 {
    font-size: 1em;
    font-weight: bold;
    color: #64748b;
    margin: 1em 0 0.5em 0;
}

.editor-viewer h6 {
    font-size: 0.9em;
    font-weight: bold;
    color: #94a3b8;
    margin: 1em 0 0.5em 0;
}