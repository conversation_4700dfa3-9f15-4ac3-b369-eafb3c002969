<div class="w-full  bg-white rounded-lg shadow-sm h-full overflow-auto">
    <div class="p-6 space-y-6">
        <h1 class="text-xl font-medium">Cài đặt giao diện</h1>
        <div class="space-y-4">
            <div>

                <!-- Component HTML -->

                <div>
                    <!-- Chọn loại màu: Color name -->
                    <div>
                        <div class="gap-2 flex items-center mb-3  mt-6">
                            <input type="radio" id="color_name" name="colorType" value="color_name"
                                class="w-4 h-4 accent-primary-600 bg-gray-100 border-gray-300"
                                [(ngModel)]="libSettings.selectedColorType">
                            <label for="color_name" class="cursor-pointer">M<PERSON>u nền khung tìm kiếm/ kết quả:</label>
                        </div>

                        <div class="flex items-center gap-4 flex-wrap"
                            [class.opacity-50]="libSettings.selectedColorType !== 'color_name'"
                            [class.pointer-events-none]="libSettings.selectedColorType !== 'color_name'">
                            <div *ngFor="let color of colors; trackBy: trackByFn" class="flex items-center">
                                <div class="relative">
                                    <input class="sr-only" type="radio" [id]="'color-' + color" [value]="color"
                                        name="colorName" [(ngModel)]="libSettings.color">
                                    <label [for]="'color-' + color" [ngClass]="['block w-8 h-8 rounded-full cursor-pointer ring-2 ring-offset-2',
                                              'bg-gradient-to-r',
                                              'ring-' + color + '-400',
                                              'from-' + color + '-400',
                                              'to-' + color + '-500']">
                                    </label>
                                    <div class="absolute inset-0 flex items-center justify-center cursor-pointer hover:opacity-40"
                                        (click)="onColorChange(color)">
                                        <div class="w-3 h-3 bg-white rounded-full opacity-100"
                                            [class.hidden]="libSettings.color !== color">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chọn loại màu: Hex -->
                    <div>
                        <div class="gap-2 flex items-center mt-6">
                            <input type="radio" id="color_hex" name="colorType" value="color_hex"
                                class="w-4 h-4 accent-primary-600 bg-gray-100 border-gray-300"
                                [(ngModel)]="libSettings.selectedColorType">
                            <label for="color_hex" class="cursor-pointer">Tùy chọn bảng màu:</label>
                        </div>

                        <div class="mb-3 w-full" [class.opacity-30]="libSettings.selectedColorType !== 'color_hex'"
                            [class.pointer-events-none]="libSettings.selectedColorType !== 'color_hex'">
                            <label for="HexColor" class="col-md-2 col-form-label"></label>
                            <div>
                                <input class="w-full h-10" type="color" [(ngModel)]="libSettings.hexColor" />
                            </div>
                        </div>
                    </div>
                </div>


                <p class="text-sm text-gray-500 mt-6">Chọn màu nền cho thư viện tìm giá của bạn. Màu sắc này sẽ được áp
                    dụng
                    cho
                    tất cả các trang trong thư viện. Màu mặc định sẽ là màu cam</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-1">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="sort-priority">Khung tìm kiếm:</label>
                    <div class="relative">
                        <select [(ngModel)]="libSettings.IsBoxVertical"
                            class="w-full rounded-md border border-gray-300 py-2 pl-3 pr-10 text-gray-900 focus:border-primary-500 focus:outline-none focus:ring-primary-500">
                            <option value=false>Ngang</option>
                            <option value=true>Dọc</option>
                        </select>
                    </div>
                </div>

            </div>

        </div>

        <button (click)="saveSettings()"
            class="w-full py-3 text-white font-medium rounded-md text-center bg-gradient-to-r from-primary-400 to-primary-500">Lưu
            !</button>
    </div>
</div>