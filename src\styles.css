@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
    height: 100%;
}

body {
    margin: 0;
    font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

.container {
    @apply max-w-7xl mx-auto md:px-4 px-0;
}

/* Modern Travel Website Styles */
:host {
    --bright-blue: oklch(51.01% 0.274 263.83);
    --electric-violet: oklch(53.18% 0.28 296.97);
    --french-violet: oklch(47.66% 0.246 305.88);
    --vivid-pink: oklch(69.02% 0.277 332.77);
    --hot-red: oklch(61.42% 0.238 15.34);
    --orange-red: oklch(63.32% 0.24 31.68);

    --gray-900: oklch(19.37% 0.006 300.98);
    --gray-700: oklch(36.98% 0.014 302.71);
    --gray-400: oklch(70.9% 0.015 304.04);

    --red-to-pink-to-purple-vertical-gradient: linear-gradient(180deg,
            var(--orange-red) 0%,
            var(--vivid-pink) 50%,
            var(--electric-violet) 100%);

    --red-to-pink-to-purple-horizontal-gradient: linear-gradient(90deg,
            var(--orange-red) 0%,
            var(--vivid-pink) 50%,
            var(--electric-violet) 100%);

    --pill-accent: var(--bright-blue);

    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol";
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}



p {
    margin: 0;
    /* color: var(--gray-700); */
}

main {
    width: 100%;
    min-height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    box-sizing: inherit;
    position: relative;
}

.angular-logo {
    max-width: 9.2rem;
}

.content {
    display: flex;
    justify-content: space-around;
    width: 100%;
    max-width: 700px;
    margin-bottom: 3rem;
}

.content h1 {
    margin-top: 1.75rem;
}

.content p {
    margin-top: 1.5rem;
}

.divider {
    width: 1px;
    background: var(--red-to-pink-to-purple-vertical-gradient);
    margin-inline: 0.5rem;
}

.pill-group {
    display: flex;
    flex-direction: column;
    align-items: start;
    flex-wrap: wrap;
    gap: 1.25rem;
}

.pill {
    display: flex;
    align-items: center;
    --pill-accent: var(--bright-blue);
    background: color-mix(in srgb, var(--pill-accent) 5%, transparent);
    color: var(--pill-accent);
    padding-inline: 0.75rem;
    padding-block: 0.375rem;
    border-radius: 2.75rem;
    border: 0;
    transition: background 0.3s ease;
    font-family: var(--inter-font);
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 500;
    line-height: 1.4rem;
    letter-spacing: -0.00875rem;
    text-decoration: none;
}

.pill:hover {
    background: color-mix(in srgb, var(--pill-accent) 15%, transparent);
}

.pill-group .pill:nth-child(6n + 1) {
    --pill-accent: var(--bright-blue);
}

.pill-group .pill:nth-child(6n + 2) {
    --pill-accent: var(--french-violet);
}

.pill-group .pill:nth-child(6n + 3),
.pill-group .pill:nth-child(6n + 4),
.pill-group .pill:nth-child(6n + 5) {
    --pill-accent: var(--hot-red);
}

.pill-group svg {
    margin-inline-start: 0.25rem;
}

.social-links {
    display: flex;
    align-items: center;
    gap: 0.73rem;
    margin-top: 1.5rem;
}

.social-links path {
    transition: fill 0.3s ease;
    fill: var(--gray-400);
}

.social-links a:hover svg path {
    fill: var(--gray-900);
}

@media screen and (max-width: 650px) {
    .content {
        flex-direction: column;
        width: max-content;
    }

    .divider {
        height: 1px;
        width: 100%;
        background: var(--red-to-pink-to-purple-horizontal-gradient);
        margin-block: 1.5rem;
    }
}

:root {
    --primary-color: #f97316;
    --secondary-color: #fb923c;
    --accent-color: #4CAF50;
    --text-color: #2C3E50;
    --light-bg: #F8F9FA;
    --white: #ffffff;
    --gradient-primary: linear-gradient(135deg, #00BCD4 0%, #4CAF50 100%);
    --gradient-secondary: linear-gradient(135deg, #FF6B6B 0%, #FFE66D 100%);
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --border-radius: 20px;
}

body {
    font-family: 'Poppins', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
}




/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('/assets/img/background/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--white);
    /* padding: 150px 0; */
    text-align: center;
    position: relative;
    /* overflow: hidden; */
}

.hero::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to top, var(--white), transparent);
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.hero-content h1 {
    @apply text-white/90;
}

.hero-content p {
    @apply text-white/60;
}

/* h1 {
    font-size: 3.125rem;
    color: var(--gray-900);
    font-weight: 500;
    line-height: 100%;
    letter-spacing: -0.125rem;
    margin: 0;
    font-family: "Inter Tight", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol";
} */

.hero h1 {
    font-size: 4rem;
    /* margin-bottom: 1.5rem; */
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease;
    line-height: 100%;
    letter-spacing: -0.125rem;
    margin: 0;
    font-family: "Inter Tight", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

.hero p {
    /* font-size: 1.4rem; */
    /* margin-bottom: 2.5rem; */
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s forwards;
    opacity: 0;
}



/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero h1 {
        @apply text-4xl;
    }

    /* section {
        padding: 70px 0;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    } */

    /* h2 {
        font-size: 2.5rem;
    } */
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--light-bg);
}

::-webkit-scrollbar-thumb {
    cursor: pointer;
    background: var(--primary-color);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

.dropdown .dropdown-menu {
    display: none;
}

.dropdown.show .dropdown-menu {
    display: block;
}

.dp-hidden,
.dp-hidden input {
    width: 0;
    margin: 0;
    border: none;
    padding: 0;
}

ngb-datepicker {
    @apply text-gray-600;
}

.ngb-dp-day.disabled {
    @apply !cursor-not-allowed;
}

.ngb-dp-day .btn-light.bg-primary.text-white {
    @apply !bg-primary-600 !text-white;
}

.ngb-dp-day.disabled .custom-day span,
.ngb-dp-day.disabled .custom-day small {
    @apply !text-[#D0D5DD] !cursor-not-allowed;
}

.ngb-dp-week.ngb-dp-weekdays {
    @apply mb-4;
    border-bottom: 1px solid #9ca3af;
}

.ngb-dp-week.ngb-dp-weekdays .ngb-dp-weekday {
    @apply font-semibold;
}

.ngb-dp-week.ngb-dp-weekdays .ngb-dp-weekday:last-child {
    @apply !text-red-600;
}

.ngb-dp-week .ngb-dp-day:last-child {
    @apply !text-red-600;
}

.ngb-dp-months .ngb-dp-month:first-child {
    @apply md:border-r md:border-dashed md:border-r-gray-400;
}

.ngb-dp-months .ngb-dp-month:first-child .ngb-dp-week.ngb-dp-weekdays {
    @apply pr-4;
}


ngb-datepicker {
    z-index: 100;
    background: #fff !important;
    border: gray 1px solid;
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
        0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.ngb-dp-day.hidden {
    display: block !important;
    color: #ea580c !important;
}

ngb-datepicker.dropdown-menu.show {
    display: block;
}

/* modal */
ngb-modal-backdrop {
    @apply bg-gray-900/20 bg-opacity-50 fixed inset-0 z-[1055] w-screen h-screen top-0 left-0;
}

ngb-modal-window {
    @apply fixed top-0 left-0 right-0 bottom-0 z-[1056] m-auto overflow-x-hidden overflow-y-auto outline-none flex items-center;
}

ngb-modal-window.modal .modal-dialog {
    @apply mx-auto relative w-auto my-7;
}

ngb-modal-window.modal .modal-dialog.modal-minw {
    @apply md:w-[450px] w-full;
}

ngb-modal-window.modal .modal-dialog.modal-lg {
    @apply md:w-[800px] lg:w-[900px] xl:w-[1000px] w-full;
}

ngb-modal-window.modal .modal-dialog.modal-sm {
    @apply w-[300px];
}

ngb-modal-window.modal .modal-dialog.modal-xl {
    @apply w-[1200px];
}

ngb-modal-window.modal .modal-dialog .modal-content {
    @apply relative flex flex-col w-full bg-white border border-gray-300 rounded-lg shadow-lg;
}

ngb-modal-window.modal.fade {
    transition: opacity 0.15s linear;
}

ngb-modal-window.modal.fade.show .modal-dialog {
    transform: none;
}

ngb-modal-window.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translateY(-50px);
}