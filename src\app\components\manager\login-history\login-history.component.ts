import { Component, inject, OnInit } from '@angular/core';
import { ActivityLogService } from '../../../services/activityLog/activity-log.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-login-history',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './login-history.component.html',
  styleUrl: './login-history.component.css'
})
export class LoginHistoryComponent implements OnInit {
  private activityLogService = inject(ActivityLogService);

  loginHistory: any[] = [];
  totalRecords: number = 0;
  pageIndex: number = 1;
  pageSize: number = 10;
  loading: boolean = false;
  countSuccess: number = 0;
  countFailed: number = 0;

  // Search filters
  keyword: string = '';
  status: string = '';
  filterTime: number | null = null;

  // Pagination
  get totalPages(): number {
    return Math.ceil(this.totalRecords / this.pageSize);
  }

  get pageNumbers(): number[] {
    const pages: number[] = [];
    const maxPages = 5; // Số trang hiển thị tối đa
    let startPage = Math.max(1, this.pageIndex - Math.floor(maxPages / 2));
    let endPage = Math.min(this.totalPages, startPage + maxPages - 1);

    if (endPage - startPage + 1 < maxPages) {
      startPage = Math.max(1, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  ngOnInit() {
    this.loadLoginHistory();
  }

  loadLoginHistory() {
    this.loading = true;
    const request = {
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      status: this.status || '',
      keyword: this.keyword,
      filterTime: this.filterTime || 10
    };

    this.activityLogService.getAuthLog(request).subscribe({
      next: (response: any) => {
        if (response.isSuccessed) {
          this.loginHistory = response.resultObj.items;
          this.totalRecords = response.resultObj.totalRecords;
          this.countSuccess = response.resultObj.countSuccess;
          this.countFailed = response.resultObj.countFalied;
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading login history:', error);
        this.loading = false;
      }
    });
  }

  onPageChange(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.pageIndex = page;
      this.loadLoginHistory();
    }
  }

  onSearch() {
    this.pageIndex = 1; // Reset to first page when searching
    this.loadLoginHistory();
  }

  onFilterChange() {
    this.onSearch();
  }

  get isFirstPage(): boolean {
    return this.pageIndex === 1;
  }

  get isLastPage(): boolean {
    return this.pageIndex === this.totalPages;
  }
}
