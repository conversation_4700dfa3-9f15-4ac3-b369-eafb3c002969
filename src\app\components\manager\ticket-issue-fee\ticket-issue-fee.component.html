<div class="container relative overflow-hidden shadow-lg bg-white dark:bg-gray-900 rounded-lg h-full min-w-full">
    <div class="px-4 pt-4 ">
        <div class="space-y-1.5 py-6 flex md:flex-row flex-col  items-center justify-between">
            <div>
                <h3 class="text-2xl font-semibold leading-none tracking-tight">Quản lý phí xuất vé</h3>
                <p class="text-sm text-gray-600">Quản lý cấu hình phí xuất vé cho vé quốc nội, quốc tế và theo
                    khu vực</p>
            </div>
            <div class="flex items-center space-x-2">
                <button type="button" (click)="addNewTicketIssueFee(content)"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary-400 text-white hover:bg-primary-400/90 h-10 px-4 py-2"
                    type="button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-circle-plus h-4 w-4 mr-2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M8 12h8"></path>
                        <path d="M12 8v8"></path>
                    </svg>Thêm mới
                </button>
            </div>
        </div>
        <div class="flex flex-col md:flex-row justify-between gap-4">
            <div class="flex items-center space-x-2 w-full md:w-1/2">
                <form #fSearch="ngForm" (ngSubmit)="onFormSearchSubmit(fSearch);" novalidate>
                    <div class="pb-2 bg-white dark:bg-gray-900">
                        <label for="table-search" class="sr-only">Search</label>
                        <div class="relative mt-1">
                            <div
                                class="absolute inset-y-0 rtl:inset-r-0 start-0 flex items-center ps-3 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                                </svg>
                            </div>
                            <input ngModel #Keyword="ngModel" name="Keyword" type="search"
                                class="block py-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-80 bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Tìm kiếm...">
                        </div>
                    </div>
                </form>
            </div>
            <div class="flex items-center space-x-2">
                <div ngbDropdown>
                    <button ngbDropdownToggle id="dropdown-filter"
                        class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"
                        type="button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-filter h-4 w-4 mr-2">
                            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                        </svg>Lọc: {{_selectedTypeFilter}}
                    </button>
                    <div ngbDropdownMenu aria-labelledby="dropdown-filter" state="closed" side="bottom"
                        class="z-50 hidden">
                        <div
                            class="z-50 bg-white w-full min-w-[8rem] overflow-hidden rounded-md border  p-1 text-gray-600 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2">
                            @for (type of _typeFilter; track type.key) {
                            <button type="button" ngbDropdownItem (click)="SetSelectedTypeFilter(type.key)"
                                class="relative w-full hover:bg-gray-200 flex cursor-pointer select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0">
                                {{type.value}}
                            </button>
                            }

                        </div>

                    </div>
                </div>
                <select [(ngModel)]="searchModel.PageSize" (change)="changePageSize($event)"
                    class="flex h-10 items-center cursor-pointer justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&amp;>span]:line-clamp-1 w-[120px]">
                    <option value="10" selected>10 dòng</option>
                    <option value="20">20 dòng</option>
                    <option value="50">50 dòng</option>
                    <option value="100">100 dòng</option>
                    <option value="200">200 dòng</option>

                </select>
            </div>
        </div>
    </div>

    <div class="container-table w-full  h-full relative overflow-auto px-4 pb-4 pt-1 sm:rounded-lg">


        <table
            class="w-full caption-bottom mt-4 h-fill-available rounded-lg text-sm text-left sm:mb-28 max-sm:mb-32 rtl:text-right text-gray-500 dark:text-white">
            <thead class="sticky -top-1 bg-white shadow-sm">
                <tr class="border-b transition-colors hover:bg-muted/50 ">
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 text-nowrap">ID </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 text-nowrap">
                        <div class="flex items-center">
                            Loại phí
                            <a (click)="sortTable('FeeType')">
                                <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                </svg>
                            </a>
                        </div>

                    </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 text-nowrap">
                        <div class="flex items-center">
                            Khu vực
                            <a (click)="sortTable('SubContinentCode')">
                                <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                </svg>
                            </a>
                        </div>
                    </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 text-nowrap">
                        <div class="flex items-center">
                            Phí người lớn
                            <a (click)="sortTable('FeeAmountAdult')">
                                <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                </svg>
                            </a>
                        </div>
                    </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 text-nowrap">
                        <div class="flex items-center">
                            Phí trẻ em
                            <a (click)="sortTable('FeeAmountChild')">
                                <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                </svg>
                            </a>
                        </div>
                    </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 text-nowrap">
                        <div class="flex items-center">
                            Phí em bé
                            <a (click)="sortTable('FeeAmountInfant')">
                                <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                </svg>
                            </a>
                        </div>
                    </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 text-nowrap">
                        <div class="flex items-center">
                            Trạng thái
                            <a (click)="sortTable('IsActive')">
                                <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                </svg>
                            </a>
                        </div>
                    </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 text-nowrap">
                        <div class="flex items-center">
                            Ghi chú
                            <a (click)="sortTable('Notes')">
                                <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                </svg>
                            </a>
                        </div>
                    </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 ">
                        <div class="flex items-center">
                            Ngày tạo
                            <a (click)="sortTable('CreatedAt')">
                                <svg class="w-3 h-3 ms-1.5 cursor-pointer hover:text-gray-600 hover:w-4 hover:h-4"
                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z" />
                                </svg>
                            </a>
                        </div>
                    </th>
                    <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 ">Thao tác</th>
                </tr>
            </thead>
            <tbody>

                @if(dataTable?.items?.length > 0){
                @for(item of dataTable?.items; track $index ){
                <tr class="border-b transition-colors hover:bg-gray-200 dark:hover:bg-gray-600 "
                    (dblclick)="handleDoubleClick(item)" style="cursor: pointer;">
                    <td class="p-4 align-middle ">{{item?.order}}</td>
                    <td class="p-4 align-middle text-nowrap">
                        @switch (item?.feeType) {
                        @case ("Domestic") {
                        Quốc nội
                        }
                        @case ("International") {
                        Quốc tế
                        }
                        @case ("Area") {
                        Theo khu vực
                        }
                        @default {
                        {{item?.feeType}}
                        }
                        }
                    </td>
                    <td class="p-4 align-middle text-nowrap">
                        @if(item?.subContinentName){
                        {{item?.subContinentName}}
                        }@else {
                        N/A
                        }
                    </td>
                    <td class="p-4 align-middle text-nowrap">{{formatCurrency(item?.feeAmountAdult)}}&nbsp;₫</td>
                    <td class="p-4 align-middle text-nowrap">{{formatCurrency(item?.feeAmountChild)}}&nbsp;₫</td>
                    <td class="p-4 align-middle text-nowrap">{{formatCurrency(item?.feeAmountInfant)}}&nbsp;₫</td>
                    <td class="p-4 align-middle text-nowrap">
                        <div [ngClass]="{'bg-green-400 hover:bg-green/80': item?.isActive, 'bg-red-400 hover:bg-red/80': !item?.isActive}"
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent  text-white  text-nowrap">
                            @if(item.isActive){
                            Đang hoạt động
                            }@else {
                            Không hoạt động
                            }
                        </div>
                    </td>
                    <td class="p-4 align-middle  max-w-[200px] truncate text-nowrap">
                        {{item?.notes}}
                    </td>
                    <td class="p-4 align-middle ">
                        <div class="text-sm">
                            <div class="text-nowrap">Tạo: {{item?.createdAt}}</div>
                            @if(item?.updatedAt){
                            <div class="text-nowrap">Cập nhật: {{item?.updatedAt}}</div>
                            }
                        </div>
                    </td>
                    <td class="p-4 align-middle ">
                        <div class="flex space-x-2">
                            <button (click)="editTicketIssueFee(item, content)"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-pencil h-4 w-4">
                                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                                    <path d="m15 5 4 4"></path>
                                </svg>
                                <span class="sr-only">Sửa</span>
                            </button>

                            @if(item?.isActive){
                            <button (click)="toggleActive(item)"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-red-500 text-white hover:bg-red-500/90 h-10 w-10"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-x h-4 w-4">
                                    <path d="M18 6 6 18"></path>
                                    <path d="m6 6 12 12"></path>
                                </svg>
                                <span class="sr-only">Vô hiệu hóa</span>
                            </button>
                            }@else {
                            <button (click)="toggleActive(item)"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-green-500 text-white hover:bg-green-500/90 h-10 w-10">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-check h-4 w-4">
                                    <path d="M20 6 9 17l-5-5"></path>
                                </svg>
                                <span class="sr-only"></span>
                            </button>
                            }
                            <button (click)="deleteTicketIssueFee(item, confirm)"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-red-500 text-white hover:bg-red-500/90 h-10 w-10"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-trash2 h-4 w-4">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                    <line x1="10" x2="10" y1="11" y2="17"></line>
                                    <line x1="14" x2="14" y1="11" y2="17"></line>
                                </svg>
                                <span class="sr-only">Xóa</span>
                            </button>
                        </div>
                    </td>
                </tr>
                }
                }@else {
                <tr
                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-100 even:dark:bg-gray-800 border-b dark:border-gray-700  hover:bg-gray-200 dark:hover:bg-gray-600">
                    <td class="px-6 py-4" colspan="10">
                        <div class="flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400">No data found</span>
                        </div>
                    </td>
                </tr>
                }
            </tbody>
        </table>

    </div>
    <nav class="pagination absolute bg-white sm:rounded-lg dark:bg-gray-600 w-full bottom-0 flex items-center flex-column flex-wrap md:flex-row justify-between p-4"
        aria-label="Table navigation">
        <span
            class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Showing
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable?.from}} - {{dataTable?.to}}</span> of
            <span class="font-semibold text-gray-900 dark:text-white">{{dataTable?.totalRecords}}</span></span>
        <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(1)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m17 16-4-4 4-4m-6 8-4-4 4-4" />
                    </svg>
                </a>
            </li>
            <li [ngClass]="{disabled:dataTable.pageIndex === 1}">
                <a (click)="getUrl(dataTable.pageIndex - 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m14 8-4 4 4 4" />
                    </svg>
                </a>
            </li>

            <li *ngFor="let page of range(startIndex, finishIndex); let i = index"
                [class.active]="page === dataTable.pageIndex">
                <a class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                    (click)="getUrl(page)" [title]="'Page ' + page">
                    {{ page }}
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageIndex + 1)"
                    class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m10 16 4-4-4-4" />
                    </svg>
                </a>
            </li>

            <li [ngClass]="{disabled:dataTable.pageIndex === dataTable.pageCount}">
                <a (click)="getUrl(dataTable.pageCount)"
                    class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m7 16 4-4-4-4m6 8 4-4-4-4" />
                    </svg>
                </a>
            </li>
        </ul>
    </nav>


</div>


<ng-template #content let-modal>
    <div class="  grid bg-white w-full max-w-lg  gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg sm:max-w-[500px]"
        tabindex="-1" style="pointer-events: auto;">
        <div class="flex flex-col space-y-1.5 text-center sm:text-left">
            <h2 class="text-lg font-semibold leading-none tracking-tight">Thêm cấu hình phí xuất vé mới</h2>
            <p class="text-sm text-muted-foreground">Nhập thông tin cho cấu hình phí xuất vé mới</p>
        </div>
        @if(this._ticketIssueFeeForm) {
        <form [formGroup]="this._ticketIssueFeeForm" (ngSubmit)="submitForm()">
            <div class="grid gap-4 py-4">
                <div class="grid grid-cols-4 items-center ">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                        for="feeType">Loại phí</label>
                    <select formControlName="FeeType"
                        class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&amp;>span]:line-clamp-1 col-span-3">
                        <option value='' disabled>Chọn loại phí</option>
                        <option value="Domestic">Quốc nội</option>
                        <option value="International">Quốc tế</option>
                        <option value="Area">Theo khu vực</option>
                    </select>
                    <div class="col-span-3 col-start-2">
                        <div *ngIf="(fm?.FeeType?.touched || isSubmit) && fm?.FeeType?.errors?.['required']"
                            class="text-red-500 text-xs mt-1">
                            Vui lòng chọn loại phí
                        </div>
                    </div>
                </div>
                <div [ngClass]="{'hidden': fm?.FeeType?.value !== 'Area'}" class="grid grid-cols-4 items-center ">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                        for="subContinentCode">Khu vực</label>
                    <ng-select [items]="_subContinentCode" bindLabel="value" bindValue="key"
                        formControlName="SubContinentCode"
                        class="flex h-10 w-full items-center justify-between rounded-md   text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&amp;>span]:line-clamp-1 col-span-3"
                        placeholder="Vui lòng chọn khu vực">
                        <ng-template ng-option-tmp let-item="item">
                            {{ item.value }}
                        </ng-template>
                    </ng-select>
                    <div class="col-span-3 col-start-2">
                        <div *ngIf="(fm?.SubContinentCode?.touched || isSubmit) && fm?.FeeType?.value === 'Area' && !fm?.SubContinentCode?.value"
                            class="text-red-500 text-xs mt-1">
                            Vui lòng chọn khu vực
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-4 items-center ">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                        for="FeeAmountAdult">Phí người lớn </label>
                    <input formControlName="FeeAmountAdult" (input)="onChangePrice($event)" inputmode="numeric"
                        [defaultValue]="0" [min]="0"
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 col-span-3"
                        id="FeeAmountAdult" type="text" inputmode="numeric">
                    <div class="col-span-3 col-start-2">
                        <div *ngIf="(fm?.FeeAmountAdult?.touched || isSubmit) && fm?.FeeAmountAdult?.errors?.['required']"
                            class="text-red-500 text-xs mt-1">
                            Vui lòng nhập phí người lớn
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-4 items-center ">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                        for="FeeAmountChild">Phí trẻ em </label>
                    <input formControlName="FeeAmountChild" (input)="onChangePrice($event)" inputmode="numeric"
                        [defaultValue]="0" [min]="0"
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 col-span-3"
                        id="FeeAmountChild" type="text" inputmode="numeric">
                    <div class="col-span-3 col-start-2">
                        <div *ngIf="(fm?.FeeAmountChild?.touched || isSubmit) && fm?.FeeAmountChild?.errors?.['required']"
                            class="text-red-500 text-xs mt-1">
                            Vui lòng nhập phí trẻ em
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-4 items-center ">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                        for="FeeAmountInfant">Phí em bé </label>
                    <input formControlName="FeeAmountInfant" (input)="onChangePrice($event)" inputmode="numeric"
                        [defaultValue]="0" [min]="0"
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 col-span-3"
                        id="FeeAmountInfant" type="text" inputmode="numeric">
                    <div class="col-span-3 col-start-2">
                        <div *ngIf="(fm?.FeeAmountInfant?.touched || isSubmit) && fm?.FeeAmountInfant?.errors?.['required']"
                            class="text-red-500 text-xs mt-1">
                            Vui lòng nhập phí em bé
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-4 items-center">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                        for="currency">Tiền tệ</label>
                    <select type="button" formControlName="Currency"
                        class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&amp;>span]:line-clamp-1 col-span-3">
                        <option value="VND">VND</option>
                    </select>
                    <div class="col-span-3 col-start-2">
                        <div *ngIf="(fm?.Currency?.touched || isSubmit) && fm?.Currency?.errors?.['required']"
                            class="text-red-500 text-xs mt-1">
                            Vui lòng chọn tiền tệ
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-4 items-center ">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                        for="isActive">Trạng thái</label>
                    <div class="flex items-center space-x-2 col-span-3">
                        <label class="inline-flex items-center me-3 cursor-pointer">
                            <input formControlName="IsActive" type="checkbox" class="sr-only peer">
                            <div [ngClass]="{'bg-green-500 peer-checked:bg-green-500 peer-focus:ring-green-300 dark:peer-focus:ring-green-800': fm?.IsActive?.value, 'bg-red-500 peer-checked:bg-red-500 peer-focus:ring-red-300 dark:peer-focus:ring-red-800': !fm?.IsActive?.value}"
                                class="relative w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 ">
                            </div>
                        </label>
                        @if(fm?.IsActive?.value) {
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-green-100 text-green-800 hover:bg-green-100">
                            Đang hoạt động</div>
                        } @else {
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-red-100 text-red-800 hover:bg-red-100">
                            Ngừng hoạt động</div>
                        }
                    </div>
                    <div class="col-span-3 col-start-2">
                        <div *ngIf="(fm?.IsActive?.touched || isSubmit) && fm?.IsActive?.errors?.['required']"
                            class="text-red-500 text-xs mt-1">
                            Vui lòng chọn trạng thái
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-4 items-center ">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                        for="notes">Ghi chú</label>
                    <textarea formControlName="Note"
                        class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 col-span-3"
                        id="notes"></textarea>
                    <div class="col-span-3 col-start-2">
                        <div *ngIf="(fm?.Note?.touched || isSubmit) && fm?.Note?.errors?.['required']"
                            class="text-red-500 text-xs mt-1">
                            Vui lòng nhập ghi chú
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 gap-2">
                <button type="button" (click)="cancelForm()"
                    class="inline-flex hover:bg-gray-200 items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                    Hủy</button>
                <button type="submit"
                    class="inline-flex  items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary-400 text-white hover:bg-primary-400/90 h-10 px-4 py-2">
                    @if(_isEdit) {
                    Cập nhật
                    } @else {
                    Thêm mới
                    }
                </button>
            </div>
        </form>
        }
        <button type="button" (click)="onCloseModal()"
            class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none ">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x h-4 w-4">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg>
            <span class="sr-only">Close</span>
        </button>
    </div>
</ng-template>

<ng-template #confirm let-modal>
    <div class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg sm:max-w-[425px]"
        tabindex="-1" style="pointer-events: auto;">
        <div class="flex flex-col space-y-1.5 text-center sm:text-left">
            <h2 id="radix-«r11»" class="text-lg font-semibold leading-none tracking-tight">Xác nhận xóa</h2>
            <p id="radix-«r12»" class="text-sm text-muted-foreground">Bạn có chắc chắn muốn xóa cấu hình phí xuất vé này
                không? Hành động này không thể hoàn tác.</p>
        </div>
        <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <button (click)="onCloseModalDelete()"
                class="inline-flex hover:bg-gray-200 items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                Hủy
            </button>
            <button (click)="onDeleteTicketIssueFee()"
                class="inline-flex bg-red-500  text-white items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-destructive text-destructive-foreground hover:bg-destructive/90 h-10 px-4 py-2">Xóa</button>
        </div>
        <button type="button" (click)="onCloseModalDelete()"
            class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"><svg
                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x h-4 w-4">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg><span class="sr-only">Close</span>
        </button>
    </div>

</ng-template>