import { isPlatformBrowser } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, Inject, PLATFORM_ID } from '@angular/core';

@Component({
  selector: 'app-flight-search',
  imports: [],
  templateUrl: './flight-search.component.html',
  styleUrl: './flight-search.component.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class FlightSearchComponent {

  constructor(@Inject(PLATFORM_ID) private platformId: Object) { }

  ngAfterViewInit() {
    if (isPlatformBrowser(this.platformId)) {
      const flightSearch = document.querySelector('flight-search');
      if (!flightSearch) return;

      const shadow = flightSearch.shadowRoot;
      if (!shadow) return;

      const parentDiv = shadow.querySelector('div');
      if (parentDiv) {
        parentDiv.classList.remove('p-2', 'mx-auto');
      }
    }

  }

}
