import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface SearchModel {
  PageIndex: number;
  PageSize: number;
  SortColumn: string;
  SortOrder: string;
  TypeDate: string;
  FromDate: string;
  ToDate: string;
  CodeKeyword: string;
  FilterStatus: number | null;
  FilterAirline: string;
  FilterAgent: string;
  CustomerKeyword: string;
}

@Injectable({
  providedIn: 'root'
})

export class SearchStateService<T> {
  private searchState = new BehaviorSubject<T | null>(null);
  searchState$ = this.searchState.asObservable();

  setSearchState(state: T) {
    this.searchState.next(state);
  }

  getSearchState(): T | null {
    return this.searchState.value;
  }

  clearSearchState() {
    this.searchState.next(null);
  }
}