import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagerComponent } from "./manager/manager.component";

const routes: Routes = [
    {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
    },
    {
        path: 'dashboard', title: 'Dashboard | Indeed Travel',
        loadComponent: () => import('./dashboard/dashboard.component').then(m => m.DashboardComponent),
        children: [
            {
                path: '',
                pathMatch: 'full',
                title: 'Dashboard | Indeed Travel',
                loadComponent: () => import('./dashboard-default/dashboard-default.component').then(m => m.DashboardDefaultComponent)
            },
            {
                path: 'api',
                title: 'Overview Api | Indeed Travel',
                loadComponent: () => import('./dashboard-api/dashboard-api.component').then(m => m.DashboardApiComponent)
            }

        ]
    },
    {
        path: 'news',
        title: 'News | Indeed Travel',
        loadComponent: () => import('./news/manager-news/manager-news.component').then(m => m.ManagerNewsComponent)
    },
    {
        path: 'news/category',
        title: 'Category News | Indeed Travel',
        loadComponent: () => import('./news/manager-category-news/manager-category-news.component').then(m => m.ManagerCategoryNewsComponent)
    },
    {
        path: 'news/create',
        // component: ManageNewsSaveComponent,
        loadComponent: () => import('./news/manager-news-save/manager-news-save.component').then(m => m.ManagerNewsSaveComponent),
        // canActivate: [authGuard], data: { screen: '' },
        title: 'Thêm tin tức mới | Indeed Travel'
    },
    {
        path: 'news/:id',
        // component: ManageNewsSaveComponent, 
        loadComponent: () => import('./news/manager-news-save/manager-news-save.component').then(m => m.ManagerNewsSaveComponent),
        // canActivate: [authGuard], data: { screen: '' },
        title: 'Update news | Indeed Travel'
    },

    {
        path: 'users',
        loadComponent: () => import('./manager/manager-users/manager-users.component').then(m => m.ManagerUsersComponent),
        title: 'Quản lý tài khoản',
    },
    {
        path: 'ticket',
        loadComponent: () => import('./ticket/manager-ticket/manager-ticket.component').then(m => m.ManagerTicketComponent),
        children: [
            {
                path: '',
                pathMatch: 'full',
                title: "Quản lý đơn hàng",
                loadComponent: () => import('./ticket/manager-ticket-dn/manager-ticket-dn.component').then(m => m.ManagerTicketDNComponent)
            },
            {
                path: ':id',
                title: "Chi tiết đơn hàng",
                loadComponent: () => import('./ticket/manager-ticket-details/manager-ticket-details.component').then(m => m.ManagerTicketDetailsComponent)
            }
        ]
    },
    {
        path: 'TicketIssueFee',
        loadComponent: () => import('./ticket-issue-fee/ticket-issue-fee.component').then(m => m.TicketIssueFeeComponent),
        title: 'Quản lý phí xuất vé',
    },
    {
        path: 'setting',
        title: 'Setting | Indeed Travel',
        loadComponent: () => import('./setting/setting/setting.component').then(m => m.SettingComponent),
        children: [
            {
                path: '',
                pathMatch: 'full',
                loadComponent: () => import('./setting/setting-default/setting-default.component').then(m => m.SettingDefaultComponent)
            },
            {
                path: 'email',
                title: 'Email | Indeed Travel',
                loadComponent: () => import('./setting/setting-email/setting-email.component').then(m => m.SettingEmailComponent)
            },
            {
                path: 'api',
                title: 'Api | Indeed Travel',
                loadComponent: () => import('./setting/setting-api/setting-api.component').then(m => m.SettingApiComponent)
            },
            {
                path: 'note-result',
                title: 'Note Result | Indeed Travel',
                loadComponent: () => import('./setting/setting-note-result/setting-note-result.component').then(m => m.SettingNoteResultComponent)
            },
            {
                path: 'payment',
                title: 'Payment | Indeed Travel',
                loadComponent: () => import('./setting/setting-info-payment/setting-info-payment.component').then(m => m.SettingInfoPaymentComponent),
                children: [
                    {
                        path: '',
                        redirectTo: 'cash',
                        pathMatch: 'full'
                    },
                    {
                        path: 'cash',
                        title: 'Cash | Indeed Travel',
                        loadComponent: () => import('./setting/setting-payment-cash/setting-payment-cash.component').then(m => m.SettingPaymentCashComponent)
                    },
                    {
                        path: 'credit',
                        title: 'Credit | Indeed Travel',
                        loadComponent: () => import('./setting/setting-payment-credit/setting-payment-credit.component').then(m => m.SettingPaymentCreditComponent)
                    }
                ]
            }
        ]
    },
    {
        path: 'profile',
        title: 'Thông tin cá nhân',
        loadComponent: () => import('./profile/profile.component').then(m => m.ProfileComponent)
    },
    {
        path: 'profile/login-history',
        title: 'Lịch sử đăng nhập',
        loadComponent: () => import('./login-history/login-history.component').then(m => m.LoginHistoryComponent)
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class ManagerRoutingModule { }