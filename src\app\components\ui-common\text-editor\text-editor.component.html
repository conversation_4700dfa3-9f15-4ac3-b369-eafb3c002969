<div class="relative">
    <div class="toolbar flex flex-wrap gap-2 p-3 bg-white rounded-t-lg border-b border-gray-200">
        <!-- <PERSON><PERSON><PERSON>/Redo lên đầu -->
        <button type="button" (click)="undo()" [disabled]="undoStack.length <= 1"
            [ngClass]="{'hover:bg-gray-300': undoStack.length > 1}"
            class="px-2 py-1 rounded bg-gray-400 text-gray-700 hover:bg-gray-300 transition"
            [class.opacity-30]="undoStack.length <= 1" title="Quay lại (Undo)">
            <!-- Undo SVG mới -->
            <svg class="w-6 h-6 text-gray-800 dark:text-white inline" aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 9h13a5 5 0 0 1 0 10H7M3 9l4-4M3 9l4 4" />
            </svg>
        </button>
        <button type="button" (click)="redo()" [disabled]="redoStack.length === 0"
            [ngClass]="{'hover:bg-gray-300': redoStack.length !== 0}"
            class="px-2 py-1 rounded bg-gray-400 text-gray-700 transition" [class.opacity-30]="redoStack.length === 0"
            title="Tiến tới (Redo)">
            <!-- Redo SVG mới -->
            <svg class="w-6 h-6 text-gray-800 dark:text-white inline" aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 9H8a5 5 0 0 0 0 10h9m4-10-4-4m4 4-4 4" />
            </svg>
        </button>
        <!-- Nhóm 1: Định dạng chữ cơ bản -->
        <button type="button" (click)="format('bold')"
            class="px-3 py-1 rounded bg-blue-500 text-white hover:bg-blue-600 font-bold transition" title="In đậm">
            <!-- Bold SVG với chữ B đậm -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" viewBox="0 0 24 24" fill="none">
                <text x="5" y="18" font-size="16" font-weight="bold" fill="currentColor">B</text>
            </svg>
        </button>
        <button type="button" (click)="format('italic')"
            class="px-3 py-1 rounded bg-green-500 text-white hover:bg-green-600 font-bold italic transition"
            title="In nghiêng">
            <!-- Italic SVG với chữ I nghiêng -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" viewBox="0 0 24 24" fill="none">
                <text x="9" y="18" font-size="16" font-style="italic" font-weight="bold" fill="currentColor">I</text>
            </svg>
        </button>
        <button type="button" (click)="format('underline')"
            class="px-3 py-1 rounded bg-yellow-500 text-white hover:bg-yellow-600 font-bold underline transition"
            title="Gạch chân">
            <!-- Underline SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path d="M6 4v6a6 6 0 0012 0V4" stroke="currentColor" stroke-width="2" fill="none" />
                <line x1="4" y1="20" x2="20" y2="20" stroke="currentColor" stroke-width="2" />
            </svg>
        </button>
        <button type="button" (click)="setStrikethrough()"
            class="px-3 py-1 rounded bg-gray-500 text-white hover:bg-gray-600 font-bold transition"
            title="Gạch giữa chữ">
            <!-- Strikethrough SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24">
                <text x="5" y="18" font-size="16" font-weight="bold" fill="currentColor">S</text>
                <line x1="4" y1="12" x2="20" y2="12" stroke="currentColor" stroke-width="2" />
            </svg>
        </button>
        <!-- Thêm nút đánh dấu chữ (highlight) -->
        <button type="button" (click)="setHighlight()"
            class="px-3 py-1 rounded bg-yellow-300 text-gray-900 hover:bg-yellow-400 font-bold transition"
            title="Đánh dấu chữ (Highlight)">
            <!-- Highlight SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24">
                <rect x="4" y="8" width="16" height="8" rx="2" fill="#fde047" />
                <text x="8" y="18" font-size="12" font-weight="bold" fill="currentColor">H</text>
            </svg>
        </button>
        <button type="button" (click)="clearFormatting()"
            class="px-2 py-1 rounded bg-red-200 text-red-700 hover:bg-red-300 transition" title="Xóa định dạng">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-eraser">
                <path d="M20 20H10L4 14l10-10 6 6-10 10" />
                <line x1="14" y1="10" x2="20" y2="16" />
            </svg>

        </button>
        <button type="button" (click)="insertLink()"
            class="px-2 py-1 rounded bg-blue-200 text-blue-700 hover:bg-blue-300 transition" title="Chèn liên kết">
            <!-- Link SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path d="M10 14a5 5 0 0 1 0-7l2-2a5 5 0 0 1 7 7l-1 1" stroke="currentColor" stroke-width="2"
                    fill="none" />
                <path d="M14 10a5 5 0 0 1 0 7l-2 2a5 5 0 0 1-7-7l1-1" stroke="currentColor" stroke-width="2"
                    fill="none" />
            </svg>
        </button>
        <button type="button" (click)="imageInput.click()"
            class="px-3 py-1 rounded bg-pink-500 text-white hover:bg-pink-600 transition" title="Chèn hình ảnh">
            <!-- Image SVG cập nhật mới -->
            <svg class="w-5 h-5 inline" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                viewBox="0 0 24 24">
                <path fill-rule="evenodd" d="M13 10a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H14a1 1 0 0 1-1-1Z"
                    clip-rule="evenodd" />
                <path fill-rule="evenodd"
                    d="M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12c0 .556-.227 1.06-.593 1.422A.999.999 0 0 1 20.5 20H4a2.002 2.002 0 0 1-2-2V6Zm6.892 12 3.833-5.356-3.99-4.322a1 1 0 0 0-1.549.097L4 12.879V6h16v9.95l-3.257-3.619a1 1 0 0 0-1.557.088L11.2 18H8.892Z"
                    clip-rule="evenodd" />
            </svg>
        </button>
        <input #imageInput type="file" accept="image/*" style="display:none" (change)="insertImage($event)">
        <!-- Chèn video -->
        <button type="button" (click)="openVideoForm()"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Chèn video">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24">
                <rect x="4" y="6" width="16" height="12" rx="2" fill="currentColor" />
                <polygon points="10,9 16,12 10,15" fill="#fff" />
            </svg>
        </button>

        <!-- Chèn bảng -->
        <button type="button" (click)="insertTable()"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Chèn bảng">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24">
                <rect x="4" y="4" width="16" height="16" rx="2" fill="currentColor" />
                <rect x="4" y="12" width="16" height="2" fill="#fff" />
                <rect x="12" y="4" width="2" height="16" fill="#fff" />
            </svg>
        </button>
        <!-- Thêm nút chia cột -->
        <select (change)="setColumns($event)" class="px-2 py-1 rounded border border-gray-300" title="Chia cột văn bản">
            <option value="">Chia cột</option>
            <option value="1">1 cột</option>
            <option value="2">2 cột</option>
            <option value="3">3 cột</option>
            <option value="4">4 cột</option>
        </select>
        <div class="relative">
            <!-- Nút mở bảng màu chữ -->
            <button type="button" class="text-color-button" (click)="openTextColorPalette($event)" title="Chọn màu chữ"
                style="background: white; border: none; padding: 0 8px; border-radius: 6px; height: 40px; width: 44px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                <span style="font-weight: bold; font-size: 18px; color: #222; line-height: 1;">A</span>
                <span class="text-color-bar" [style.background]="selectedTextColor"
                    style="display:block;width:22px;height:4px;border-radius:2px;margin-top:2px;"></span>
            </button>
            <!-- Bảng màu chữ -->
            <div *ngIf="showTextColorPalette" class="text-color-palette" [style.top.px]="textColorPalettePosition.top"
                [style.left.px]="textColorPalettePosition.left">
                <div class="color-grid">
                    <div *ngFor="let row of textColors" class="color-row">
                        <span *ngFor="let color of row" class="color-item" [style.background]="color"
                            (mousedown)="onPaletteColorMouseDown($event, color)" [title]="color"></span>
                    </div>
                </div>
                <div class="color-actions flex justify-between">

                    <span class="palette-action" style="background:#fff;border:1px solid #ccc;"
                        (mousedown)="clearTextColor(); showTextColorPalette=false" title="Xóa màu">
                        <svg width="18" height="18">
                            <line x1="3" y1="15" x2="15" y2="3" stroke="red" stroke-width="2" />
                        </svg>
                    </span>

                    <!-- Phải: Color picker -->
                    <span title="Chọn màu nâng cao" style="position: relative;">
                        <svg width="24" height="24">
                            <path
                                d="M12 3a9 9 0 0 0 0 18 1.5 1.5 0 0 0 1.1-2.5c-.2-.3-.4-.6-.4-1 0-.8.7-1.5 1.5-1.5H16a5 5 0 0 0 5-5c0-4.4-4-8-9-8Zm-5.5 9a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm3-4a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm3 4a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Z"
                                fill-rule="nonzero"></path>
                        </svg>
                        <input #advancedColorInput type="color"
                            style="opacity:0;position:absolute;left:0;top:0;width:100%;height:100%;cursor:pointer;"
                            (change)="onAdvancedColorPicked($event)">
                    </span>
                </div>
            </div>
        </div>

        <div class="relative">
            <!-- Thêm nút chọn màu nền sau nút chọn màu chữ -->
            <button class="toolbar-button bg-color-button" (click)="openBgColorPalette($event)" title="Màu nền"
                style="background: white; border: none; padding: 0 8px; border-radius: 6px; height: 40px; width: 44px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </button>
            <!-- Thêm palette màu nền -->
            <div class="bg-color-palette" *ngIf="showBgColorPalette" [style.top.px]="bgColorPalettePosition.top"
                [style.left.px]="bgColorPalettePosition.left">
                <div class="color-grid">
                    <div *ngFor="let row of textColors" class="color-row">
                        <div *ngFor="let color of row" class="color-item" [style.background-color]="color"
                            (mousedown)="onBgColorPaletteMouseDown($event, color)">
                        </div>
                    </div>
                </div>
                <div class="color-actions flex justify-between">
                    <span class="palette-action" style="background:#fff;border:1px solid #ccc;"
                        (mousedown)="clearBackgroundColor(); showBgColorPalette=false" title="Xóa màu">
                        <svg width="18" height="18">
                            <line x1="3" y1="15" x2="15" y2="3" stroke="red" stroke-width="2" />
                        </svg>
                    </span>

                    <!-- Phải: Color picker -->
                    <span title="Chọn màu nâng cao" style="position: relative;">
                        <svg width="24" height="24">
                            <path
                                d="M12 3a9 9 0 0 0 0 18 1.5 1.5 0 0 0 1.1-2.5c-.2-.3-.4-.6-.4-1 0-.8.7-1.5 1.5-1.5H16a5 5 0 0 0 5-5c0-4.4-4-8-9-8Zm-5.5 9a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm3-4a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm3 4a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Z"
                                fill-rule="nonzero"></path>
                        </svg>
                        <input #advancedBgColorInput type="color"
                            style="opacity:0;position:absolute;left:0;top:0;width:100%;height:100%;cursor:pointer;"
                            (change)="onAdvancedBgColorPicked($event)">
                    </span>
                    <!-- <button class="clear-color" (click)="clearBackgroundColor()">Xóa màu</button> -->
                </div>
            </div>
        </div>

        <!-- Nhóm 2: Cỡ chữ và màu chữ -->
        <!-- Thêm chọn font chữ -->
        <select (change)="setFontFamily($event)" class="px-2 py-1 rounded border border-gray-300" title="Chọn font chữ"
            [value]="currentFontFamily" style="min-width:120px;">
            <option value="">Font chữ</option>
            <option *ngFor="let font of fontFamilies" [value]="font" [selected]="font === currentFontFamily"
                [ngStyle]="{'font-family': font}">
                {{font}}
            </option>
        </select>
        <select [(ngModel)]="currentFontSize" (change)="setFontSize($event)"
            class="px-2 py-1 rounded border border-gray-300" title="Chọn cỡ chữ">

            <option *ngFor="let size of fontSizes" [value]="size">{{size}}</option>
        </select>




        <!-- Nhóm 3: Undo/Redo -->
        <!-- Removed duplicate Undo/Redo buttons -->
        <!-- Nhóm 4: Định dạng chữ nâng cao -->
        <button type="button" (click)="setTextTransform('uppercase')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Chữ HOA">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <text x="4" y="18" font-size="16" font-weight="bold" fill="currentColor">A</text>
                <text x="14" y="18" font-size="16" font-weight="bold" fill="currentColor">A</text>
            </svg>
        </button>
        <button type="button" (click)="setTextTransform('lowercase')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Chữ thường">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <text x="4" y="18" font-size="16" font-weight="bold" fill="currentColor">a</text>
                <text x="14" y="18" font-size="16" font-weight="bold" fill="currentColor">a</text>
            </svg>
        </button>
        <button type="button" (click)="setTextTransform('capitalize')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Viết hoa đầu từ">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <text x="4" y="18" font-size="16" font-weight="bold" fill="currentColor">A</text>
                <text x="14" y="18" font-size="16" font-weight="bold" fill="currentColor">a</text>
            </svg>
        </button>
        <!-- Nhóm 5: Danh sách -->
        <app-custom-select [options]="bulletListOptions" [placeholder]="bulletIcon" [value]="selectedBulletType"
            (valueChange)="onListTypeChange($event, 'ul')" title="Chọn kiểu bullet list">
        </app-custom-select>
        <app-custom-select [options]="numberListOptions" [placeholder]="numberIcon" [value]="selectedNumberType"
            (valueChange)="onListTypeChange($event, 'ol')" title="Chọn kiểu number list">
        </app-custom-select>
        <!-- Nhóm 6: Căn lề -->
        <button type="button" (click)="align('justifyLeft', true)"
            class="px-3 py-1 rounded bg-gray-400 text-white hover:bg-gray-500 transition" title="Căn trái">
            <!-- Align Left SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <rect x="4" y="6" width="16" height="2" rx="1" fill="currentColor" />
                <rect x="4" y="10" width="10" height="2" rx="1" fill="currentColor" />
                <rect x="4" y="14" width="14" height="2" rx="1" fill="currentColor" />
                <rect x="4" y="18" width="8" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="align('justifyCenter', true)"
            class="px-3 py-1 rounded bg-gray-400 text-white hover:bg-gray-500 transition" title="Căn giữa">
            <!-- Align Center SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <rect x="6" y="6" width="12" height="2" rx="1" fill="currentColor" />
                <rect x="8" y="10" width="8" height="2" rx="1" fill="currentColor" />
                <rect x="5" y="14" width="14" height="2" rx="1" fill="currentColor" />
                <rect x="7" y="18" width="10" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="align('justifyRight', true)"
            class="px-3 py-1 rounded bg-gray-400 text-white hover:bg-gray-500 transition" title="Căn phải">
            <!-- Align Right SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <rect x="4" y="6" width="16" height="2" rx="1" fill="currentColor" />
                <rect x="10" y="10" width="10" height="2" rx="1" fill="currentColor" />
                <rect x="6" y="14" width="14" height="2" rx="1" fill="currentColor" />
                <rect x="12" y="18" width="8" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="align('justifyFull', true)"
            class="px-3 py-1 rounded bg-gray-400 text-white hover:bg-gray-500 transition" title="Căn đều 2 bên">
            <!-- Align Justify SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <rect x="4" y="6" width="16" height="2" rx="1" fill="currentColor" />
                <rect x="4" y="10" width="16" height="2" rx="1" fill="currentColor" />
                <rect x="4" y="14" width="16" height="2" rx="1" fill="currentColor" />
                <rect x="4" y="18" width="16" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <!-- Nhóm 7: Thụt lề, đoạn, ảnh -->
        <button type="button" (click)="decreaseIndent()"
            class="px-3 py-1 rounded bg-gray-300 text-gray-700 hover:bg-gray-400 transition" title="Giảm thụt lề">
            <!-- Decrease Indent SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path d="M20 6v12" stroke="currentColor" stroke-width="2" />
                <path d="M4 12l6-6v12l-6-6z" stroke="currentColor" stroke-width="2" fill="none" />
            </svg>
        </button>
        <button type="button" (click)="increaseIndent()"
            class="px-3 py-1 rounded bg-gray-300 text-gray-700 hover:bg-gray-400 transition" title="Tăng thụt lề">
            <!-- Increase Indent SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path d="M4 6v12" stroke="currentColor" stroke-width="2" />
                <path d="M20 12l-6-6v12l6-6z" stroke="currentColor" stroke-width="2" fill="none" />
            </svg>
        </button>
        <button type="button" (click)="indentFirstLine()"
            class="px-3 py-1 rounded bg-gray-300 text-gray-700 hover:bg-gray-400 transition" title="Thụt đầu dòng đoạn">
            <!-- Indent First Line SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="4" y="6" width="16" height="2" rx="1" fill="currentColor" />
                <rect x="8" y="10" width="12" height="2" rx="1" fill="currentColor" />
                <rect x="8" y="14" width="12" height="2" rx="1" fill="currentColor" />
                <rect x="8" y="18" width="12" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="indentFirstLine(true)"
            class="px-3 py-1 rounded bg-gray-300 text-gray-700 hover:bg-gray-400 transition"
            title="Bỏ thụt đầu dòng đoạn">
            <!-- Remove Indent First Line SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="4" y="6" width="16" height="2" rx="1" fill="currentColor" />
                <rect x="8" y="10" width="12" height="2" rx="1" fill="currentColor" />
                <rect x="8" y="14" width="12" height="2" rx="1" fill="currentColor" />
                <rect x="8" y="18" width="12" height="2" rx="1" fill="currentColor" />
                <line x1="4" y1="6" x2="8" y2="18" stroke="red" stroke-width="2" />
            </svg>
        </button>
        <button type="button" (click)="format('insertParagraph')"
            class="px-3 py-1 rounded bg-purple-500 text-white hover:bg-purple-600 transition" title="Chèn đoạn mới">
            <!-- Paragraph SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <rect x="6" y="4" width="12" height="2" rx="1" fill="currentColor" />
                <rect x="6" y="8" width="8" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="format('insertHorizontalRule')"
            class="px-3 py-1 rounded bg-indigo-500 text-white hover:bg-indigo-600 transition"
            title="Chèn đường kẻ ngang">
            <!-- HR SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <rect x="4" y="12" width="16" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <!-- Nhóm 8: Ảnh -->
        <!-- Thêm cài đặt padding cho ảnh -->
        <div class="flex items-center gap-1">
            <label
                class="flex items-center cursor-pointer select-none px-2 py-1 rounded bg-gray-100 hover:bg-gray-200 transition"
                title="Padding cho ảnh">
                <svg class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <rect x="6" y="6" width="12" height="12" rx="2" fill="#e5e7eb" />
                    <rect x="10" y="10" width="4" height="4" rx="1" fill="#a3a3a3" />
                </svg>
                <span class="text-xs text-gray-700 mr-1">Padding ảnh</span>
                <input #imgPadInput type="number" min="0" max="100" value="0"
                    (change)="setImagePadding(imgPadInput.value)"
                    class="w-14 text-center border border-gray-300 rounded px-1 py-0.5 ml-1" style="width:50px;" />
                <span class="text-xs text-gray-500 ml-1">px</span>
            </label>
        </div>
        <button type="button" (click)="alignImage('left')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Ảnh trái">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 20 20" stroke="currentColor">
                <rect x="2" y="5" width="8" height="10" rx="1" fill="currentColor" />
                <rect x="12" y="7" width="6" height="2" rx="1" fill="currentColor" />
                <rect x="12" y="11" width="6" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="alignImage('center')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Ảnh giữa">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 20 20" stroke="currentColor">
                <rect x="6" y="5" width="8" height="10" rx="1" fill="currentColor" />
                <rect x="2" y="7" width="4" height="2" rx="1" fill="currentColor" />
                <rect x="14" y="7" width="4" height="2" rx="1" fill="currentColor" />
                <rect x="2" y="11" width="4" height="2" rx="1" fill="currentColor" />
                <rect x="14" y="11" width="4" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="alignImage('right')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Ảnh phải">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 20 20" stroke="currentColor">
                <rect x="10" y="5" width="8" height="10" rx="1" fill="currentColor" />
                <rect x="2" y="7" width="6" height="2" rx="1" fill="currentColor" />
                <rect x="2" y="11" width="6" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>

        <button type="button" (click)="alignImage('inline-left')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition"
            title="Ảnh cùng text bên trái">
            <!-- Inline Left SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="4" y="8" width="6" height="8" rx="1" fill="currentColor" />
                <rect x="12" y="10" width="8" height="2" rx="1" fill="currentColor" />
                <rect x="12" y="14" width="8" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="alignImage('inline-center')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition"
            title="Ảnh cùng text căn giữa">
            <!-- Inline Center SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="9" y="8" width="6" height="8" rx="1" fill="currentColor" />
                <rect x="4" y="10" width="16" height="2" rx="1" fill="currentColor" />
                <rect x="4" y="14" width="16" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="alignImage('inline-right')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition"
            title="Ảnh cùng text bên phải">
            <!-- Inline Right SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="14" y="8" width="6" height="8" rx="1" fill="currentColor" />
                <rect x="4" y="10" width="8" height="2" rx="1" fill="currentColor" />
                <rect x="4" y="14" width="8" height="2" rx="1" fill="currentColor" />
            </svg>
        </button>
        <button type="button" (click)="alignImage('inline-none')"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Tắt ảnh cùng text">
            <!-- Inline None SVG -->
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="4" y="8" width="16" height="8" rx="1" fill="#e5e7eb" />
                <line x1="4" y1="8" x2="20" y2="16" stroke="red" stroke-width="2" />
            </svg>
        </button>
        <button type="button" (click)="cropSelectedImage()"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Cắt ảnh (crop)">
            <!-- Crop SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M6 2v14a2 2 0 0 0 2 2h14" />
                <path d="M18 22V8a2 2 0 0 0-2-2H2" />
                <line x1="3" y1="21" x2="21" y2="3" />
            </svg>
        </button>
        <!-- Chèn tiêu đề -->
        <select (change)="onHeadingChange($event)" class="px-2 py-1 rounded border border-gray-300"
            title="Chèn tiêu đề">
            <option value="">Tiêu đề</option>
            <option value="1">H1</option>
            <option value="2">H2</option>
            <option value="3">H3</option>
            <option value="4">H4</option>
            <option value="5">H5</option>
            <option value="6">H6</option>
        </select>

        <button type="button" (click)="insertTableRowAbove()"
            class="px-2 py-1 rounded bg-blue-100 text-blue-700 hover:bg-blue-200 transition"
            title="Chèn dòng phía trên">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-row-insert-top">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M4 18v-4a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1z" />
                <path d="M12 9v-4" />
                <path d="M10 7l4 0" />
            </svg>
        </button>
        <button type="button" (click)="insertTableRowBelow()"
            class="px-2 py-1 rounded bg-blue-100 text-blue-700 hover:bg-blue-200 transition"
            title="Chèn dòng phía dưới">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-row-insert-bottom">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M20 6v4a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1v-4a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1z" />
                <path d="M12 15l0 4" />
                <path d="M14 17l-4 0" />
            </svg>
        </button>
        <button type="button" (click)="insertTableColLeft()"
            class="px-2 py-1 rounded bg-green-100 text-green-700 hover:bg-green-200 transition"
            title="Chèn cột bên trái">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-column-insert-left">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M14 4h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-14a1 1 0 0 1 1 -1z" />
                <path d="M5 12l4 0" />
                <path d="M7 10l0 4" />
            </svg>
        </button>
        <button type="button" (click)="insertTableColRight()"
            class="px-2 py-1 rounded bg-green-100 text-green-700 hover:bg-green-200 transition"
            title="Chèn cột bên phải">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-column-insert-right">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M6 4h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-14a1 1 0 0 1 1 -1z" />
                <path d="M15 12l4 0" />
                <path d="M17 10l0 4" />
            </svg>
        </button>
        <button type="button" (click)="deleteTableRow()"
            class="px-2 py-1 rounded bg-red-100 text-red-700 hover:bg-red-200 transition" title="Xóa dòng hiện tại">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-row-remove">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M20 6v4a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1v-4a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1z" />
                <path d="M10 16l4 4" />
                <path d="M10 20l4 -4" />
            </svg>
        </button>
        <button type="button" (click)="deleteTableCol()"
            class="px-2 py-1 rounded bg-red-100 text-red-700 hover:bg-red-200 transition" title="Xóa cột hiện tại">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="icon icon-tabler icons-tabler-outline icon-tabler-column-remove">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M6 4h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-14a1 1 0 0 1 1 -1z" />
                <path d="M16 10l4 4" />
                <path d="M16 14l4 -4" />
            </svg>
        </button>
        <button type="button" (click)="deleteTable()"
            class="px-2 py-1 rounded bg-red-200 text-red-800 hover:bg-red-300 transition" title="Xóa bảng">
            <svg width="24" height="24" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g fill="#212121" fill-rule="nonzero">
                        <path
                            d="M17.5,12 C20.5376,12 23,14.4624 23,17.5 C23,20.5376 20.5376,23 17.5,23 C14.4624,23 12,20.5376 12,17.5 C12,14.4624 14.4624,12 17.5,12 Z M11.1739,16 C11.0602,16.4815 11,16.9837 11,17.5 C11,18.6894769 11.3195266,19.8043976 11.8774103,20.7635139 L12.0218,21 L9.5,21 L9.5,16 L11.1739,16 Z M8,16 L8,21 L6.25,21 C4.51696414,21 3.10075377,19.6435215 3.00514477,17.9344215 L3,17.75 L3,16 L8,16 Z M15.1464,15.1464 C14.9512,15.3417 14.9512,15.6583 15.1464,15.8536 L16.7929,17.5 L15.1464,19.1464 C14.9512,19.3417 14.9512,19.6583 15.1464,19.8536 C15.3417,20.0488 15.6583,20.0488 15.8536,19.8536 L17.5,18.2071 L19.1464,19.8536 C19.3417,20.0488 19.6583,20.0488 19.8536,19.8536 C20.0488,19.6583 20.0488,19.3417 19.8536,19.1464 L18.2071,17.5 L19.8536,15.8536 C20.0488,15.6583 20.0488,15.3417 19.8536,15.1464 C19.6583,14.9512 19.3417,14.9512 19.1464,15.1464 L17.5,16.7929 L15.8536,15.1464 C15.6583,14.9512 15.3417,14.9512 15.1464,15.1464 Z M14.5,9.5 L14.5,11.7322 C13.3176,12.3485 12.3485,13.3176 11.7322,14.5 L9.5,14.5 L9.5,9.5 L14.5,9.5 Z M8,9.5 L8,14.5 L3,14.5 L3,9.5 L8,9.5 Z M21,9.5 L21,12.0218 C19.9897,11.375 18.7886,11 17.5,11 C16.9837,11 16.4815,11.0602 16,11.1739 L16,9.5 L21,9.5 Z M17.75,3 C19.4830069,3 20.8992442,4.35645051 20.9948551,6.06557565 L21,6.25 L21,8 L16,8 L16,3 L17.75,3 Z M14.5,3 L14.5,8 L9.5,8 L9.5,3 L14.5,3 Z M8,3 L8,8 L3,8 L3,6.25 C3,4.51696414 4.35645051,3.10075377 6.06557565,3.00514477 L6.25,3 L8,3 Z">

                        </path>
                    </g>
                </g>
            </svg>
        </button>

        <!-- Thêm nút chèn checklist -->
        <button type="button" (click)="insertChecklist()"
            class="px-2 py-1 rounded bg-green-200 text-green-700 hover:bg-green-300 transition" title="Chèn checklist">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24">
                <rect x="3" y="5" width="18" height="14" rx="2" fill="currentColor" fill-opacity="0.15" />
                <rect x="5" y="7" width="3" height="3" rx="1" fill="#22c55e" />
                <rect x="5" y="12" width="3" height="3" rx="1" fill="#22c55e" />
                <rect x="5" y="17" width="3" height="3" rx="1" fill="#22c55e" />
                <rect x="10" y="7" width="9" height="2" rx="1" fill="#a3a3a3" />
                <rect x="10" y="12" width="9" height="2" rx="1" fill="#a3a3a3" />
                <rect x="10" y="17" width="9" height="2" rx="1" fill="#a3a3a3" />
            </svg>
        </button>
        <!-- Chèn trích dẫn -->
        <button type="button" (click)="insertBlockquote()"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Chèn trích dẫn">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24"><text x="4" y="18" font-size="16"
                    font-weight="bold" fill="currentColor">"</text></svg>
        </button>
        <!-- Tăng/giảm giãn dòng -->
        <app-custom-select [options]="lineHeightOptions" [placeholder]="lineHeightIcon" [value]="selectedLineHeight"
            (valueChange)="onLineHeightChange($event)" title="Giãn dòng">
        </app-custom-select>

        <!-- Chèn ngày giờ hiện tại -->
        <button type="button" (click)="insertCurrentDateTime()"
            class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition" title="Chèn ngày giờ">
            <svg class="w-5 h-5 inline" fill="none" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" />
                <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" />
            </svg>
        </button>
        <!-- Chèn emoji và ký tự đặc biệt -->
        <div class="relative">
            <button type="button" (click)="openEmojiPicker($event)"
                class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition"
                title="Chọn emoji">😊</button>
            <!-- Emoji Picker -->
            <div *ngIf="showEmojiPicker" class="emoji-picker" [style.top.px]="emojiPickerPosition.top"
                [style.left.px]="emojiPickerPosition.left">
                <div class="picker-header">
                    <span>Emoji</span>
                    <button class="close-button" (click)="closePickers()">×</button>
                </div>
                <div class="picker-content">
                    <div class="emoji-grid">
                        <button *ngFor="let emoji of emojis" class="emoji-item" (click)="insertEmoji(emoji)"
                            [title]="emoji">
                            {{emoji}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="relative">
            <button type="button" (click)="openSpecialCharPicker($event)"
                class="px-2 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 transition"
                title="Chọn ký tự đặc biệt">Ω</button>
            <!-- Special Character Picker -->
            <div *ngIf="showSpecialCharPicker" class="special-char-picker"
                [style.top.px]="specialCharPickerPosition.top" [style.left.px]="specialCharPickerPosition.left">
                <div class="picker-header">
                    <span>Ký tự đặc biệt</span>
                    <button class="close-button" (click)="closePickers()">×</button>
                </div>
                <div class="picker-content">
                    <div class="special-char-grid">
                        <button *ngFor="let char of specialChars" class="special-char-item"
                            (click)="insertSpecialChar(char)" [title]="char">
                            {{char}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="editor-stats" style="margin-top: 8px; font-size: 13px; color: #666;">
            {{ countWordsAndChars().words }} từ, {{ countWordsAndChars().chars }} ký tự
        </div>


    </div>

    <div #editorRef class="editor prose prose-sm max-w-none contenteditable-true" contenteditable="true"
        (input)="onInput($event)" (blur)="onBlur()" (click)="onEditorClick($event)" [attr.contenteditable]="true"
        [attr.style]="' min-height:300px;  padding:10px; background:#fff; outline:none;'"
        [ngClass]="{'editor': true, 'prose': true, 'prose-sm': true, 'max-w-none': true}">
    </div>

    <!-- Toolbar nổi cho ảnh khi chuột phải vào ảnh -->
    <!-- <div *ngIf="showImageToolbar && imageToolbarTarget" class="editor-image-toolbar" [ngStyle]="{
            top: imageToolbarPosition.top + 'px',
            left: imageToolbarPosition.left + 'px'
        }" style="position:absolute;pointer-events:auto;">
        <button type="button" (click)="alignImage('left'); showImageToolbar=false;">Trái</button>
        <button type="button" (click)="alignImage('center'); showImageToolbar=false;">Giữa</button>
        <button type="button" (click)="alignImage('right'); showImageToolbar=false;">Phải</button>
        <span class="divider"></span>
        <button type="button" (click)="cropSelectedImage(); showImageToolbar=false;">Cắt ảnh</button>
        <span class="divider"></span>
        <button type="button" (click)="setImagePadding(8); showImageToolbar=false;">Padding 8px</button>
        <button type="button" (click)="setImagePadding(0); showImageToolbar=false;">Không padding</button>
    </div> -->

    <!-- Form chèn link -->
    <div *ngIf="showLinkForm"
        style="position:fixed; top:0; left:0; width:100vw; height:100vh; z-index:1000; background:rgba(0,0,0,0.25); display:flex; align-items:center; justify-content:center;">
        <div
            style="background:#fff; border-radius:8px; box-shadow:0 2px 16px #0002; padding:24px 20px; min-width:320px; max-width:90vw;">
            <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Tiêu đề hiển thị</label>
                <input [(ngModel)]="linkForm.text" type="text" class="w-full border rounded px-2 py-1"
                    placeholder="Tiêu đề liên kết">
            </div>
            <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Địa chỉ liên kết (URL)</label>
                <input [(ngModel)]="linkForm.url" type="text" class="w-full border rounded px-2 py-1"
                    placeholder="https://example.com">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium mb-1">Mở liên kết ở</label>
                <select [(ngModel)]="linkForm.target" class="w-full border rounded px-2 py-1">
                    <option value="">Mặc định</option>
                    <option value="_blank">Tab mới (_blank)</option>
                    <option value="_self">Tab hiện tại (_self)</option>
                </select>
            </div>
            <div class="flex gap-2 justify-end">
                <button type="button" (click)="confirmInsertLink()"
                    class="px-3 py-1 rounded bg-blue-500 text-white hover:bg-blue-600">Chèn</button>
                <button type="button" (click)="cancelInsertLink()"
                    class="px-3 py-1 rounded bg-gray-300 text-gray-700 hover:bg-gray-400">Hủy</button>
            </div>
        </div>
    </div>

    <!-- Form chèn bảng -->
    <div *ngIf="showTableForm"
        style="position:fixed; top:0; left:0; width:100vw; height:100vh; z-index:1000; background:rgba(0,0,0,0.25); display:flex; align-items:center; justify-content:center;">
        <div
            style="background:#fff; border-radius:8px; box-shadow:0 2px 16px #0002; padding:24px 20px; min-width:320px; max-width:90vw;">
            <div class="mb-3 flex gap-4">
                <label class="block text-sm font-medium mb-1 flex-1">
                    Số dòng
                    <input [(ngModel)]="tableForm.rows" type="number" min="1" max="20"
                        class="w-full border rounded px-2 py-1 mt-1" />
                </label>
                <label class="block text-sm font-medium mb-1 flex-1">
                    Số cột
                    <input [(ngModel)]="tableForm.cols" type="number" min="1" max="20"
                        class="w-full border rounded px-2 py-1 mt-1" />
                </label>
            </div>
            <div class="flex gap-2 justify-end mt-4">
                <button type="button" (click)="confirmInsertTable()"
                    class="px-3 py-1 rounded bg-blue-500 text-white hover:bg-blue-600">Chèn bảng</button>
                <button type="button" (click)="cancelInsertTable()"
                    class="px-3 py-1 rounded bg-gray-300 text-gray-700 hover:bg-gray-400">Hủy</button>
            </div>
        </div>
    </div>

    <!-- Form chèn video -->
    <div *ngIf="showVideoForm"
        style="position:fixed; top:0; left:0; width:100vw; height:100vh; z-index:1000; background:rgba(0,0,0,0.25); display:flex; align-items:center; justify-content:center;">
        <div
            style="background:#fff; border-radius:8px; box-shadow:0 2px 16px #0002; padding:24px 20px; min-width:320px; max-width:90vw;">
            <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Nhập URL video (Youtube, Vimeo, ...)</label>
                <input [(ngModel)]="videoForm.url" type="text" class="w-full border rounded px-2 py-1"
                    placeholder="https://www.youtube.com/watch?v=..." />
            </div>
            <div class="flex gap-2 justify-end mt-4">
                <button type="button" (click)="confirmInsertVideo()"
                    class="px-3 py-1 rounded bg-blue-500 text-white hover:bg-blue-600">Chèn video</button>
                <button type="button" (click)="cancelInsertVideo()"
                    class="px-3 py-1 rounded bg-gray-300 text-gray-700 hover:bg-gray-400">Hủy</button>
            </div>
        </div>
    </div>
</div>