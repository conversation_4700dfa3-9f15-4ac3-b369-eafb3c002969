<div
    class=" mt-2 h-full relative  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
    <div class="rounded-lg  border bg-card text-card-foreground shadow-sm">
        <div class="flex flex-col space-y-1.5 p-6">
            <h3 class="text-2xl font-semibold leading-none tracking-tight">Thông Tin Địa Điểm Thanh Toán
                T<PERSON>ền Mặt</h3>
            <p class="text-sm text-muted-foreground">Cập nhật thông tin địa điểm thanh toán tiền mặt cho
                khách hàng</p>
        </div>
        <div class="p-6 pt-0 space-y-4">
            @if(settingCashForm){
            <form [formGroup]="settingCashForm" (submit)="onSubmitForm()">


                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"><PERSON><PERSON><PERSON>
                        Chỉ <PERSON>
                        <span class="text-sm text-gray-600 font-thin">({{fm.paymentAddress.value.length}})</span>
                    </label>
                    <textarea formControlName="paymentAddress"
                        class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm  placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="Nhập địa chỉ thanh toán" rows="3"></textarea>
                    @if(fm.paymentAddress.errors && (fm.paymentAddress.dirty || fm.paymentAddress.touched ||
                    formSubmitted)){
                    @if(fm.paymentAddress.errors.required){
                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                        </svg>
                        <span class="sr-only">Info</span>
                        <div>
                            <span class="font-medium">Required!</span> Please enter address.
                        </div>
                    </div>
                    }@else if(fm.paymentAddress.errors.maxlength) {
                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                        </svg>
                        <span class="sr-only">Info</span>
                        <div>
                            <span class="font-medium">Max length!</span> Maximum length is 512 characters.
                        </div>
                    </div>
                    }
                    }
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Giờ
                        Làm Việc
                        <span class="text-sm text-gray-600 font-thin">({{fm.workingHours.value.length}})</span>
                    </label>
                    <input formControlName="workingHours"
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="Ví dụ: 8:00 - 17:30 (Thứ 2 - Thứ 6)">
                    @if(fm.workingHours.errors && (fm.workingHours.dirty || fm.workingHours.touched ||
                    formSubmitted)){
                    @if(fm.workingHours.errors.required){
                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                        </svg>
                        <span class="sr-only">Info</span>
                        <div>
                            <span class="font-medium">Required!</span> Please enter working hours.
                        </div>
                    </div>
                    }@else if(fm.workingHours.errors.maxlength) {
                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                        </svg>
                        <span class="sr-only">Info</span>
                        <div>
                            <span class="font-medium">Max length!</span> Maximum length is 512 characters.
                        </div>
                    </div>
                    }
                    }
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Thời
                        Hạn Thanh Toán
                        <span class="text-sm text-gray-600 font-thin">({{fm.paymentDeadline.value.length}})</span>
                    </label>
                    <input formControlName="paymentDeadline"
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="Ví dụ: 24 giờ sau khi đặt vé">
                    @if(fm.paymentDeadline.errors && (fm.paymentDeadline.dirty || fm.paymentDeadline.touched ||
                    formSubmitted)){
                    @if(fm.paymentDeadline.errors.required){
                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                        </svg>
                        <span class="sr-only">Info</span>
                        <div>
                            <span class="font-medium">Required!</span> Please enter payment deadline.
                        </div>
                    </div>
                    }@else if(fm.paymentDeadline.errors.maxlength) {
                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                        </svg>
                        <span class="sr-only">Info</span>
                        <div>
                            <span class="font-medium">Max length!</span> Maximum length is 512 characters.
                        </div>
                    </div>
                    }
                    }
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Ghi
                        chú
                        <span class="text-sm text-gray-600 font-thin">({{fm.note.value.length}})</span>
                    </label>
                    <textarea formControlName="note"
                        class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm  placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="Ví dụ: CMND/CCCD và mã đặt chỗ" rows="2"></textarea>
                    @if(fm.note.errors && (fm.note.dirty || fm.note.touched ||
                    formSubmitted)){
                    @if(fm.note.errors.maxlength) {
                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                        </svg>
                        <span class="sr-only">Info</span>
                        <div>
                            <span class="font-medium">Max length!</span> Maximum length is 1024 characters.
                        </div>
                    </div>
                    }
                    }
                </div>
                <div class="mt-6 ">
                    <button type="submit"
                        class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-[#fb6340] via-[#fb6340] to-[#fbb140] text-white hover:opacity-90 h-11 rounded-md px-8 w-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-save mr-2 h-5 w-5">
                            <path
                                d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z">
                            </path>
                            <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"></path>
                            <path d="M7 3v4a1 1 0 0 0 1 1h7"></path>
                        </svg> Lưu Thông Tin Thanh Toán
                    </button>
                </div>
            </form>
            }
        </div>
    </div>

</div>