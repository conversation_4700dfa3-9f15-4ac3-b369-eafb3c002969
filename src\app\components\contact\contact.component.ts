import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FeatureService } from '../../services/feature/feature.service';
import { BANK_LOGOS } from '../../share/data/bank-logos';

export interface BankAccountInfo {
  logo: string;
  accountHolder: string;   // Chủ T<PERSON>ản
  bankName: string | null;        // Tên Ngân Hàng
  branch: string;          // Chi Nhánh
  accountNumber: string;   // Số T<PERSON>
}

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.css'
})
export class ContactComponent implements OnInit {
  public bankLogos = BANK_LOGOS;
  public banksInfoModel: {
    banksInfo: BankAccountInfo[];
    note: string;
  } = {
      banksInfo: [
        {
          logo: '/assets/img/banks/logo-vietcombank.jpg',
          accountHolder: 'THAI MINH DUC',
          bankName: 'VIETCOMBANK',
          branch: 'PG<PERSON> <PERSON><PERSON>',
          accountNumber: '101.607.2201'
        },
        {
          logo: '/assets/img/banks/logo-techcombank.jpg',
          accountHolder: 'THAI MINH DUC',
          bankName: 'TECHCOMBANK',
          branch: 'SO 29 NGUYEN TAT THANH, P. TAN LOI, TP. BUON MA THUOT, TINH DAKLAK',
          accountNumber: '120.560.6368'
        },
      ],
      note: 'Vui lòng ghi rõ nội dung chuyển khoản để chúng tôi có thể xác nhận giao dịch nhanh chóng.'
    };

  constructor(
    private readonly featureService: FeatureService
  ) { }

  ngOnInit() {
    // Comment out the loadBankInfo() call since we're using static data
    // this.loadBankInfo();
  }

  loadBankInfo() {
    this.featureService.getPartnerPaymentInfo('credit').subscribe(
      (res: any) => {
        if (res.isSuccessed) {
          this.banksInfoModel = JSON.parse(res.resultObj);
        }
      }
    );
  }

  getBankLogo(bankName: string | null): string {
    if (!bankName) return '';
    const bank = this.bankLogos.find(b => b.name === bankName);
    return bank ? bank.logoPath : '';
  }
}
