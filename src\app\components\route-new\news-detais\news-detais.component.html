<div class="min-h-screen bg-white">

    <section class="relative  pt-32 pb-16 overflow-hidden">
        <div class="absolute inset-0 z-0">
            @if (isLoading) {
            <div class="w-full h-full bg-gray-200 animate-pulse"></div>
            } @else {
            <img [alt]="newsDetail?.name" decoding="async" data-nimg="fill" class="object-cover "
                [src]="newsDetail?.pathImage"
                style="position: absolute; height: 100%; width: 100%; inset: 0px; object-position: 50% 80%;">
            <div class="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/30"></div>
            }
        </div>
        <div class="container mx-auto px-4 relative z-10">
            <a (click)="goBack()"
                class="inline-flex items-center cursor-pointer text-white/90 hover:text-white mb-6 transition-colors"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-arrow-left h-4 w-4 mr-2">
                    <path d="m12 19-7-7 7-7"></path>
                    <path d="M19 12H5"></path>
                </svg>Quay lại tin tức
            </a>
            <div class="max-w-4xl mx-auto">
                @if (isLoading) {
                <div class="space-y-4">
                    <div class="h-6 w-24 bg-gray-200 rounded-full animate-pulse"></div>
                    <div class="h-12 w-3/4 bg-gray-200 rounded-lg animate-pulse"></div>
                    <div class="h-8 w-1/2 bg-gray-200 rounded-lg animate-pulse"></div>
                    <div class="flex items-center space-x-4">
                        <div class="h-6 w-32 bg-gray-200 rounded-full animate-pulse"></div>
                        <div class="h-6 w-32 bg-gray-200 rounded-full animate-pulse"></div>
                        <div class="h-6 w-32 bg-gray-200 rounded-full animate-pulse"></div>
                    </div>
                    <div class="flex items-center mt-4">
                        <div class="h-12 w-12 bg-gray-200 rounded-full animate-pulse mr-4"></div>
                        <div class="space-y-2">
                            <div class="h-4 w-24 bg-gray-200 rounded-full animate-pulse"></div>
                            <div class="h-3 w-16 bg-gray-200 rounded-full animate-pulse"></div>
                        </div>
                    </div>
                </div>
                } @else {
                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 mb-4 bg-primary-500 text-white border-none"
                    data-v0-t="badge">
                    {{newsDetail?.categoryName}}
                </div>
                <h1 class="text-3xl md:text-5xl font-bold mb-6 text-white drop-shadow-md">
                    {{newsDetail?.name}}</h1>
                <div class="flex items-center text-white/90 mb-8 flex-wrap">
                    <div class="flex items-center mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-calendar h-4 w-4 mr-2">
                            <path d="M8 2v4"></path>
                            <path d="M16 2v4"></path>
                            <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                            <path d="M3 10h18"></path>
                        </svg>
                        <span>
                            {{newsDetail?.timeCreate}}</span>
                    </div>
                    <div class="flex items-center mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-clock h-4 w-4 mr-2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        <span>5 phút đọc</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-eye h-4 w-4 mr-2">
                            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        <span>
                            {{newsDetail?.viewCount | number:'1.0-0'}} lượt xem</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="relative h-12 w-12 rounded-full overflow-hidden mr-4">
                        <img [alt]="newsDetail?.author" loading="lazy" decoding="async" data-nimg="fill"
                            class="object-cover bg-white/90" src="\assets\img\favicon\LogoNgocMai.png"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                    </div>
                    <div>
                        <p class="font-medium text-white">{{newsDetail?.author}}</p>
                        <p class="text-sm text-white/80">Tác giả</p>
                    </div>
                </div>
                }
            </div>
        </div>
    </section>
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
                <div class="lg:col-span-8 flex flex-col">
                    @if (isLoading) {
                    <div class="space-y-6">
                        <div class="flex justify-between items-center mb-8 pb-8 border-b border-gray-200">
                            <div class="flex space-x-2">
                                <div class="h-9 w-24 bg-gray-200 rounded-full animate-pulse"></div>
                                <div class="h-9 w-24 bg-gray-200 rounded-full animate-pulse"></div>
                            </div>
                            <div class="h-9 w-24 bg-gray-200 rounded-full animate-pulse"></div>
                        </div>
                        <div class="space-y-4">
                            <div class="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                            <div class="h-4 w-5/6 bg-gray-200 rounded animate-pulse"></div>
                            <div class="h-4 w-4/6 bg-gray-200 rounded animate-pulse"></div>
                            <div class="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                            <div class="h-4 w-5/6 bg-gray-200 rounded animate-pulse"></div>
                        </div>
                    </div>
                    } @else {
                    <div class="flex justify-between items-center mb-8 pb-8 border-b border-gray-200">
                        <div class="flex space-x-2">
                            <button (click)="shareNews()"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3 rounded-full">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-share2 h-4 w-4 mr-2">
                                    <circle cx="18" cy="5" r="3"></circle>
                                    <circle cx="6" cy="12" r="3"></circle>
                                    <circle cx="18" cy="19" r="3"></circle>
                                    <line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line>
                                    <line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line>
                                </svg>Chia sẻ
                            </button>
                            <button (click)="saveNews()" [class.bg-primary-100]="isSaved"
                                [class.text-primary-600]="isSaved" [class.border-primary-200]="isSaved"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3 rounded-full">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-bookmark h-4 w-4 mr-2"
                                    [class.fill-primary-600]="isSaved">
                                    <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"></path>
                                </svg>{{isSaved ? 'Đã lưu' : 'Lưu'}}
                            </button>
                        </div>
                        <button (click)="likeNews()" [class.bg-primary-100]="isLiked" [class.text-primary-600]="isLiked"
                            [class.border-primary-200]="isLiked"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-thumbs-up h-4 w-4 mr-2"
                                [class.fill-primary-600]="isLiked">
                                <path d="M7 10v12"></path>
                                <path
                                    d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z">
                                </path>
                            </svg>{{isLiked ? 'Đã thích' : 'Thích'}}
                        </button>
                    </div>
                    <div class="prose prose-lg max-w-none min-h-[50vh] mb-12">
                        <app-editor-viewer [content]="editorContent"></app-editor-viewer>
                    </div>
                    <div class="mb-12">
                        <h3 class="text-lg font-semibold mb-4">Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            <div routerLink="/news/{{newsDetail?.seoCategory}}"
                                class="inline-flex items-center border px-2.5 py-0.5 cursor-pointer text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200 rounded-full">
                                {{newsDetail?.categoryName}}</div>

                        </div>
                    </div>
                    }
                </div>
                <div class="lg:col-span-4">
                    @if (isLoading) {
                    <div class="space-y-8">
                        <div class="space-y-4">
                            <div class="h-6 w-48 bg-gray-200 rounded animate-pulse"></div>
                            @for (item of [1,2,3]; track $index) {
                            <div class="flex items-start space-x-4">
                                <div class="h-24 w-24 bg-gray-200 rounded animate-pulse"></div>
                                <div class="flex-1 space-y-2">
                                    <div class="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                                    <div class="h-4 w-2/3 bg-gray-200 rounded animate-pulse"></div>
                                    <div class="h-3 w-1/3 bg-gray-200 rounded animate-pulse"></div>
                                </div>
                            </div>
                            }
                        </div>
                        <div class="space-y-4">
                            <div class="h-6 w-48 bg-gray-200 rounded animate-pulse"></div>
                            @for (item of [1,2,3]; track $index) {
                            <div class="flex items-start space-x-4">
                                <div class="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
                                <div class="flex-1 space-y-2">
                                    <div class="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                                    <div class="h-3 w-1/3 bg-gray-200 rounded animate-pulse"></div>
                                </div>
                            </div>
                            }
                        </div>
                        <div class="space-y-4">
                            <div class="h-6 w-48 bg-gray-200 rounded animate-pulse"></div>
                            @for (item of [1,2,3,4,5]; track $index) {
                            <div class="flex items-center justify-between p-3">
                                <div class="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                                <div class="h-6 w-12 bg-gray-200 rounded-full animate-pulse"></div>
                            </div>
                            }
                        </div>
                    </div>
                    } @else {
                    <div class="mb-8">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold">Bài viết liên quan</h3>
                            <a routerLink="/news"
                                class="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center">Xem
                                thêm<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 ml-1">
                                    <path d="m9 18 6-6-6-6"></path>
                                </svg>
                            </a>
                        </div>
                        <div class="space-y-6">
                            @for (item of relatedNews; track $index) {
                            <a [routerLink]="['/news', item.seoCategory, item.seoTitle]" class="group block">
                                <div
                                    class="rounded-lg bg-card text-card-foreground shadow-sm overflow-hidden hover:shadow-md transition-shadow border border-gray-100">
                                    <div class="flex">
                                        <div class="relative h-24 w-24 flex-shrink-0">
                                            <img [alt]="item.name" loading="lazy" decoding="async" data-nimg="fill"
                                                class="object-cover" [src]="item.pathImage"
                                                style="position: absolute; height: 100%; width: 100%; inset: 0px;">
                                        </div>
                                        <div class="p-4">
                                            <h4
                                                class="font-medium text-sm line-clamp-2 group-hover:text-primary-600 transition-colors">
                                                {{item.name}}</h4>
                                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-calendar h-3 w-3 mr-1">
                                                    <path d="M8 2v4"></path>
                                                    <path d="M16 2v4"></path>
                                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                                    <path d="M3 10h18"></path>
                                                </svg>
                                                <span class="mr-3">{{item.timeCreate}}</span>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-eye h-3 w-3 mr-1">
                                                    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                                    <circle cx="12" cy="12" r="3"></circle>
                                                </svg>
                                                <span>{{item?.viewCount | number:'1.0-0'}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                            }

                        </div>
                    </div>
                    <div class="mb-8">
                        <h3 class="text-xl font-bold mb-4">Bài viết phổ biến</h3>
                        <div class="space-y-4">
                            @for (item of popularNews; track $index) {
                            <a [routerLink]="['/news', item.seoCategory, item.seoTitle]" class="group">
                                <div class="flex items-start">
                                    <div
                                        class="flex-shrink-0 w-8 h-8 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center mr-3 font-bold">
                                        {{$index + 1}}</div>
                                    <div>
                                        <h4
                                            class="font-medium text-sm line-clamp-2 group-hover:text-primary-600 transition-colors">
                                            {{item.name}}</h4>
                                        <div class="flex items-center mt-1 text-xs text-gray-500">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-eye h-3 w-3 mr-1">
                                                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                                <circle cx="12" cy="12" r="3"></circle>
                                            </svg>
                                            <span>{{item.viewCount | number:'1.0-0'}} lượt xem</span>
                                        </div>
                                    </div>
                                </div>
                                <div data-orientation="horizontal" role="none"
                                    class="shrink-0 bg-gray-200 h-[1px] w-full my-3"></div>
                            </a>
                            }



                        </div>
                    </div>
                    <div class="mb-8">
                        <h3 class="text-xl font-bold mb-4">Danh mục</h3>
                        <div class="space-y-2">
                            @for (category of categories; track $index) {
                            <a [routerLink]="['/news', category.key]"
                                class="flex items-center justify-between p-3 rounded-lg hover:bg-primary-50 transition-colors">
                                <span class="font-medium">{{category.value}}</span>
                                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground bg-primary-50"
                                    data-v0-t="badge">{{category.countNews}}
                                </div>
                            </a>
                            }

                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl p-6 text-white">
                        <h3 class="text-xl font-bold mb-3">Đăng ký nhận tin</h3>
                        <p class="text-white/90 text-sm mb-4">Nhận thông báo về các bài viết mới và ưu đãi du lịch hấp
                            dẫn</p>
                        <form [formGroup]="emailForm" (ngSubmit)="subscribeNewsletter()" class="space-y-3">
                            <div>
                                <input formControlName="email" placeholder="Email của bạn"
                                    class="w-full px-4 py-2 rounded-lg text-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-white"
                                    type="email">
                                @if (emailError) {
                                <p class="text-red-200 text-xs mt-1">{{ emailError }}</p>
                                }
                            </div>
                            <button type="submit" [disabled]="isSubmitting"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 w-full bg-white text-primary-600 hover:bg-primary-50 disabled:bg-white/50">
                                @if (isSubmitting) {
                                <svg class="animate-spin h-5 w-5 text-primary-600" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                <span>Đang xử lý...</span>
                                } @else {
                                <span>Đăng ký ngay</span>
                                }
                            </button>
                        </form>
                    </div>
                    }
                </div>
            </div>
            <div class="mt-16">
                <h2 class="text-2xl font-bold mb-8">Có thể bạn cũng thích</h2>
                @if (isLoading) {
                <div class="mt-16 space-y-8">
                    <div class="h-8 w-64 bg-gray-200 rounded animate-pulse"></div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        @for (item of [1,2,3]; track $index) {
                        <div class="space-y-4">
                            <div class="h-48 w-full bg-gray-200 rounded animate-pulse"></div>
                            <div class="h-6 w-full bg-gray-200 rounded animate-pulse"></div>
                            <div class="h-4 w-2/3 bg-gray-200 rounded animate-pulse"></div>
                        </div>
                        }
                    </div>
                </div>
                } @else {
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <a *ngFor="let news of recommendedNews" [routerLink]="['/news', news.seoCategory, news.seoTitle]"
                        class="group">
                        <div class="rounded-2xl overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                            <div class="relative h-48 w-full">
                                <img [alt]="news.name" loading="lazy" decoding="async" data-nimg="fill"
                                    class="object-cover transition-transform duration-500 group-hover:scale-105"
                                    [src]="news.pathImage"
                                    style="position: absolute; height: 100%; width: 100%; inset: 0px;">
                            </div>
                            <div class="p-4">
                                <h3
                                    class="h-14 font-semibold group-hover:text-primary-600 transition-colors line-clamp-2">
                                    {{news.name}}
                                </h3>

                                <div class="flex items-center mt-2 text-sm text-gray-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-calendar h-4 w-4 mr-1 text-primary-500">
                                        <path d="M8 2v4"></path>
                                        <path d="M16 2v4"></path>
                                        <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                        <path d="M3 10h18"></path>
                                    </svg>
                                    <span class="mr-4">{{news.timeCreate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-eye h-4 w-4 mr-1 text-primary-500">
                                        <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                    <span>{{news.viewCount}}</span>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                }
            </div>
        </div>
    </section>

</div>