import { Injectable } from '@angular/core';
// @ts-ignore

@Injectable({
  providedIn: 'root'
})
export class LunarService {
  private PI: number = 3.14;
  private mDay: number;
  private mMonth: number;
  private mYear: number;
  private mTimeZone: number = 7;

  public mLunarDay: number = 0;
  public mLunarYear: number = 0;
  public mLunarMonth: number = 0;

  constructor() {
    this.mDay = 1;
    this.mMonth = 1;
    this.mYear = 2000;
    this.mTimeZone = 7;
  }

  public setDate(day: number, month: number, year: number, timeZone: number) {
    this.mDay = day;
    this.mMonth = month;
    this.mYear = year;
    this.mTimeZone = timeZone;
  }

  private convertToJuliusDay(): number {
    const a = Math.floor((14 - this.mMonth) / 12);
    const y = this.mYear + 4800 - a;
    const m = this.mMonth + 12 * a - 3;
    const jd = this.mDay + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) - 32045;
    return jd;
  }

  private getNewMoonDay(k: number, timeZone: number): number {
    const T = k / 1236.85;
    const T2 = T * T;
    const T3 = T2 * T;
    const dr = this.PI / 180;
    let Jd1 = 2415020.75933 + 29.53058868 * k + 0.0001178 * T2 - 0.000000155 * T3;
    Jd1 += 0.00033 * Math.sin((166.56 + 132.87 * T - 0.009173 * T2) * dr);

    const M = 359.2242 + 29.10535608 * k - 0.0000333 * T2 - 0.00000347 * T3;
    const Mpr = 306.0253 + 385.81691806 * k + 0.0107306 * T2 + 0.00001236 * T3;
    const F = 21.2964 + 390.67050646 * k - 0.0016528 * T2 - 0.00000239 * T3;

    let C1 = (0.1734 - 0.000393 * T) * Math.sin(M * dr) + 0.0021 * Math.sin(2 * dr * M);
    C1 -= 0.4068 * Math.sin(Mpr * dr) + 0.0161 * Math.sin(dr * 2 * Mpr);
    C1 -= 0.0004 * Math.sin(dr * 3 * Mpr);
    C1 += 0.0104 * Math.sin(dr * 2 * F) - 0.0051 * Math.sin(dr * (M + Mpr));
    C1 -= 0.0074 * Math.sin(dr * (M - Mpr)) + 0.0004 * Math.sin(dr * (2 * F + M));
    C1 -= 0.0004 * Math.sin(dr * (2 * F - M)) - 0.0006 * Math.sin(dr * (2 * F + Mpr));
    C1 += 0.0010 * Math.sin(dr * (2 * F - Mpr)) + 0.0005 * Math.sin(dr * (2 * Mpr + M));

    let deltat: number;
    if (T < -11) {
      deltat = 0.001 + 0.000839 * T + 0.0002261 * T2 - 0.00000845 * T3 - 0.000000081 * T * T3;
    } else {
      deltat = -0.000278 + 0.000265 * T + 0.000262 * T2;
    }

    const JdNew = Jd1 + C1 - deltat;
    return Math.floor(JdNew + 0.5 + timeZone / 24);
  }

  private jdFromDate(dd: number, mm: number, yy: number): number {
    const a = Math.floor((14 - mm) / 12);
    const y = yy + 4800 - a;
    const m = mm + 12 * a - 3;
    let jd = dd + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) - 32045;

    if (jd < 2299161) {
      jd = dd + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - 32083;
    }

    return jd;
  }

  private getSunLongitude(jdn: number, timeZone: number): number {
    const T = (jdn - 2451545.5 - timeZone / 24) / 36525;
    const T2 = T * T;
    const dr = this.PI / 180;
    const M = 357.52910 + 35999.05030 * T - 0.0001559 * T2 - 0.00000048 * T * T2;
    const L0 = 280.46645 + 36000.76983 * T + 0.0003032 * T2;
    let DL = (1.914600 - 0.004817 * T - 0.000014 * T2) * Math.sin(dr * M);
    DL += (0.019993 - 0.000101 * T) * Math.sin(dr * 2 * M) + 0.000290 * Math.sin(dr * 3 * M);
    let L = L0 + DL;
    L = L * dr;
    L = L - this.PI * 2 * Math.floor(L / (this.PI * 2));
    return Math.floor(L / this.PI * 6);
  }

  private getLunarMonth11(yy: number, timeZone: number): number {
    const off = this.jdFromDate(31, 12, yy) - 2415021;
    const k = Math.floor(off / 29.530588853);
    let nm = this.getNewMoonDay(k, timeZone);
    const sunLong = this.getSunLongitude(nm, timeZone);

    if (sunLong >= 9) {
      nm = this.getNewMoonDay(k - 1, timeZone);
    }

    return nm;
  }

  private getLeapMonthOffset(a11: number, timeZone: number): number {
    const k = Math.floor((a11 - 2415021.076998695) / 29.530588853 + 0.5);
    let last = 0;
    let i = 1;
    let arc = this.getSunLongitude(this.getNewMoonDay(k + i, timeZone), timeZone);

    do {
      last = arc;
      i++;
      arc = this.getSunLongitude(this.getNewMoonDay(k + i, timeZone), timeZone);
    } while (arc !== last && i < 14);

    return i - 1;
  }

  public convertToLunar(): Date {
    const dayNumber = this.jdFromDate(this.mDay, this.mMonth, this.mYear);
    let k = Math.floor((dayNumber - 2415021.076998695) / 29.530588853);
    let monthStart = this.getNewMoonDay(k + 1, this.mTimeZone);

    if (monthStart > dayNumber) {
      monthStart = this.getNewMoonDay(k, this.mTimeZone);
    }

    let a11 = this.getLunarMonth11(this.mYear, this.mTimeZone);
    let b11 = a11;

    if (a11 >= monthStart) {
      this.mLunarYear = this.mYear;
      a11 = this.getLunarMonth11(this.mYear - 1, this.mTimeZone);
    } else {
      this.mLunarYear = this.mYear + 1;
      b11 = this.getLunarMonth11(this.mYear + 1, this.mTimeZone);
    }

    this.mLunarDay = dayNumber - monthStart + 1;
    let diff = Math.floor((monthStart - a11) / 29);
    let lunarLeap = 0;
    this.mLunarMonth = diff + 11;

    if (b11 - a11 > 365) {
      const leapMonthDiff = this.getLeapMonthOffset(a11, this.mTimeZone);

      if (diff >= leapMonthDiff) {
        this.mLunarMonth = diff + 10;

        if (diff === leapMonthDiff) {
          lunarLeap = 1;
        }
      }
    }

    if (this.mLunarMonth > 12) {
      this.mLunarMonth -= 12;
    }

    if (this.mLunarMonth >= 11 && diff < 4) {
      this.mLunarYear -= 1;
    }

    return new Date(this.mLunarYear, this.mLunarMonth - 1, this.mLunarDay);
  }

  public static convertToLunar(year: number, month: number, day: number, timeZone: number = 7): Date {
    const lunarService = new LunarService();
    lunarService.setDate(day, month, year, timeZone);
    return lunarService.convertToLunar();
  }

  public getLunarDate(): string {
    const can = ["Giap", "At", "Binh", "Dinh", "Mau", "Ki", "Canh", "Tan", "Nham", "Qui"];
    const chi = ["Ti", "Suu", "Dan", "Mao", "Thinh", "Ti", "Ngo", "Mui", "Than", "Dau", "Tuat", "Hoi"];
    const juliusDay = this.convertToJuliusDay();
    return `${can[(juliusDay + 9) % 10]} ${chi[(juliusDay + 1) % 12]}`;
  }

  public getLunarMonthName(): string {
    const can = ["Giap", "At", "Binh", "Dinh", "Mau", "Ki", "Canh", "Tan", "Nham", "Qui"];
    const chi = ["Dan", "Mao", "Thinh", "Ti", "Ngo", "Mui", "Than", "Dau", "Tuat", "Hoi", "Ti", "Suu"];
    const mod = (this.mLunarYear * 12 + this.mLunarMonth + 3) % 10;
    return `${can[mod]} ${chi[this.mLunarMonth - 1]}`;
  }

  public getLunarYearName(): string {
    const can = ["Giap", "At", "Binh", "Dinh", "Mau", "Ki", "Canh", "Tan", "Nham", "Qui"];
    const chi = ["Ty", "Suu", "Dan", "Mao", "Thinh", "Ti", "Ngo", "Mui", "Than", "Dau", "Tuat", "Hoi"];
    return `${can[(this.mYear + 6) % 10]} ${chi[(this.mYear + 8) % 12]}`;
  }
}

// export class LunarCalendar {
//   // Hàm chuyển đổi ngày Dương sang Âm lịch
//   public static getLunarDate(solarDate: any): string {
//     const year = solarDate.year;
//     const month = solarDate.month; // Tháng trong JavaScript bắt đầu từ 0
//     const day = solarDate.day;

//     const lunarDate = lunarCalendar.solarToLunar(year, month, day);

//     if (lunarDate) {
//       return lunarDate.lunarDay === 1 ? `${lunarDate.lunarDay}/${lunarDate.lunarMonth}` : `${lunarDate.lunarDay}`;
//     } else {
//       throw new Error('Không thể chuyển đổi ngày dương lịch sang âm lịch.');
//     }
//   }
// }

