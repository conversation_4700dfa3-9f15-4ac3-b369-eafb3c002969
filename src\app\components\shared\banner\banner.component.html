<section class=" py-24 relative min-h-screen w-full  backdrop-brightness-75 backdrop-saturate-50 bg-cover bg-center" style="background: linear-gradient(rgb(0 0 0 / 41%), rgb(0 0 0 / 55%)),  url('/assets/img/background/hero-bg{{randomImg}}.jpg');
background-size: cover;
background-position: center;
background-attachment: fixed;">
    <div
        class="from-primary-500 to-primary-400 absolute top-20 left-20 w-28 h-32 bg-gradient-to-br  opacity-20 rounded-full blur-2xl animate-float">
    </div>
    <div class="from-primary-500 to-primary-400 absolute bottom-32 right-32 w-48 h-48 bg-gradient-to-br  opacity-15 rounded-full blur-3xl animate-float"
        style="animation-delay: 2s;">
    </div>
    <div class="from-primary-500 to-primary-400 absolute top-1/2 right-20 w-24 h-24 bg-gradient-to-br  opacity-25 rounded-full blur-xl animate-float"
        style="animation-delay: 1s;">
    </div>
    <div
        class=" z-20 grid lg:grid-cols-2 md:gap-12 gap-6 items-center min-h-[80vh] max-w-7xl mx-auto max-md:px-2 pb-24">
        <div class="text-white space-y-8 max-md:px-4 ">
            <div class="space-y-4">
                <h2 class="text-3xl lg:text-5xl font-bold  md:text-left text-center">Easy contact, easy fly, 
                    <span class="block text-transparent bg-clip-text bg-gradient-to-r from-primary-400 to-primary-400">
                        Easy conquer the world</span>
                </h2>
                <div
                    class="md:text-xl text-lg text-gray-200 leading-relaxed max-w-lg md:text-justify text-center min-w-full">
                    🌸 Hành trình của bạn bắt đầu từ đây! Cùng
                    chúng tôi bay đến những vùng đất mơ ước, nơi mỗi khoảnh khắc đều trở thành kỷ niệm đáng nhớ.
                </div>
            </div>
            <div class="grid grid-cols-3 gap-6 md:pt-8 pt-2">
                <div class="text-center">
                    <div class="text-3xl font-bold text-primary-400">500K+</div>
                    <div class="text-sm text-gray-300">Khách hàng tin tưởng</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-primary-400">200+</div>
                    <div class="text-sm text-gray-300">Điểm đến hấp dẫn</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-primary-400">24/24</div>
                    <div class="text-sm text-gray-300">Hỗ trợ tận tình</div>
                </div>
            </div>
        </div>
        <div class="relative md:justify-self-end justify-self-center max-md:w-full ">
            <div
                class="from-primary-500 to-primary-400 absolute inset-0 bg-gradient-to-r  rounded-[40px]  blur-sm group-hover:blur-md transition-all duration-500 animate-pulse">
            </div>
            <div
                class="from-primary-500 to-primary-400  rounded-[40px] bg-gradient-to-r relative  p-1 transform group-hover:scale-105 transition-transform duration-500">
                <div class="bg-white  rounded-[40px]">
                    <app-flight-search></app-flight-search>
                </div>
            </div>
        </div>
    </div>

    <div class="absolute -z-10 -bottom-1 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" class="w-full h-auto">
            <path fill="#ffffff" fill-opacity="1"
                d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z">
            </path>
        </svg>
    </div>
</section>