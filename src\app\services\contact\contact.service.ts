import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

const apiUrl = environment.apiUrl;

@Injectable({
  providedIn: 'root'
})
export class ContactService {

  constructor(
    private http: HttpClient
  ) { }

  sentContact(data: any): any {
    return this.http.post(`${apiUrl}/api/contact/email`, data);
  }
  postContact(data: any): Observable<any> {
    return this.http.post(`${apiUrl}/api/contact`, data);
  }
}
