import { Component, OnInit, inject, ElementRef, ViewChild, AfterViewInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { RouterLink } from '@angular/router';
import { NewsService } from '../../../services/news/news.service';
import { CategoryNewsService } from '../../../services/category-news/category-news.service';

interface News {
  id: string;
  index: number;
  name: string;
  seoTitle: string;
  categoryName: string;
  seoCategory: string;
  pathImage: string;
  description: string;
  timeCreate: string;
  author: string;
  viewCount: number;
  status: boolean;
}

interface Category {
  key: string;
  value: string;
  countNews: string;
}

@Component({
  selector: 'app-news',
  imports: [
    CommonModule,
    RouterLink
  ],
  templateUrl: './news.component.html',
  styleUrl: './news.component.css'
})
export class NewsComponent implements OnInit, AfterViewInit {
  private readonly newsService = inject(NewsService);
  private readonly categoryNewsService = inject(CategoryNewsService);
  @ViewChild('newsSection') newsSection!: ElementRef;

  _isLoading: boolean = true;
  _isVisible: boolean = false;
  featuredNews: News | null = null;
  secondaryNews: News[] = [];
  regularNews: News[] = [];
  categories: Category[] = [];

  constructor(@Inject(PLATFORM_ID) private platformId: Object) { }
  ngOnInit(): void {
    // Initialize with loading state
    this._isLoading = true;
  }

  ngAfterViewInit(): void {
    this.setupIntersectionObserver();
  }

  private setupIntersectionObserver(): void {
    if (isPlatformBrowser(this.platformId)) {
      const options = {
        root: null, // viewport
        rootMargin: '100px', // start loading when within 100px of viewport
        threshold: 0.1 // trigger when at least 10% of the element is visible
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this._isVisible) {
            this._isVisible = true;
            this.loadCategories();
            this.loadNews();
            observer.unobserve(entry.target); // Stop observing once loaded
          }
        });
      }, options);

      if (this.newsSection) {
        observer.observe(this.newsSection.nativeElement);
      }
    }
  }

  async loadCategories(): Promise<void> {
    try {
      const res = await this.categoryNewsService.getPartnerClientItems().toPromise();
      if (res.isSuccessed) {
        this.categories = res.resultObj;
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }

  async loadNews(): Promise<void> {
    try {
      this._isLoading = true;
      const searchModel = {
        PageIndex: 1,
        PageSize: 6,
        SortColumn: 'timeCreate',
        SortOrder: 'desc',
        Type: '',
        Keyword: '',
        FilterStatus: null
      };

      const res = await this.newsService.getPartnerClientNewsPaging(searchModel).toPromise();
      if (res.isSuccessed) {
        const news = res.resultObj.items;
        if (news.length > 0) {
          // First news as featured
          this.featuredNews = news[0];
          // Next 2 news as secondary
          this.secondaryNews = news.slice(1, 3);
          // Remaining 3 news as regular
          this.regularNews = news.slice(3, 6);
        }
      }
    } catch (error) {
      console.error('Error loading news:', error);
    } finally {
      this._isLoading = false;
    }
  }
}
