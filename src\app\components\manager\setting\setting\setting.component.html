<div class=" relative h-full flex flex-row max-md:flex-col gap-2 min-w-full">
    <div
        class="stepper h-fit pt-2 pb-8 pl-8 pr-4 w-fit max-md:w-full max-md:pb-4 max-md:overflow-x-auto max-md:overflow-y-hidden shadow-lg bg-white dark:bg-gray-800 rounded-lg">
        <div>
            <div (click)="goBack()"
                class="max-md:fixed flex w-fit hover:gap-2 mb-4 items-center cursor-pointer text-gray-500 hover:text-primary-600">
                <svg class="rotate-180 -ml-7 w-8 h-8 hover:text-primary-600 dark:text-white dark:hover:text-primary-600"
                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                    viewBox="0 0 24 24">
                    <path fill-rule="evenodd"
                        d="M3 4a1 1 0 0 0-.822 1.57L6.632 12l-4.454 6.43A1 1 0 0 0 3 20h13.153a1 1 0 0 0 .822-.43l4.847-7a1 1 0 0 0 0-1.14l-4.847-7a1 1 0 0 0-.822-.43H3Z"
                        clip-rule="evenodd" />
                </svg>
                <span class="font-light">Back</span>
            </div>
        </div>
        <ul
            class="max-md:pt-8 max-md:flex-row h-full flex flex-col -mb-px text-sm font-medium text-center relative text-gray-500 border-s max-md:border-none border-gray-200 dark:border-gray-700 dark:text-gray-400 w-fit">
            <li role="presentation" class="me-2" routerLink="/manager/setting" [routerLinkActiveOptions]="{exact: true}"
                routerLinkActive="bg-primary-600 text-white rounded-e-lg">
                <button type="button"
                    class="max-md:relative inline-block py-4 px-6  dark:border-transparent   dark:text-gray-400 border-gray-100 hover:border-gray-300 dark:border-gray-700 dark:hover:text-gray-300">
                    <span
                        class="-mt-2 absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                        <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 54 54" xml:space="preserve"
                            class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400">
                            <g>
                                <path
                                    d="M51.22,21h-5.052c-0.812,0-1.481-0.447-1.792-1.197s-0.153-1.54,0.42-2.114l3.572-3.571                           c0.525-0.525,0.814-1.224,0.814-1.966c0-0.743-0.289-1.441-0.814-1.967l-4.553-4.553c-1.05-1.05-2.881-1.052-3.933,0l-3.571,3.571                           c-0.574,0.573-1.366,0.733-2.114,0.421C33.447,9.313,33,8.644,33,7.832V2.78C33,1.247,31.753,0,30.22,0H23.78                           C22.247,0,21,1.247,21,2.78v5.052c0,0.812-0.447,1.481-1.197,1.792c-0.748,0.313-1.54,0.152-2.114-0.421l-3.571-3.571                           c-1.052-1.052-2.883-1.05-3.933,0l-4.553,4.553c-0.525,0.525-0.814,1.224-0.814,1.967c0,0.742,0.289,1.44,0.814,1.966l3.572,3.571                           c0.573,0.574,0.73,1.364,0.42,2.114S8.644,21,7.832,21H2.78C1.247,21,0,22.247,0,23.78v6.439C0,31.753,1.247,33,2.78,33h5.052                           c0.812,0,1.481,0.447,1.792,1.197s0.153,1.54-0.42,2.114l-3.572,3.571c-0.525,0.525-0.814,1.224-0.814,1.966                           c0,0.743,0.289,1.441,0.814,1.967l4.553,4.553c1.051,1.051,2.881,1.053,3.933,0l3.571-3.572c0.574-0.573,1.363-0.731,2.114-0.42                           c0.75,0.311,1.197,0.98,1.197,1.792v5.052c0,1.533,1.247,2.78,2.78,2.78h6.439c1.533,0,2.78-1.247,2.78-2.78v-5.052                           c0-0.812,0.447-1.481,1.197-1.792c0.751-0.312,1.54-0.153,2.114,0.42l3.571,3.572c1.052,1.052,2.883,1.05,3.933,0l4.553-4.553                           c0.525-0.525,0.814-1.224,0.814-1.967c0-0.742-0.289-1.44-0.814-1.966l-3.572-3.571c-0.573-0.574-0.73-1.364-0.42-2.114                           S45.356,33,46.168,33h5.052c1.533,0,2.78-1.247,2.78-2.78V23.78C54,22.247,52.753,21,51.22,21z M52,30.22                           C52,30.65,51.65,31,51.22,31h-5.052c-1.624,0-3.019,0.932-3.64,2.432c-0.622,1.5-0.295,3.146,0.854,4.294l3.572,3.571                           c0.305,0.305,0.305,0.8,0,1.104l-4.553,4.553c-0.304,0.304-0.799,0.306-1.104,0l-3.571-3.572c-1.149-1.149-2.794-1.474-4.294-0.854                           c-1.5,0.621-2.432,2.016-2.432,3.64v5.052C31,51.65,30.65,52,30.22,52H23.78C23.35,52,23,51.65,23,51.22v-5.052                           c0-1.624-0.932-3.019-2.432-3.64c-0.503-0.209-1.021-0.311-1.533-0.311c-1.014,0-1.997,0.4-2.761,1.164l-3.571,3.572                           c-0.306,0.306-0.801,0.304-1.104,0l-4.553-4.553c-0.305-0.305-0.305-0.8,0-1.104l3.572-3.571c1.148-1.148,1.476-2.794,0.854-4.294                           C10.851,31.932,9.456,31,7.832,31H2.78C2.35,31,2,30.65,2,30.22V23.78C2,23.35,2.35,23,2.78,23h5.052                           c1.624,0,3.019-0.932,3.64-2.432c0.622-1.5,0.295-3.146-0.854-4.294l-3.572-3.571c-0.305-0.305-0.305-0.8,0-1.104l4.553-4.553                           c0.304-0.305,0.799-0.305,1.104,0l3.571,3.571c1.147,1.147,2.792,1.476,4.294,0.854C22.068,10.851,23,9.456,23,7.832V2.78                           C23,2.35,23.35,2,23.78,2h6.439C30.65,2,31,2.35,31,2.78v5.052c0,1.624,0.932,3.019,2.432,3.64                           c1.502,0.622,3.146,0.294,4.294-0.854l3.571-3.571c0.306-0.305,0.801-0.305,1.104,0l4.553,4.553c0.305,0.305,0.305,0.8,0,1.104                           l-3.572,3.571c-1.148,1.148-1.476,2.794-0.854,4.294c0.621,1.5,2.016,2.432,3.64,2.432h5.052C51.65,23,52,23.35,52,23.78V30.22z">
                                </path>
                                <path
                                    d="M27,18c-4.963,0-9,4.037-9,9s4.037,9,9,9s9-4.037,9-9S31.963,18,27,18z M27,34c-3.859,0-7-3.141-7-7s3.141-7,7-7s7,3.141,7,7S30.859,34,27,34z">
                                </path>
                            </g>
                        </svg>
                    </span>
                    <h3 class="font-medium leading-tight text-nowrap">Cài đặt</h3>
                </button>
            </li>
            <li role="presentation" class="me-2" routerLink="/manager/setting/email"
                [routerLinkActiveOptions]="{exact: true}" routerLinkActive="bg-primary-600 text-white rounded-e-lg">
                <button type="button"
                    class="max-md:relative inline-block py-4 px-6  dark:border-transparent   dark:text-gray-400 border-gray-100 hover:border-gray-300 dark:border-gray-700 dark:hover:text-gray-300">
                    <span
                        class="-mt-2 absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" width="800px" height="800px" viewBox="0 0 24 24"
                            fill="none" class="w-3.5 h-3.5 fill-gray-500 dark:fill-gray-400">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M3.75 5.25L3 6V18L3.75 18.75H20.25L21 18V6L20.25 5.25H3.75ZM4.5 7.6955V17.25H19.5V7.69525L11.9999 14.5136L4.5 7.6955ZM18.3099 6.75H5.68986L11.9999 12.4864L18.3099 6.75Z"
                                fill="#080341"></path>
                        </svg>
                    </span>
                    <h3 class="font-medium leading-tight text-nowrap">Email</h3>
                </button>
            </li>
            <li role="presentation" class="me-2" routerLink="/manager/setting/api"
                [routerLinkActiveOptions]="{exact: true}" routerLinkActive="bg-primary-600 text-white rounded-e-lg">
                <button type="button"
                    class="max-md:relative inline-block py-4 px-6  dark:border-transparent   dark:text-gray-400 border-gray-100 hover:border-gray-300 dark:border-gray-700 dark:hover:text-gray-300">
                    <span
                        class="-mt-2 absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                        <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                            viewBox="0 0 24 24"
                            class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400 group-active:text-white">
                            <path fill="currentColor"
                                d="M6.94318 11h-.85227l.96023-2.90909h1.07954L9.09091 11h-.85227l-.63637-2.10795h-.02272L6.94318 11Zm-.15909-1.14773h1.60227v.59093H6.78409v-.59093ZM9.37109 11V8.09091h1.25571c.2159 0 .4048.04261.5667.12784.162.08523.2879.20502.3779.35937.0899.15436.1349.33476.1349.5412 0 .20833-.0464.38873-.1392.54119-.0918.15246-.2211.26989-.3878.35229-.1657.0824-.3593.1236-.5809.1236h-.75003v-.61367h.59093c.0928 0 .1719-.0161.2372-.0483.0663-.03314.1169-.08002.152-.14062.036-.06061.054-.13211.054-.21449 0-.08334-.018-.15436-.054-.21307-.0351-.05966-.0857-.10511-.152-.13636-.0653-.0322-.1444-.0483-.2372-.0483h-.2784V11h-.78981Zm3.41481-2.90909V11h-.7898V8.09091h.7898Z">
                            </path>
                            <path stroke="currentColor" stroke-linejoin="round" stroke-width="2"
                                d="M8.31818 2c-.55228 0-1 .44772-1 1v.72878c-.06079.0236-.12113.04809-.18098.07346l-.55228-.53789c-.38828-.37817-1.00715-.37817-1.39543 0L3.30923 5.09564c-.19327.18824-.30229.44659-.30229.71638 0 .26979.10902.52813.30229.71637l.52844.51468c-.01982.04526-.03911.0908-.05785.13662H3c-.55228 0-1 .44771-1 1v2.58981c0 .5523.44772 1 1 1h.77982c.01873.0458.03802.0914.05783.1366l-.52847.5147c-.19327.1883-.30228.4466-.30228.7164 0 .2698.10901.5281.30228.7164l1.88026 1.8313c.38828.3781 1.00715.3781 1.39544 0l.55228-.5379c.05987.0253.12021.0498.18102.0734v.7288c0 .5523.44772 1 1 1h2.65912c.5523 0 1-.4477 1-1v-.7288c.1316-.0511.2612-.1064.3883-.1657l.5435.2614v.4339c0 .5523.4477 1 1 1H14v.0625c0 .5523.4477 1 1 1h.0909v.0625c0 .5523.4477 1 1 1h.6844l.4952.4823c1.1648 1.1345 3.0214 1.1345 4.1863 0l.2409-.2347c.1961-.191.3053-.454.3022-.7277-.0031-.2737-.1183-.5342-.3187-.7207l-6.2162-5.7847c.0173-.0398.0342-.0798.0506-.12h.7799c.5522 0 1-.4477 1-1V8.17969c0-.55229-.4478-1-1-1h-.7799c-.0187-.04583-.038-.09139-.0578-.13666l.5284-.51464c.1933-.18824.3023-.44659.3023-.71638 0-.26979-.109-.52813-.3023-.71637l-1.8803-1.8313c-.3883-.37816-1.0071-.37816-1.3954 0l-.5523.53788c-.0598-.02536-.1201-.04985-.1809-.07344V3c0-.55228-.4477-1-1-1H8.31818Z">
                            </path>
                        </svg>
                    </span>
                    <h3 class="font-medium leading-tight text-nowrap">Cài đặt Api</h3>
                </button>
            </li>
            <li role="presentation" class="me-2" routerLink="/manager/setting/payment"
                routerLinkActive="bg-primary-600 text-white rounded-e-lg">
                <button type="button"
                    class="max-md:relative inline-block py-4 px-6  dark:border-transparent   dark:text-gray-400 border-gray-100 hover:border-gray-300 dark:border-gray-700 dark:hover:text-gray-300">
                    <span
                        class="-mt-2 absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1"
                            class="w-3.5 h-3.5 fill-gray-500 dark:fill-gray-400" width="256" height="256"
                            viewBox="0 0 256 256" xml:space="preserve">
                            <g style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;"
                                transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)">
                                <path
                                    d="M 50.91 60.162 H 20.037 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 H 50.91 c 0.553 0 1 0.447 1 1 S 51.463 60.162 50.91 60.162 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 37.869 67.512 H 20.037 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 h 17.833 c 0.552 0 1 0.447 1 1 S 38.421 67.512 37.869 67.512 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 50.91 67.512 h -8.383 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 h 8.383 c 0.553 0 1 0.447 1 1 S 51.463 67.512 50.91 67.512 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 80.35 67.512 H 65.18 c -0.553 0 -1 -0.447 -1 -1 v -7.35 c 0 -0.553 0.447 -1 1 -1 h 15.17 c 0.553 0 1 0.447 1 1 v 7.35 C 81.35 67.064 80.902 67.512 80.35 67.512 z M 66.18 65.512 h 13.17 v -5.35 H 66.18 V 65.512 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 87.099 25.203 H 81.27 l -0.798 -8.916 c -0.147 -1.645 -1.611 -2.859 -3.251 -2.717 L 2.729 20.242 c -0.797 0.072 -1.519 0.449 -2.032 1.063 s -0.756 1.392 -0.685 2.188 l 4.036 45.065 c 0.139 1.555 1.45 2.729 2.983 2.729 c 0.089 0 0.178 -0.004 0.268 -0.012 l 1.918 -0.172 v 2.436 c 0 1.6 1.302 2.901 2.901 2.901 h 74.979 c 1.6 0 2.901 -1.302 2.901 -2.901 V 50.625 V 34.652 v -6.547 C 90 26.505 88.698 25.203 87.099 25.203 z M 7.122 69.284 c -0.557 0.055 -1.033 -0.357 -1.082 -0.903 L 2.004 23.315 c -0.049 -0.547 0.356 -1.032 0.903 -1.081 l 74.491 -6.671 c 0.539 -0.052 1.033 0.355 1.081 0.903 l 0.782 8.737 H 12.12 c -1.6 0 -2.901 1.302 -2.901 2.901 v 6.547 v 15.973 v 18.471 L 7.122 69.284 z M 11.218 36.652 H 88 v 11.973 H 11.218 V 36.652 z M 88 73.54 c 0 0.497 -0.404 0.901 -0.901 0.901 H 12.12 c -0.497 0 -0.901 -0.404 -0.901 -0.901 V 50.625 H 88 V 73.54 z M 11.218 34.652 v -6.547 c 0 -0.497 0.404 -0.901 0.901 -0.901 h 74.979 c 0.497 0 0.901 0.404 0.901 0.901 v 6.547 H 11.218 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                            </g>
                        </svg>
                    </span>
                    <h3 class="font-medium leading-tight text-nowrap">Thanh toán</h3>
                </button>
            </li>
            <li role="presentation" class="me-2" routerLink="/manager/setting/note-result"
                [routerLinkActiveOptions]="{exact: true}" routerLinkActive="bg-primary-600 text-white rounded-e-lg">
                <button type="button"
                    class="max-md:relative inline-block py-4 px-6  dark:border-transparent   dark:text-gray-400 border-gray-100 hover:border-gray-300 dark:border-gray-700 dark:hover:text-gray-300">
                    <span
                        class="-mt-2 absolute flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full -start-4 ring-4 ring-white dark:ring-gray-900 dark:bg-gray-700">

                        <svg class="w-3.5 h-3.5 fill-gray-500 dark:fill-gray-400" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="256" height="256"
                            viewBox="0 0 256 256" xml:space="preserve">
                            <g style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;"
                                transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)">
                                <path
                                    d="M 70.111 90 H 19.888 c -2.672 0 -4.846 -2.174 -4.846 -4.846 v -66.44 c 0 -2.672 2.174 -4.846 4.846 -4.846 h 9.061 c 0.552 0 1 0.448 1 1 s -0.448 1 -1 1 h -9.061 c -1.569 0 -2.846 1.276 -2.846 2.846 v 66.44 c 0 1.569 1.276 2.846 2.846 2.846 h 50.223 c 1.569 0 2.847 -1.276 2.847 -2.846 v -66.44 c 0 -1.569 -1.277 -2.846 -2.847 -2.846 h -9.061 c -0.553 0 -1 -0.448 -1 -1 s 0.447 -1 1 -1 h 9.061 c 2.673 0 4.847 2.174 4.847 4.846 v 66.44 C 74.958 87.826 72.784 90 70.111 90 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 61.051 19.383 H 28.949 c -0.552 0 -1 -0.448 -1 -1 v -3.397 c 0 -5.314 4.264 -9.651 9.55 -9.77 C 38.654 2.109 41.636 0 45 0 s 6.346 2.109 7.501 5.216 c 5.286 0.119 9.55 4.456 9.55 9.77 v 3.397 C 62.051 18.936 61.604 19.383 61.051 19.383 z M 29.949 17.383 h 30.102 v -2.397 c 0 -4.286 -3.486 -7.772 -7.772 -7.772 H 51.77 c -0.454 0 -0.851 -0.305 -0.967 -0.744 C 50.105 3.838 47.72 2 45 2 c -2.719 0 -5.105 1.838 -5.803 4.47 c -0.116 0.438 -0.513 0.744 -0.967 0.744 h -0.509 c -4.286 0 -7.772 3.487 -7.772 7.772 V 17.383 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 47.4 11.485 h -4.801 c -0.552 0 -1 -0.448 -1 -1 s 0.448 -1 1 -1 H 47.4 c 0.553 0 1 0.448 1 1 S 47.953 11.485 47.4 11.485 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 31.019 58.883 h -4.948 c -1.271 0 -2.305 -1.034 -2.305 -2.305 V 51.63 c 0 -1.271 1.034 -2.305 2.305 -2.305 h 4.948 c 1.271 0 2.305 1.034 2.305 2.305 v 4.948 C 33.323 57.849 32.29 58.883 31.019 58.883 z M 26.071 51.325 c -0.168 0 -0.305 0.137 -0.305 0.305 v 4.948 c 0 0.168 0.137 0.305 0.305 0.305 h 4.948 c 0.168 0 0.305 -0.137 0.305 -0.305 V 51.63 c 0 -0.168 -0.137 -0.305 -0.305 -0.305 H 26.071 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 64.271 51.325 H 39.872 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 h 24.399 c 0.553 0 1 0.447 1 1 S 64.824 51.325 64.271 51.325 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 52.653 58.883 H 39.872 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 h 12.781 c 0.553 0 1 0.447 1 1 S 53.206 58.883 52.653 58.883 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 27.789 40.452 c -0.319 0 -0.62 -0.152 -0.808 -0.411 l -3.023 -4.148 c -0.325 -0.446 -0.227 -1.072 0.219 -1.397 c 0.447 -0.326 1.072 -0.227 1.397 0.219 l 2.134 2.928 l 3.758 -6.263 c 0.284 -0.474 0.897 -0.628 1.372 -0.343 c 0.474 0.284 0.627 0.898 0.343 1.372 l -4.534 7.557 c -0.172 0.288 -0.478 0.469 -0.813 0.484 C 27.819 40.451 27.804 40.452 27.789 40.452 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 64.271 32.895 H 39.872 c -0.552 0 -1 -0.448 -1 -1 s 0.448 -1 1 -1 h 24.399 c 0.553 0 1 0.448 1 1 S 64.824 32.895 64.271 32.895 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 52.653 40.452 H 39.872 c -0.552 0 -1 -0.448 -1 -1 s 0.448 -1 1 -1 h 12.781 c 0.553 0 1 0.448 1 1 S 53.206 40.452 52.653 40.452 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 31.019 78.102 h -4.948 c -1.271 0 -2.305 -1.034 -2.305 -2.305 V 70.85 c 0 -1.271 1.034 -2.305 2.305 -2.305 h 4.948 c 1.271 0 2.305 1.034 2.305 2.305 v 4.947 C 33.323 77.067 32.29 78.102 31.019 78.102 z M 26.071 70.545 c -0.168 0 -0.305 0.137 -0.305 0.305 v 4.947 c 0 0.168 0.137 0.305 0.305 0.305 h 4.948 c 0.168 0 0.305 -0.137 0.305 -0.305 V 70.85 c 0 -0.168 -0.137 -0.305 -0.305 -0.305 H 26.071 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 64.271 70.545 H 39.872 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 h 24.399 c 0.553 0 1 0.447 1 1 S 64.824 70.545 64.271 70.545 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                                <path
                                    d="M 52.653 78.102 H 39.872 c -0.552 0 -1 -0.447 -1 -1 s 0.448 -1 1 -1 h 12.781 c 0.553 0 1 0.447 1 1 S 53.206 78.102 52.653 78.102 z"
                                    style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                                    transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round" />
                            </g>
                        </svg>
                    </span>
                    <h3 class="font-medium leading-tight text-nowrap">Thông tin khác</h3>
                </button>
            </li>
        </ul>
    </div>

    <div
        class=" relative shadow-lg bg-white dark:bg-gray-800 rounded-lg h-full overflow-hidden min-w-[calc(100%-13rem)]">
        <router-outlet></router-outlet>
    </div>
</div>