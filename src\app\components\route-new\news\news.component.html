<section class="relative pt-32 pb-20 overflow-hidden">
    <div class="absolute inset-0 z-0">
        <img alt="News Background" decoding="async" data-nimg="fill" class="object-cover"
            src="\assets\img\background\service_tour.jpg"
            style="position: absolute; height: 100%; width: 100%; inset: 0px;">
        <div class="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/30"></div>
    </div>
    <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-3xl mx-auto text-center mb-10">
            <h1 class="text-4xl md:text-5xl font-bold mb-6 text-white drop-shadow-md">Tin tức &amp; Cẩm nang du lịch
            </h1>
            <p class="text-lg text-white/90 mb-8 drop-shadow-sm">Cậ<PERSON> nh<PERSON><PERSON> những thông tin, kinh nghiệm và cẩm nang
                du lịch hữu ích cho chuyến đi của bạn</p>
            <div class="max-w-xl mx-auto relative">
                <input
                    class="flex w-full border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-12 h-14 rounded-full bg-white/90 backdrop-blur-sm border-white/30 focus:border-primary-500 shadow-lg"
                    placeholder="Tìm kiếm bài viết..." [(ngModel)]="searchModel.Keyword"
                    (keyup.enter)="onSearch($event)">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-search absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.3-4.3"></path>
                </svg>
                <button
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-white px-4 py-2 absolute right-1 top-1/2 -translate-y-1/2 rounded-full h-12 bg-primary-500 hover:bg-primary-600"
                    (click)="onSearch($event)">Tìm kiếm</button>
            </div>
        </div>
    </div>
</section>
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="flex overflow-x-auto hide-scrollbar space-x-2 mb-12 pb-2">
            <button routerLink="/news" routerLinkActive="text-primary-400 border-primary-400"
                [routerLinkActiveOptions]="{exact: true}"
                class="inline-flex items-center justify-center gap-2 text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-10 py-2 rounded-full px-4 whitespace-nowrap border-gray-200 hover:border-primary-200 hover:bg-primary-50">Tất
                cả</button>
            <button *ngFor="let category of categories" routerLink="/news/{{category.key}}"
                routerLinkActive="text-primary-400 border-primary-400"
                class="inline-flex items-center justify-center gap-2 text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-10 py-2 rounded-full px-4 whitespace-nowrap border-gray-200 hover:border-primary-200 hover:bg-primary-50">
                {{category.value}}
            </button>
        </div>
        @if(!_isLoading && dataTable.pageCount <= 0) { <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Không tìm thấy bài viết nào</h2>
            <p class="text-gray-600">Hãy thử tìm kiếm với từ khóa khác hoặc kiểm tra lại danh mục bạn đã chọn.</p>
    </div>
    }
    <div class="grid grid-cols-1 md:grid-cols-12 gap-6 mb-12">
        <!-- start new 1 -->
        <div class="md:col-span-8 group">
            @if(_isLoading) {
            <div class="relative h-[400px] rounded-3xl overflow-hidden bg-gray-200 animate-pulse">
                <div class="absolute inset-0 bg-gradient-to-t from-gray-300/80 via-gray-200/50 to-transparent">
                </div>
                <div class="absolute bottom-0 left-0 right-0 p-8">
                    <div class="h-6 w-24 bg-gray-300 rounded-full mb-4"></div>
                    <div class="h-8 w-3/4 bg-gray-300 rounded mb-4"></div>
                    <div class="h-4 w-full bg-gray-300 rounded mb-6"></div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="h-4 w-24 bg-gray-300 rounded"></div>
                            <div class="h-4 w-24 bg-gray-300 rounded"></div>
                            <div class="h-4 w-16 bg-gray-300 rounded"></div>
                        </div>
                        <div class="h-10 w-24 bg-gray-300 rounded-full"></div>
                    </div>
                </div>
            </div>
            }@else {
            @if(featuredNews){
            <div
                class="relative h-[400px] rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <img [alt]="featuredNews.name" loading="lazy" decoding="async" data-nimg="fill"
                    class="object-cover transition-transform duration-700 group-hover:scale-105"
                    [src]="featuredNews.pathImage" style="position: absolute; height: 100%; width: 100%; inset: 0px;">
                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent"></div>
                <div class="absolute bottom-0 left-0 right-0 p-8">
                    <div
                        class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent mb-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                        {{featuredNews.categoryName}}</div>
                    <h3
                        class="text-2xl md:text-3xl font-bold mb-4 text-white group-hover:text-primary-200 transition-colors line-clamp-2">
                        {{featuredNews.name}}</h3>
                    <p class="text-white/80 mb-6 line-clamp-2 max-w-3xl">{{featuredNews.description}}</p>
                    <div class="flex items-center justify-between flex-wrap">
                        <div class="flex items-center text-white/80 space-x-4">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 mr-2">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg>
                                <span>{{featuredNews.timeCreate}}</span>
                            </div>
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-clock h-4 w-4 mr-2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12 6 12 12 16 14"></polyline>
                                </svg>
                                <span>5 phút đọc</span>
                            </div>
                            <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-eye h-4 w-4 mr-2">
                                    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                </svg>
                                <span>{{featuredNews.viewCount | number:'1.0-0'}}</span>
                            </div>
                        </div>
                        <a routerLink="/news/{{featuredNews.seoCategory}}/{{featuredNews.seoTitle}}"
                            class="inline-flex items-center bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-full transition-colors">
                            Đọc tiếp
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-arrow-right h-4 w-4 ml-2">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            }
            }

        </div>
        <!-- end new 1 -->
        <div class="md:col-span-4 grid grid-rows-2 gap-6">
            @if(_isLoading) {
            <div class="space-y-6">
                <div class="relative h-[190px] rounded-3xl overflow-hidden bg-gray-200 animate-pulse">
                    <div class="absolute inset-0 bg-gradient-to-t from-gray-300/80 via-gray-200/50 to-transparent">
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 p-6">
                        <div class="h-4 w-20 bg-gray-300 rounded-full mb-2"></div>
                        <div class="h-6 w-3/4 bg-gray-300 rounded mb-2"></div>
                        <div class="flex items-center justify-between">
                            <div class="h-4 w-24 bg-gray-300 rounded"></div>
                            <div class="h-4 w-16 bg-gray-300 rounded"></div>
                        </div>
                    </div>
                </div>
                <div class="relative h-[190px] rounded-3xl overflow-hidden bg-gray-200 animate-pulse">
                    <div class="absolute inset-0 bg-gradient-to-t from-gray-300/80 via-gray-200/50 to-transparent">
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 p-6">
                        <div class="h-4 w-20 bg-gray-300 rounded-full mb-2"></div>
                        <div class="h-6 w-3/4 bg-gray-300 rounded mb-2"></div>
                        <div class="flex items-center justify-between">
                            <div class="h-4 w-24 bg-gray-300 rounded"></div>
                            <div class="h-4 w-16 bg-gray-300 rounded"></div>
                        </div>
                    </div>
                </div>
            </div>
            } @else {
            @for (item of secondaryNews; track $index) {
            <div class="group">
                <div
                    class="relative h-[190px] rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                    <img [alt]="item.name" loading="lazy" decoding="async" data-nimg="fill"
                        class="object-cover transition-transform duration-700 group-hover:scale-105"
                        [src]="item.pathImage"
                        style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent">
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 p-6">
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent mb-2 bg-primary-500 hover:bg-primary-600 text-white border-none text-xs">
                            {{item.categoryName}}
                        </div>
                        <h3
                            class="text-lg font-bold mb-2 text-white group-hover:text-primary-200 transition-colors line-clamp-2">
                            {{item.name}}</h3>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-white/80 text-xs"><svg xmlns="http://www.w3.org/2000/svg"
                                    width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-calendar h-3 w-3 mr-1">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg>
                                <span>{{item.timeCreate}}</span>
                            </div>
                            <a routerLink="/news/{{item.seoCategory}}/{{item.seoTitle}}"
                                class="text-white/90 hover:text-white text-sm font-medium transition-colors">Đọc
                                tiếp</a>
                        </div>
                    </div>
                </div>
            </div>
            }

            }
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        @if(_isLoading) {
        <div class="space-y-6">
            <div class="h-full rounded-3xl overflow-hidden shadow-lg bg-white border border-gray-100">
                <div class="relative h-48 w-full overflow-hidden bg-gray-200 animate-pulse">
                    <div class="absolute top-4 left-4 h-6 w-20 bg-gray-300 rounded-full"></div>
                </div>
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-3">
                        <div class="h-4 w-24 bg-gray-200 rounded"></div>
                        <div class="h-4 w-16 bg-gray-200 rounded"></div>
                    </div>
                    <div class="h-6 w-3/4 bg-gray-200 rounded mb-3"></div>
                    <div class="h-4 w-full bg-gray-200 rounded mb-4"></div>
                    <div class="h-4 w-24 bg-gray-200 rounded"></div>
                </div>
            </div>
        </div>
        <div class="space-y-6">
            <div class="h-full rounded-3xl overflow-hidden shadow-lg bg-white border border-gray-100">
                <div class="relative h-48 w-full overflow-hidden bg-gray-200 animate-pulse">
                    <div class="absolute top-4 left-4 h-6 w-20 bg-gray-300 rounded-full"></div>
                </div>
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-3">
                        <div class="h-4 w-24 bg-gray-200 rounded"></div>
                        <div class="h-4 w-16 bg-gray-200 rounded"></div>
                    </div>
                    <div class="h-6 w-3/4 bg-gray-200 rounded mb-3"></div>
                    <div class="h-4 w-full bg-gray-200 rounded mb-4"></div>
                    <div class="h-4 w-24 bg-gray-200 rounded"></div>
                </div>
            </div>
        </div>
        <div class="space-y-6">
            <div class="h-full rounded-3xl overflow-hidden shadow-lg bg-white border border-gray-100">
                <div class="relative h-48 w-full overflow-hidden bg-gray-200 animate-pulse">
                    <div class="absolute top-4 left-4 h-6 w-20 bg-gray-300 rounded-full"></div>
                </div>
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-3">
                        <div class="h-4 w-24 bg-gray-200 rounded"></div>
                        <div class="h-4 w-16 bg-gray-200 rounded"></div>
                    </div>
                    <div class="h-6 w-3/4 bg-gray-200 rounded mb-3"></div>
                    <div class="h-4 w-full bg-gray-200 rounded mb-4"></div>
                    <div class="h-4 w-24 bg-gray-200 rounded"></div>
                </div>
            </div>
        </div>
        } @else {
        @for (item of regularNews; track $index) {
        <div class="group">
            <div
                class="h-full rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100">
                <div class="relative h-48 w-full overflow-hidden">
                    <img [alt]="item.name" loading="lazy" decoding="async" data-nimg="fill"
                        class="object-cover transition-transform duration-700 group-hover:scale-105"
                        [src]="item.pathImage"
                        style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                    <div
                        class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 left-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                        {{item.categoryName}}
                    </div>
                    <button
                        class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:text-accent-foreground absolute top-4 right-4 bg-white/20 backdrop-blur-sm hover:bg-white/40 text-white rounded-full h-8 w-8"><svg
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-bookmark h-4 w-4">
                            <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="flex items-center text-sm text-gray-500 mb-3">
                        <div class="flex items-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 mr-1 text-primary-500">
                                <path d="M8 2v4"></path>
                                <path d="M16 2v4"></path>
                                <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                <path d="M3 10h18"></path>
                            </svg>
                            <span>{{item.timeCreate}}</span>
                        </div>
                        <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-eye h-4 w-4 mr-1 text-primary-500">
                                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            <span>{{item.viewCount | number:'1.0-0'}}</span>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors">
                        {{item.name}}
                    </h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">{{item.description}}</p>
                    <a routerLink="/news/{{item.seoCategory}}/{{item.seoTitle}}"
                        class="inline-flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors group-hover:translate-x-1 duration-300">Đọc
                        tiếp
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-arrow-right h-4 w-4 ml-1">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
        }
        }
    </div>
    <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
        @if(_isLoading) {
        <div class="md:col-span-5">
            <div class="h-full rounded-3xl overflow-hidden shadow-lg bg-white border border-gray-100">
                <div class="relative h-64 w-full overflow-hidden bg-gray-200 animate-pulse">
                    <div class="absolute top-4 left-4 h-6 w-24 bg-gray-300 rounded-full"></div>
                </div>
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-3">
                        <div class="h-4 w-24 bg-gray-200 rounded"></div>
                        <div class="h-4 w-20 bg-gray-200 rounded"></div>
                        <div class="h-4 w-16 bg-gray-200 rounded"></div>
                    </div>
                    <div class="h-8 w-3/4 bg-gray-200 rounded mb-3"></div>
                    <div class="h-4 w-full bg-gray-200 rounded mb-4"></div>
                    <div class="h-4 w-24 bg-gray-200 rounded"></div>
                </div>
            </div>
        </div>
        <div class="md:col-span-7">
            <div class="h-full rounded-3xl overflow-hidden shadow-lg bg-white">
                <div class="flex flex-col md:flex-row h-full">
                    <div class="relative md:w-1/2 h-64 md:h-auto overflow-hidden bg-gray-200 animate-pulse">
                        <div class="absolute top-4 left-4 h-6 w-24 bg-gray-300 rounded-full"></div>
                    </div>
                    <div class="md:w-1/2 p-6 md:p-8 bg-gradient-to-br from-primary-50 to-white">
                        <div class="flex items-center space-x-4 mb-3">
                            <div class="h-4 w-24 bg-gray-200 rounded"></div>
                            <div class="h-4 w-16 bg-gray-200 rounded"></div>
                        </div>
                        <div class="h-8 w-3/4 bg-gray-200 rounded mb-4"></div>
                        <div class="h-4 w-full bg-gray-200 rounded mb-6"></div>
                        <div class="h-10 w-32 bg-gray-200 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
        } @else {
        @for (item of specialNews; track $index) {
        <div class="md:col-span-5 group" *ngIf="$index %2 === 0">
            <div
                class="h-full rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100">
                <div class="relative h-48 w-full overflow-hidden">
                    <img [alt]="item.name" loading="lazy" decoding="async" data-nimg="fill"
                        class="object-cover transition-transform duration-700 group-hover:scale-105"
                        [src]="item.pathImage"
                        style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                    <div
                        class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 left-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                        {{item.categoryName}}
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex items-center text-sm text-gray-500 mb-3">
                        <div class="flex items-center mr-4"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-calendar h-4 w-4 mr-1 text-primary-500">
                                <path d="M8 2v4"></path>
                                <path d="M16 2v4"></path>
                                <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                <path d="M3 10h18"></path>
                            </svg>
                            <span>{{item.timeCreate}}</span>
                        </div>
                        <div class="flex items-center mr-4"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-clock h-4 w-4 mr-1 text-primary-500">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            <span>7 phút đọc</span>
                        </div>
                        <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-eye h-4 w-4 mr-1 text-primary-500">
                                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            <span>{{item.viewCount | number:'1.0-0'}}</span>
                        </div>
                    </div>
                    <h3 class="text-2xl font-bold mb-3 group-hover:text-primary-600 transition-colors">{{item.name}}
                    </h3>
                    <p class="text-gray-600 mb-4">{{item.description}}</p>
                    <a routerLink="/news/{{item.seoCategory}}/{{item.seoTitle}}"
                        class="inline-flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors group-hover:translate-x-1 duration-300">Đọc
                        tiếp
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-arrow-right h-4 w-4 ml-1">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
        <div class="md:col-span-7 group" *ngIf="$index %2 === 1">
            <div class="h-full rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <div class="flex flex-col md:flex-row h-full">
                    <div class="relative md:w-1/2 h-64 md:h-auto overflow-hidden">
                        <img [alt]="item.name" loading="lazy" decoding="async" data-nimg="fill"
                            class="object-cover transition-transform duration-700 group-hover:scale-105"
                            [src]="item.pathImage"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 left-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                            {{item.categoryName}}</div>
                    </div>
                    <div class="md:w-1/2 p-6 md:p-8 bg-gradient-to-br from-primary-50 to-white">
                        <div class="flex items-center text-sm text-gray-500 mb-3">
                            <div class="flex items-center mr-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-calendar h-4 w-4 mr-1 text-primary-500">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg>
                                <span>{{item.timeCreate}}</span>
                            </div>
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-eye h-4 w-4 mr-1 text-primary-500">
                                    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                </svg>
                                <span>{{item.viewCount | number:'1.0-0'}}</span>
                            </div>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-primary-600 transition-colors">
                            {{item.name}}</h3>
                        <p class="text-gray-600 mb-6">{{item.description}}</p>
                        <a routerLink="/news/{{item.seoCategory}}/{{item.seoTitle}}"
                            class="inline-flex items-center bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-full transition-colors">Đọc
                            tiếp<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-arrow-right h-4 w-4 ml-2">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        }

        }
    </div>
    <div class="flex justify-center mt-12" *ngIf="!_isLoading && dataTable.pageCount > 1">
        <div class="flex items-center space-x-2">

            <button (click)="getUrl(1)" [ngClass]="{'bg-gray-100 cursor-default':dataTable.pageIndex === 1}"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:bg-accent hover:text-accent-foreground rounded-full w-10 h-10 border-gray-200">
                <svg class="w-6 h-6 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                        d="m17 16-4-4 4-4m-6 8-4-4 4-4" />
                </svg>
            </button>
            <button (click)="getUrl(dataTable.pageIndex - 1)"
                [ngClass]="{'bg-gray-100 cursor-default':dataTable.pageIndex === 1}"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:bg-accent hover:text-accent-foreground rounded-full w-10 h-10 border-gray-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-4 w-4">
                    <path d="m15 18-6-6 6-6"></path>
                </svg>
            </button>
            <button *ngFor="let page of range(startIndex, finishIndex); let i = index" (click)="getUrl(page)"
                [ngClass]="{'bg-primary-50 text-primary-600 border-primary-200': page === dataTable.pageIndex}"
                [class.active]="page === dataTable.pageIndex"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:bg-accent hover:text-accent-foreground px-3 rounded-full w-10 h-10 border-gray-200">
                {{ page }}
            </button>

            <button (click)="getUrl(dataTable.pageIndex + 1)"
                [ngClass]="{'bg-gray-100 cursor-default': dataTable.pageIndex === dataTable.pageCount}"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:bg-accent hover:text-accent-foreground rounded-full w-10 h-10 border-gray-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-4 w-4">
                    <path d="m9 18 6-6-6-6"></path>
                </svg>
            </button>
            <button (click)="getUrl(dataTable.pageCount)"
                [ngClass]="{'bg-gray-100 cursor-default': dataTable.pageIndex === dataTable.pageCount}"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:bg-accent hover:text-accent-foreground rounded-full w-10 h-10 border-gray-200">
                <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                        d="m7 16 4-4-4-4m6 8 4-4-4-4" />
                </svg>
            </button>
        </div>
    </div>
    </div>
</section>
<section class="py-20 bg-gradient-to-b from-white to-primary-50 relative overflow-hidden">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-8">
                <div
                    class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200">
                    Đăng ký nhận tin</div>
                <h2 class="text-3xl font-bold mb-4">Nhận thông tin du lịch mới nhất</h2>
                <p class="text-gray-600">Đăng ký nhận bản tin của chúng tôi để cập nhật những thông tin du lịch mới
                    nhất và ưu đãi hấp dẫn</p>
            </div>
            <form [formGroup]="emailForm" (ngSubmit)="subscribeNewsletter()"
                class="flex flex-col md:flex-row gap-4 max-w-xl mx-auto">
                <div class="flex-1">
                    <input formControlName="email"
                        class="flex w-full border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 h-12 rounded-xl border-gray-200 focus:border-primary-500"
                        placeholder="Email của bạn" type="email">
                    <div *ngIf="emailError" class="text-red-500 text-sm mt-1">{{ emailError }}</div>
                </div>
                <button type="submit" [disabled]="isSubmitting"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 px-4 py-2 h-12 bg-primary-500 hover:bg-primary-600 text-white rounded-xl">
                    <span *ngIf="!isSubmitting">Đăng ký ngay</span>
                    <span *ngIf="isSubmitting">Đang xử lý...</span>
                </button>
            </form>
        </div>
    </div>
    <div class="absolute -top-16 -left-16 w-64 h-64 bg-primary-100 rounded-full opacity-30 blur-3xl"></div>
    <div class="absolute -bottom-32 -right-32 w-96 h-96 bg-blue-50 rounded-full opacity-40 blur-3xl"></div>
</section>