<div class="bg-gray-50 p-6 h-full overflow-y-auto">
    <div class="container mx-auto">
        <h1 class="text-2xl font-bold mb-6"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> Thông Tin Check-in</h1>
        <div dir="ltr" data-orientation="horizontal" class="w-full">
            <div class="h-10 items-center justify-center rounded-md bg-[#f4f4f5] p-1 text-[rgb(113,113,122)] grid w-full grid-cols-2"
                style="outline: none;">
                <button type="button" (click)="setTabSelected('edit')"
                    [ngClass]="{'bg-white text-black': tabSelected === 'edit'}"
                    class="inline-flex items-center line-clamp-1 overflow-hidden justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm">
                    Chỉnh Sửa
                </button>
                <button type="button" (click)="setTabSelected('view')"
                    [ngClass]="{'bg-white text-black': tabSelected === 'view'}"
                    class="inline-flex items-center line-clamp-1 overflow-hidden justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm">

                    Xem Trước
                </button>
            </div>
            <!-- start edit -->
            <div [ngClass]="{'hidden': tabSelected !== 'edit'}"
                class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-6">
                <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <h3 class="text-2xl font-semibold leading-none tracking-tight">Thời Gian Check-in</h3>
                        <p class="text-sm text-muted-foreground">Cập nhật thông tin thời gian check-in cho chuyến bay
                        </p>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <div class="grid gap-4 md:grid-cols-2">
                            <div class="space-y-2">
                                <label
                                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Chuyến
                                    Bay Nội Địa</label>
                                <input [(ngModel)]="NoteModel.TimeCheckIn.Domestic"
                                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    placeholder="Ví dụ: 90 phút">
                            </div>
                            <div class="space-y-2">
                                <label
                                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Chuyến
                                    Bay Quốc Tế</label>
                                <input [(ngModel)]="NoteModel.TimeCheckIn.International"
                                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    placeholder="Ví dụ: 3 tiếng">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <h3 class="text-2xl font-semibold leading-none tracking-tight">Giấy Tờ Tùy Thân</h3>
                        <p class="text-sm text-muted-foreground">Quản lý danh sách giấy tờ tùy thân cần thiết</p>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        @for(identityDoc of NoteModel.IdentityDocuments; track $index) {
                        <div class="flex items-start gap-2  rounded-lg relative">
                            <textarea [(ngModel)]="identityDoc.value"
                                class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[60px] flex-1"
                                placeholder="CCCD (đường quá hạn), Giấy Phép lái xe, Hộ Chiếu (passport), Thẻ Đảng..."></textarea>
                            <button (click)="removeIdentityDoc($index)"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  h-10 w-10 absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-trash2 h-4 w-4">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                    <line x1="10" x2="10" y1="11" y2="17"></line>
                                    <line x1="14" x2="14" y1="11" y2="17"></line>
                                </svg>
                            </button>
                        </div>
                        }

                        <div class="flex gap-2 mt-4">
                            <textarea [(ngModel)]="newIdentityDocText"
                                class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[60px] flex-1"
                                placeholder="Nhập giấy tờ tùy thân mới"></textarea>
                            <button type="button" (click)="addIdentityDoc()"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-black text-white hover:opacity-90 h-10 px-4 py-2 mt-auto">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plus mr-2 h-4 w-4">
                                    <path d="M5 12h14"></path>
                                    <path d="M12 5v14"></path>
                                </svg> Thêm
                            </button>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <h3 class="text-2xl font-semibold leading-none tracking-tight">Quy Định Đặc Biệt</h3>
                        <p class="text-sm text-muted-foreground">Quản lý các quy định đặc biệt cho hành khách</p>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        @for(specialRule of NoteModel.SpecialRules; track $index){
                        <div class="flex items-start gap-2  rounded-lg  relative">
                            <textarea [(ngModel)]="specialRule.value"
                                class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[60px] flex-1"></textarea>
                            <button (click)="removeSpecialRule($index)"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  h-10 w-10 absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-trash2 h-4 w-4">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                    <line x1="10" x2="10" y1="11" y2="17"></line>
                                    <line x1="14" x2="14" y1="11" y2="17"></line>
                                </svg>
                            </button>
                        </div>
                        }

                        <div class="flex gap-2 mt-4">
                            <textarea [(ngModel)]="newSpecialRuleText"
                                class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[60px] flex-1"
                                placeholder="Nhập quy định đặc biệt mới"></textarea>
                            <button type="button" (click)="addSpecialRule()"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-black text-white hover:opacity-90 h-10 px-4 py-2 mt-auto">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plus mr-2 h-4 w-4">
                                    <path d="M5 12h14"></path>
                                    <path d="M12 5v14"></path>
                                </svg> Thêm
                            </button>
                        </div>
                    </div>
                </div>
                <div class="mt-8">
                    <button (click)="saveNote()"
                        [disabled]="!NoteModel.TimeCheckIn.Domestic || !NoteModel.TimeCheckIn.International"
                        class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-[#fb6340] via-[#fb6340] to-[#fbb140] text-white hover:opacity-90 h-11 rounded-md px-8 w-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-save mr-2 h-5 w-5">
                            <path
                                d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z">
                            </path>
                            <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"></path>
                            <path d="M7 3v4a1 1 0 0 0 1 1h7"></path>
                        </svg> Lưu Thông Tin </button>
                </div>
            </div>
            <!-- end edit -->

            <!-- start view  -->
            <div [ngClass]="{'hidden': tabSelected !== 'view'}"
                class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-6">
                <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <h3 class="text-2xl font-semibold leading-none tracking-tight">Xem Trước Thông Tin Check-in</h3>
                        <p class="text-sm text-muted-foreground">Đây là cách thông tin sẽ hiển thị cho khách hàng</p>
                    </div>
                    <div class="p-6 pt-0 space-y-6">
                        <div class="space-y-2">
                            <h3 class="text-lg font-semibold border-b pb-2">Thời gian check-in</h3>
                            <ul class="list-disc pl-6 space-y-1">
                                <li>Quý khách vui lòng có mặt bay trước {{NoteModel.TimeCheckIn.Domestic}} (nội địa)
                                    hoặc {{NoteModel.TimeCheckIn.International}} (quốc tế) để làm
                                    thủ tục check-in.</li>
                            </ul>
                        </div>
                        <div class="space-y-2" *ngIf="NoteModel.IdentityDocuments.length > 0">
                            <h3 class="text-lg font-semibold border-b pb-2">Giấy tờ tùy thân</h3>
                            <ul class="list-disc pl-6 space-y-1">
                                @for(identityDoc of NoteModel.IdentityDocuments; track $index) {
                                <li>{{identityDoc.value}}</li>
                                }
                            </ul>
                        </div>
                        <div class="space-y-2" *ngIf="NoteModel.SpecialRules.length > 0">
                            <h3 class="text-lg font-semibold border-b pb-2">Quy định đặc biệt</h3>
                            <ul class="list-disc pl-6 space-y-1">
                                @for(specialRule of NoteModel.SpecialRules; track $index){
                                <li>{{specialRule.value}}</li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end view -->
        </div>
    </div>
</div>