<div class="w-full  bg-white rounded-lg shadow-sm h-full overflow-auto ">
    <div class="p-6 flex flex-col h-full">
        <h1 class="text-2xl font-bold mb-2"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> T<PERSON>ông Tin Thanh Toán</h1>
        <div class="h-10 items-center justify-center rounded-md bg-[#f4f4f5] p-1 text-[rgb(113,113,122)] grid w-full grid-cols-2"
            style="outline: none;">
            <button type="button" routerLink="/manager/setting/payment/cash" routerLinkActive="bg-white text-black"
                class="inline-flex items-center line-clamp-1 overflow-hidden justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm">
                <PERSON><PERSON> T<PERSON>ề<PERSON> Mặt
            </button>
            <button type="button" routerLink="/manager/setting/payment/credit" routerLinkActive="bg-white text-black"
                class="inline-flex items-center line-clamp-1 overflow-hidden justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm">

                Tài Khoản Ngân Hàng
            </button>
        </div>
        <div class="w-full h-max ">
            <router-outlet></router-outlet>


        </div>

    </div>
</div>