import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';

const apiUrl = environment.apiUrl;

@Injectable({
  providedIn: 'root'
})
export class UserService {

  constructor(
    private http: HttpClient
  ) { }

  getPaging(data: any) {
    return this.http.post(apiUrl + '/api/Users/<USER>', data);
  }
  getpartnerPaging(data: any): any {
    return this.http.post(apiUrl + '/api/partner/Users/<USER>', data);
  }
  updatePermission(data: any) {
    return this.http.put(apiUrl + '/api/Users/<USER>', data);
  }

  update(data: any) {
    var nameAdvance = this.getFirstAndLastName(data.FullName || data.fullName);
    data.FirstName = nameAdvance.firstName;
    data.LastName = nameAdvance.lastName;
    return this.http.put(apiUrl + '/api/Users', data);
  }
  updatePartner(data: any) {
    return this.http.put(apiUrl + '/api/partner/Users', data);
  }
  get(id: any) {
    return this.http.get(apiUrl + '/api/Users/' + id);
  }

  delete(id: string) {
    return this.http.delete(apiUrl + '/api/Users/' + id);
  }
  deletePartner(id: string) {
    return this.http.delete(apiUrl + '/api/partner/Users/' + id);
  }

  create(data: any) {
    return this.http.post(apiUrl + '/api/Users', data);
  }
  createPartner(data: any) {
    return this.http.post(apiUrl + '/api/partner/Users/<USER>', data);
  }

  getFirstAndLastName(fullName: string): { firstName: string; lastName: string } {
    const nameParts = fullName.trim().split(" ");

    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    return { firstName, lastName };
  }

  changePassword(data: any) {
    return this.http.put(apiUrl + '/api/Users/<USER>', data);
  }
}
