import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';

const apiUrl = environment.apiUrl

@Injectable({
  providedIn: 'root'
})
export class ActivityLogService {

  constructor(
    private readonly httpClient: HttpClient
  ) { }

  public getAuthLog(request: any) {
    return this.httpClient.post(apiUrl + "/api/ActivityLogs/paging/auth", request);
  }
}
