<div class="container relative h-full flex flex-row max-md:flex-col gap-2 min-w-full">
    <div class="container  relative shadow-lg bg-white dark:bg-gray-800 rounded-lg h-full overflow-y-scroll min-w-full">
        <main class="flex-1 p-6 md:p-8">
            <div class="w-full">
                <div class="flex flex-col gap-6">
                    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                        <h1 class="text-3xl font-bold tracking-tight">Lịch sử đăng nhập</h1>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="p-6">
                                <div class="flex items-center gap-4">
                                    <div class="p-3 rounded-lg bg-blue-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-clock h-6 w-6 text-blue-600">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12 6 12 12 16 14"></polyline>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-2xl font-bold">{{countSuccess + countFailed}}</p>
                                        <p class="text-sm text-muted-foreground">Tổng lần đăng nhập</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="p-6">
                                <div class="flex items-center gap-4">
                                    <div class="p-3 rounded-lg bg-green-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-circle-check-big h-6 w-6 text-green-600">
                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                            <path d="m9 11 3 3L22 4"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-2xl font-bold">{{countSuccess}}</p>
                                        <p class="text-sm text-muted-foreground">Đăng nhập thành công</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="p-6">
                                <div class="flex items-center gap-4">
                                    <div class="p-3 rounded-lg bg-red-50"><svg xmlns="http://www.w3.org/2000/svg"
                                            width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-circle-x h-6 w-6 text-red-600">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <path d="m15 9-6 6"></path>
                                            <path d="m9 9 6 6"></path>
                                        </svg></div>
                                    <div>
                                        <p class="text-2xl font-bold">{{countFailed}}</p>
                                        <p class="text-sm text-muted-foreground">Đăng nhập thất bại</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                        <div class="p-6">
                            <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div class="flex w-full max-w-sm items-center space-x-2">
                                    <input [(ngModel)]="keyword" (keyup.enter)="onSearch()"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        placeholder="Tìm kiếm theo IP, thiết bị..." type="search">
                                    <button (click)="onSearch()"
                                        class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10"
                                        type="button">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-search h-4 w-4">
                                            <circle cx="11" cy="11" r="8"></circle>
                                            <path d="m21 21-4.3-4.3"></path>
                                        </svg>
                                        <span class="sr-only">Tìm kiếm</span>
                                    </button>
                                </div>
                                <div class="flex items-center gap-2">
                                    <select [(ngModel)]="filterTime" (change)="onFilterChange()"
                                        class="rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
                                        <option [value]="null" selected>Tất cả</option>
                                        <option [value]="1">24 giờ qua</option>
                                        <option [value]="7">7 ngày qua</option>
                                        <option [value]="30">30 ngày qua</option>
                                        <option [value]="90">90 ngày qua</option>
                                    </select>
                                    <select [(ngModel)]="status" (change)="onFilterChange()"
                                        class="rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
                                        <option value="" selected>Tất cả trạng thái</option>
                                        <option value="success">Thành công</option>
                                        <option value="failed">Thất bại</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Chi tiết lịch sử đăng nhập
                            </h3>
                            <p class="text-sm text-muted-foreground">Danh sách tất cả các lần đăng nhập vào tài khoản
                                của bạn</p>
                        </div>
                        <div class="p-6 pt-0">
                            <div class="rounded-lg border">
                                <div class="relative w-full overflow-auto">
                                    <table class="w-full caption-bottom text-sm">
                                        <thead>
                                            <tr
                                                class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                                                <th
                                                    class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                                                    Thời gian</th>
                                                <th
                                                    class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                                                    Địa chỉ IP</th>
                                                <th
                                                    class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                                                    Thiết bị</th>
                                                <th
                                                    class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                                                    Trạng thái</th>
                                            </tr>
                                        </thead>
                                        <tbody class="[&_tr:last-child]:border-0">
                                            <tr *ngFor="let log of loginHistory"
                                                class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                                                [ngClass]="{'bg-red-50': !log.status}">
                                                <td class="p-4 align-middle">
                                                    <div class="flex flex-col">
                                                        <span class="font-medium">{{log.createdAt}}</span>
                                                    </div>
                                                </td>
                                                <td class="p-4 align-middle font-mono text-sm">{{log.ipAddress}}</td>
                                                <td class="p-4 align-middle">
                                                    <div class="flex items-center gap-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            class="lucide lucide-monitor h-4 w-4">
                                                            <rect width="20" height="14" x="2" y="3" rx="2"></rect>
                                                            <line x1="8" x2="16" y1="21" y2="21"></line>
                                                            <line x1="12" x2="12" y1="17" y2="21"></line>
                                                        </svg>
                                                        <span class="text-sm">{{log.device.browser}} -
                                                            {{log.device.os}}</span>
                                                    </div>
                                                </td>
                                                <td class="p-4 align-middle">
                                                    <div class="flex items-center gap-2">
                                                        <span [ngClass]="{
                                                            'text-green-600': log.status,
                                                            'text-red-600': !log.status
                                                        }">
                                                            @if(log.status){
                                                            <div
                                                                class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-green-600 border-green-200 bg-green-50">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                                    height="24" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="lucide lucide-circle-check-big h-3 w-3 mr-1">
                                                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                                    <path d="m9 11 3 3L22 4"></path>
                                                                </svg>Thành công
                                                            </div>
                                                            }@else {
                                                            <div
                                                                class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-red-600 border-red-200 bg-red-50">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                                    height="24" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="lucide lucide-circle-x h-3 w-3 mr-1">
                                                                    <circle cx="12" cy="12" r="10"></circle>
                                                                    <path d="m15 9-6 6"></path>
                                                                    <path d="m9 9 6 6"></path>
                                                                </svg>Thất bại
                                                            </div>
                                                            }
                                                        </span>
                                                    </div>
                                                </td>

                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center justify-center space-x-2 py-4">
                        <button [disabled]="isFirstPage" (click)="onPageChange(pageIndex - 1)"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10"
                            type="button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-left h-4 w-4">
                                <path d="m15 18-6-6 6-6"></path>
                            </svg>
                            <span class="sr-only">Trang trước</span>
                        </button>

                        @for (page of pageNumbers; track page) {
                        <button (click)="onPageChange(page)" [disabled]="page === pageIndex"
                            [class.bg-accent]="page === pageIndex" [class.text-accent-foreground]="page === pageIndex"
                            [class.cursor-default]="page === pageIndex"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"
                            type="button">
                            {{page}}
                        </button>
                        }

                        <button [disabled]="isLastPage" (click)="onPageChange(pageIndex + 1)"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10"
                            type="button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4">
                                <path d="m9 18 6-6-6-6"></path>
                            </svg>
                            <span class="sr-only">Trang sau</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>