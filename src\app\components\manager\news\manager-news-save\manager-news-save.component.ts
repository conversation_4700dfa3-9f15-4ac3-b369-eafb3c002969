import { Component, ElementRef, HostListener, Renderer2, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Route, Router } from '@angular/router';
import { CommonModule, Location } from '@angular/common';
import { CategoryNewsService } from '../../../../services/category-news/category-news.service';

import { Title } from '@angular/platform-browser';
import { NewsService } from '../../../../services/news/news.service';
import { TextEditorComponent } from '../../../ui-common/text-editor/text-editor.component';

@Component({
  selector: 'app-manager-news-save',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TextEditorComponent

  ],
  templateUrl: './manager-news-save.component.html',
  styleUrl: './manager-news-save.component.css'
})

export class ManagerNewsSaveComponent {
  titlePage = 'Thêm tin tức mới';
  isEditMode: boolean = false;
  @ViewChild('img_selected') imgSelected: ElementRef | null = null;
  countSelectedImg: number = 0;
  maxSelectedImg: number = 1;
  categories: any[] = [];
  images: File[] = [];

  crudForm?: FormGroup;
  formSubmitted: boolean = false;
  newsDetail?: any = null;

  constructor(
    private formBuilder: FormBuilder,
    private readonly categoryService: CategoryNewsService,
    private readonly newsService: NewsService,
    private readonly toastService: ToastrService,
    private renderer: Renderer2,
    private readonly location: Location,
    private readonly titleService: Title,
    private readonly route: ActivatedRoute,
    private readonly router: Router
  ) { }

  async ngOnInit(): Promise<void> {
    this.valiator();
    await this.loadCategories();

    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.isEditMode = true;
        this.loadDetailsNews(id);
        this.titleService.setTitle('Chỉnh sửa tin tức');
        this.titlePage = 'Chỉnh sửa tin tức';
        // this.loadNewsData(id); // Load data for the existing news item
      } else {
        this.isEditMode = false;
        // this.crudForm?.reset(); // Prepare for creating a new news item
      }
    });
  }
  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: any): void {
    $event.returnValue = 'Are you sure you want to leave?';
  }
  async loadCategories() {
    const res = await this.categoryService.getPartnerItems().toPromise();
    try {
      if (res.isSuccessed) {
        this.categories = res.resultObj;
      } else {
        this.toastService.error(res.message, "faidle");
      }
    } catch (err) {
      console.log(' eror', err);
    }
  }

  goBack() {
    this.router.navigate(['/manager/news']);
  }

  getImage(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    const selectedFiles = inputElement.files;
    if (!selectedFiles) return;

    const filesArray = Array.from(selectedFiles).slice(0, this.maxSelectedImg - this.countSelectedImg);

    filesArray.forEach((selectedFile) => {
      if (this.countSelectedImg >= this.maxSelectedImg) {
        alert(`You can only select up to ${this.maxSelectedImg} images.`);
        return;
      }
      const reader = new FileReader();

      reader.onload = (event: ProgressEvent<FileReader>) => {
        if (this.imgSelected) {
          const imgItem = this.renderer.createElement('div');
          this.renderer.addClass(imgItem, 'img-selected-item');

          const img = this.renderer.createElement('img');
          img.src = event.target?.result as string;

          const trashIcon = this.renderer.createElement('i');
          //add content to trash icon
          trashIcon.innerHTML = `<svg class="w-4 h-4 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"/>
                                </svg>
                                `;
          this.renderer.listen(trashIcon, 'click', () => {
            this.renderer.removeChild(this.imgSelected?.nativeElement, imgItem);
            this.images = this.images.filter((img) => img !== selectedFile);
            this.countSelectedImg--;
          });

          this.renderer.appendChild(imgItem, img);
          this.renderer.appendChild(imgItem, trashIcon);

          const customFileUpload = this.renderer.selectRootElement('.custom-file-upload', true);
          this.renderer.insertBefore(this.imgSelected.nativeElement, imgItem, customFileUpload);
          this.countSelectedImg++;
          this.crudForm?.patchValue({
            ImageFile: selectedFile
          });
        }
      };

      this.images.push(selectedFile);
      reader.readAsDataURL(selectedFile);
    });
  }

  valiator() {
    this.crudForm = this.formBuilder.group({
      Id: [''],
      Name: ['', [Validators.required, Validators.maxLength(255)]],
      Description: ['', [Validators.required, Validators.maxLength(2000)]],
      ImageFile: [null, [Validators.required]],
      CategoryId: ['', [Validators.required]],
      Stock: [1, [Validators.required]],
      ShowInHome: [false, [Validators.required]],
      Active: [true, [Validators.required]],
      Detail: ['', [Validators.required]]
    });
  }

  get fm(): any {
    return this.crudForm?.controls;
  }

  async loadDetailsNews(id: string) {
    this.crudForm?.get('ImageFile')?.clearValidators();
    this.crudForm?.get('ImageFile')?.updateValueAndValidity();

    var res = await this.newsService.getByID(id).toPromise();
    try {
      if (res.isSuccessed) {
        this.newsDetail = res.resultObj;
        this.crudForm?.patchValue({
          Id: res.resultObj.id,
          Name: res.resultObj.name,
          Description: res.resultObj.description,
          CategoryId: res.resultObj.categoryId,
          Stock: res.resultObj.stock,
          ShowInHome: res.resultObj.showInHome,
          Active: res.resultObj.active,
          Detail: res.resultObj.detail
        });
      } else {
        this.toastService.error(res.message, "faidle");
      }
    } catch (err: any) {
      console.log(' eror', err);
      this.toastService.error(err?.message, "faidle");
    }

  }
  resetImage() {
    const images = this.imgSelected?.nativeElement.querySelectorAll('.img-selected-item');
    images.forEach((imgItem: HTMLElement) => {
      this.renderer.removeChild(this.imgSelected?.nativeElement, imgItem);
    });
    this.countSelectedImg = 0;
    if (this.newsDetail)
      this.newsDetail.imagePath = null;
  }

  onSubmitCrudForm() {
    this.formSubmitted = true;
    if (this.crudForm?.invalid) {
      return;
    }
    const formData = new FormData();
    var arrAttr = this.crudForm?.controls;
    Object.keys(arrAttr as any).forEach(key => {
      if (key !== 'ImageFile') {
        formData.append(key, this.crudForm?.get(key)?.value);
      }
    });
    if (this.crudForm?.get('ImageFile')?.value !== null) {
      formData.append('ImageFile', this.crudForm?.get('ImageFile')?.value, this.crudForm?.get('ImageFile')?.value.name);
    }
    this.newsService.partnersave(formData).subscribe({
      next: (res: any) => {
        if (res.isSuccessed) {
          this.toastService.success("Lưu thành xông", "Success");
          this.location.back();
        } else {
          this.toastService.error(res.message, "faidle");
        }

      },
      error: (err: any) => {
        var modelErr = err.error;
        console.log(' eror', err);
        this.toastService.error(err.message, "faidle");
      }
    });
  }

  onCancel() {
    this.location.back();
  }
}
