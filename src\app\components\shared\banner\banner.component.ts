import { <PERSON>mpo<PERSON>, <PERSON><PERSON>nit, On<PERSON><PERSON>roy, Inject, PLATFORM_ID, Input, ViewChild, ElementRef, HostListener, inject } from '@angular/core';
import { FlightSearchComponent } from '../flight-search/flight-search.component';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { MenuAirportsComponent } from '../menu-airports/menu-airports.component';
import { NgbCalendar, NgbDate, NgbDateParserFormatter, NgbDateStruct, NgbDropdown, NgbInputDatepicker, NgbDropdownModule, NgbDatepickerModule, NgbCarouselModule, NgbCarousel } from '@ng-bootstrap/ng-bootstrap';
import { LunarService } from '../../../services/lunar/lunar.service';
import { debounceTime, Subject, switchMap } from 'rxjs';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { LoaderService } from '../../../services/loader/loader.service';
import { ScreenService } from '../../../services/screen/screen.service';
import { WorldService } from '../../../services/world/world.service';
import { BoxSearchTicketService } from '../../../services/BoxSearchSticket/box-search-sticket.service';

@Component({
  selector: 'app-banner',
  imports: [
    // FlightSearchComponent,
    CommonModule,
    RouterLink,
    RouterLinkActive,
    NgbDropdownModule,
    NgbDatepickerModule,
    NgbCarouselModule,
    MenuAirportsComponent,

    FormsModule,
    ReactiveFormsModule,
    FlightSearchComponent
  ],
  templateUrl: './banner.component.html',
  styleUrl: './banner.component.css'
})
export class BannerComponent implements OnInit, OnDestroy {
  currentTime: string = '';
  private timeInterval: any;
  private isBrowser: boolean;
  isMobile: boolean = false;
  @Input() vertical: boolean = false;
  @Input() isChild: boolean = false;
  @Input() _isShowBox: boolean = true;
  @Input() _isShowBottom: boolean = false;
  language: string = 'vi';
  isSubmitForm: boolean = false;
  passengerString: string = '';
  _userToggledBox = false;


  minDate: NgbDateStruct;
  maxDate: NgbDateStruct;
  lang: string | null = '';
  sliderWidth: number = 0;
  sliderTransform: string = '';
  sliderHeight: number = 0;
  AirportsDefault: any[] = [];
  AirportsDefaultFiltered: any;
  searchTerm: string = '';
  inputType: string = '';
  departureCode: any = ''
  arrivalCode = '';
  AirportListSelected: any[] = [];
  selectedIndex: number | null = null;
  _isOpenFeatureShowBox: boolean = false;
  searchSubject: Subject<string> = new Subject<string>();
  _searchTripRequest: any = {
    airlines: "VN",
    adult: 0,
    child: 0,
    infant: 0,
    originDestinationTrip: [],
    directOnly: false,
    typeTrip: "",
    currencyCode: "",
    isCalendar: false,
    isRound: false,
    isAllFare: false,
    userName: "",
    pnr: "",
    AirportListSelected: []
  }

  @Input()
  set searchTripRequest(value: any) {
    this._searchTripRequest = value;
  }
  get searchTripRequest(): any {
    return this._searchTripRequest;
  }

  get randomImg(){
    //random number from 1 to 10
    return Math.floor(Math.random() * 7) + 1;
  }



  dateStartValue: Date | null = null;
  @ViewChild('DateStart') DateStart: ElementRef | null = null;
  @ViewChild('datepicker') datepicker: NgbInputDatepicker | null = null;
  @ViewChild('departureDropdown', { static: false }) departureDropdown: NgbDropdown | null = null;
  @ViewChild('arrivalDropdown', { static: false }) arrivalDropdown: NgbDropdown | null = null;
  @ViewChild('bannerCarousel') bannerCarousel: NgbCarousel | null = null;
  requestForm?: FormGroup;
  Airports: any[] = [];
  translatePage: any;
  isMobileDevice: boolean = false;
  // private deviceSubscription!: Subscription;


  convertToLunar(date: any): string {
    const year = date.year;
    const month = date.month;
    const day = date.day;
    const lunarDate: Date = LunarService.convertToLunar(year, month, day);
    if (lunarDate) {
      return lunarDate.getDate() === 1 ? `${lunarDate.getDate()}/${lunarDate.getMonth() + 1}` : `${lunarDate.getDate()}`;
    } else {
      throw new Error('Không thể chuyển đổi ngày dương lịch sang âm lịch.');
    }
  }
  model: NgbDateStruct | undefined;

  constructor(
    @Inject(PLATFORM_ID) platformId: Object,
    private formBuilder: FormBuilder,
    public loaderService: LoaderService,
    private readonly screenService: ScreenService,
    private router: Router,
    private boxSearchTicketService: BoxSearchTicketService,
    private readonly worldService: WorldService,
  ) {
    this.isBrowser = isPlatformBrowser(platformId);
    const today = new Date();
    this.minDate = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate()
    };
    this.maxDate = {
      year: today.getFullYear() + 2,
      month: today.getMonth() + 1,
      day: today.getDate()
    }
  }

  async ngOnInit(): Promise<void> {
    this.checkDevice()
    this.validateForm();
    this.updateTime();
    if (this.isBrowser) {
      this.timeInterval = setInterval(() => {
        this.updateTime();
      }, 1000);
    }
    await this.loadAirportsDefault(this.lang || 'vi');
    this.updateDisplayMonths();


    this.bindData();
    this.searchSubject.pipe(
      debounceTime(500),
      switchMap(searchTerm => {
        return this.searchAirport(searchTerm);
      })
    ).subscribe(results => {
      this.AirportsDefaultFiltered = results?.data;
      if (this.AirportsDefaultFiltered?.length === 1 && this.AirportsDefaultFiltered[0].isParent) {
        this.AirportsDefaultFiltered[0].selected = true;
      }
      if (this.inputType === 'departure') {
        this.departureDropdown?.open();
      } else if (this.inputType === 'arrival') {
        this.arrivalDropdown?.open();
      }
    });
  }
  onDepartureDropdownClose(isOpen: any) {
    if (!isOpen) {
      if (!this.fm.departure.value.includes('(') || !this.fm.departure.value.includes(')')) {
        //clear value departure
        this.requestForm?.patchValue({
          departure: ''
        });
        this.departureCode = '';
      }
    }
  }
  checkDevice() {
    const width = window.innerWidth;
    // if (width <= 768) {
    //   this.vertical = true;
    // } 
    // else {
    //   this.vertical = this.vertical;
    // }
    this.vertical = width <= 768;
    this.isMobile = width <= 768;
    if (this._isShowBottom && !this._userToggledBox) {
      this._isShowBox = width > 768;
    }
  }
  onArrivalDropdownClose(isOpen: any) {
    if (!isOpen) {
      if (!this.fm.arrival.value.includes('(') || !this.fm.arrival.value.includes(')')) {
        this.requestForm?.patchValue({
          arrival: ''
        });
        this.arrivalCode = '';
      }
    }
  }
  handleAirportClick(event: any) {
    this.clickAirportItem(event.airport, event.tripType);
    this.departureDropdown?.close();
    this.arrivalDropdown?.close();
  }

  onFocus(event: any, typeInput: string) {
    event.preventDefault();
    this.inputType = typeInput;
  }

  continentClick(event: any, continentCode: string) {
    event.preventDefault();
    //set selected for continent clicked
    this.AirportsDefault.forEach((continent: any) => {
      continent.selected = continent.continentCode === continentCode;
    });

  }

  itemParentClick(event: any, parentCode: string, type: string) {
    event.preventDefault();
    this.AirportsDefaultFiltered.forEach((continent: any) => {
      continent.code === parentCode ? continent.selected = !continent.selected : continent.selected = false;
    });
  }

  async loadAirportsDefault(lang: string): Promise<void> {
    var data = await this.worldService.getFlightAirportsDefault(lang).toPromise();
    this.AirportsDefault = data.resultObj;

    this.AirportsDefault.forEach((continent: any) => {
      if (continent.continentCode === 'VN') {
        continent.selected = true;
        continent.airports.sort((a: any, b: any) => b.regionCode.localeCompare(a.regionCode));
      }
    });
  }

  toggleShowBox() {
    this._isShowBox = !this._isShowBox;
    this._userToggledBox = true;
  }

  async searchAirport(searchTerm: string): Promise<any> {
    if (searchTerm === '' || this.searchTerm === '') {
      return;
    }
    const searchTermLower = searchTerm.toLowerCase();

    var filteredAirports = this.AirportsDefault
      .flatMap((continent: any) => continent.airports ?? [])
      .filter((airport: any) =>
        airport.name.toLowerCase().includes(searchTermLower) ||
        airport.cityName.toLowerCase().includes(searchTermLower) ||
        airport.code.toLowerCase() === searchTermLower
      );

    filteredAirports = filteredAirports.filter((airport: any, index: number, self: any) =>
      index === self.findIndex((t: any) => (
        t.code === airport.code
      ))
    );

    if (filteredAirports.length === 0) {
      const request = {
        Language: this.lang || 'vi',
        keyword: searchTerm
      };
      var res = await this.worldService.searchAirport(request).toPromise();
      return Promise.resolve({
        data: res.resultObj,
      });

    } else {
      return Promise.resolve({
        data: filteredAirports,
      });
    }
  }


  swapAirport(event: any) {
    event.preventDefault();
    const departure = this.requestForm?.get('departure')?.value;
    const arrival = this.requestForm?.get('arrival')?.value;
    this.requestForm?.patchValue({
      departure: arrival,
      arrival: departure
    });
    this.departureCode = this.AirportListSelected.find((x: any) => x.type === 'arrival')?.airport.code || '';
    this.arrivalCode = this.AirportListSelected.find((x: any) => x.type === 'departure')?.airport.code || '';
    this.AirportListSelected = this.AirportListSelected.map((x: any) => {
      if (x.type === 'arrival') {
        x.type = 'departure';
      } else if (x.type === 'departure') {
        x.type = 'arrival';
      }
      return x;
    });
  }
  toggleDropdown(event: Event) {
    event.stopPropagation();
    const currentDropdown = (event.target as HTMLElement).closest(".dropdown");

    document.querySelectorAll(".dropdown").forEach((dropdown) => {
      if (dropdown !== currentDropdown) {
        dropdown.classList.remove("open");
      }
    });
    var dropdownMenu = currentDropdown?.querySelector('.dropdown-menu');
    if (dropdownMenu) {
      if (!dropdownMenu?.contains(event.target as Node)) {
        currentDropdown?.classList.toggle("open");
      }
    }
  }

  onSearchChange(searchTerm: string) {
    if (searchTerm.includes('(') && searchTerm.includes(')') || searchTerm === '') {
      this.AirportsDefaultFiltered = null;
      return;
    }
    this.searchSubject.next(searchTerm);
  }

  validateForm() {
    this.requestForm = this.formBuilder.group({
      departure: ['', Validators.required],
      arrival: ['', Validators.required],
      dateStart: ['', Validators.required],
      dateEnd: ['', this.isRT ? Validators.required : []],
      Adult: [1, Validators.min(1)],
      Child: [0, Validators.min(0)],
      Infant: [0, Validators.min(0)],
    });

    // Add subscription to Adult changes
    const adultControl = this.requestForm?.get('Adult');
    if (adultControl) {
      adultControl.valueChanges.subscribe(adultValue => {
        if (adultValue !== null && adultValue !== undefined) {
          const infantControl = this.requestForm?.get('Infant');
          if (infantControl && infantControl.value > adultValue) {
            infantControl.setValue(0);
          }
        }
      });
    }
  }

  bindData() {
    const searchTripRequestStr = this.boxSearchTicketService.getSearchTripRequest();
    this._searchTripRequest = typeof searchTripRequestStr === 'string' ? JSON.parse(searchTripRequestStr) : searchTripRequestStr;
    console.log('_searchTripRequest', this._searchTripRequest);
    if (this._searchTripRequest) {
      const params = new URLSearchParams(this._searchTripRequest.params);
      this.departureCode = params.get('departure') || '';
      this.arrivalCode = params.get('arrival') || '';
      const dateStart = params.get('dateStart');
      const dateEnd = params.get('dateEnd');
      this.fromDate = this.convertyyyyMMddToNgbDate(dateStart);
      this.toDate = this.convertyyyyMMddToNgbDate(dateEnd);
      this._searchTripRequest.adult = parseInt(params.get('Adult') || '1');
      this._searchTripRequest.child = parseInt(params.get('Child') || '0');
      this._searchTripRequest.infant = parseInt(params.get('Infant') || '0');

      this.isRT = !!dateEnd;


      this.requestForm?.patchValue({
        dateStart: dateStart || '',
        dateEnd: dateEnd || '',
        Adult: this._searchTripRequest.adult || 1,
        Child: this._searchTripRequest.child || 0,
        Infant: this._searchTripRequest.infant || 0
      });

      console.log('requestForm', this.requestForm?.value);
      this.AirportListSelected = this._searchTripRequest.airports || [];
      if (this._searchTripRequest.airports) {
        var departureAirport = this._searchTripRequest.airports.find((x: any) => x.type === 'departure');
        var arrivalAirport = this._searchTripRequest.airports.find((x: any) => x.type === 'arrival');

        this.requestForm?.patchValue({
          departure: `${departureAirport.airport.cityName} (${departureAirport.airport.code})`,
          arrival: `${arrivalAirport.airport.cityName} (${arrivalAirport.airport.code})`
        });
      }
    }
  }



  convertyyyyMMddToNgbDate(date: string | null): NgbDate | null {
    if (!date) {
      return null;
    }
    const dateParts = date.split('-');
    return new NgbDate(Number(dateParts[0]), Number(dateParts[1]), Number(dateParts[2]));
  }

  AirportClick(airport: any, inputType: string) {
    this.clickAirportItem(airport, inputType);
    this.searchTerm = '';
    this.AirportsDefaultFiltered = null;
    if (inputType === 'departure') {
      this.departureDropdown?.close();
    } else {
      this.arrivalDropdown?.close();
    }
  }

  clickAirportItem(airport: any, type: any) {
    if (type === 'departure') {
      this.requestForm?.patchValue({
        departure: `${airport.cityName} (${airport.code})`
      });
      this.departureCode = airport.code;
    }
    if (type === 'arrival') {
      this.requestForm?.patchValue({
        arrival: `${airport.cityName} (${airport.code})`
      });
      this.arrivalCode = airport.code;
    }

    //save airport to list
    if (this.AirportListSelected?.length === 0 || this.AirportListSelected === undefined) {
      this.AirportListSelected.push(
        {
          type: type,
          airport: airport
        }
      );
    }
    else {
      var index = this.AirportListSelected.findIndex(x => x.type === type);
      if (index === -1) {
        this.AirportListSelected.push(
          {
            type: type,
            airport: airport
          }
        );
      }
      else {
        this.AirportListSelected[index].airport = airport;
      }
    }
  }
  initRequest() {
    this.searchTripRequest = {
      airlines: "VN",
      adult: 0,
      child: 0,
      infant: 0,
      originDestinationTrip: [],
      directOnly: false,
      typeTrip: "",
      currencyCode: "",
      isCalendar: false,
      isRound: false,
      isAllFare: false,
      userName: "",
      pnr: "",
    };
  }
  saveToLocalStorage(params: any) {
    if (this.searchTripRequest === null) {
      this.initRequest();
    }
    // console.log(paramsString);
    this.searchTripRequest.adult = params?.get('Adult') ? Number(params.get('Adult')) : 0;
    this.searchTripRequest.child = params?.get('Child') ? Number(params.get('Child')) : 0;
    this.searchTripRequest.infant = params?.get('Infant') ? Number(params.get('Infant')) : 0;
    this.searchTripRequest.originDestinationTrip = [
      {
        originCode: params?.get('departure') || '',
        destinationCode: params?.get('arrival') || '',
        originDate: params?.get('dateStart') || ''
      }
    ];

    if (params?.get('dateEnd')) {
      this.searchTripRequest.originDestinationTrip.push({
        originCode: params?.get('arrival') || '',
        destinationCode: params?.get('departure') || '',
        originDate: params?.get('dateEnd') || ''
      });
    }
    this.searchTripRequest.AirportListSelected = this.AirportListSelected;
    this.boxSearchTicketService.addSearchTripRequest(this.searchTripRequest);
    // console.log(this.searchTripRequest);
  }

  async submitForm() {

    this.isSubmitForm = true;
    this.requestForm?.patchValue({
      dateStart: this.fromDate ? `${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}` : '',
      dateEnd: this.toDate ? `${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}` : '',
    });

    // Đảm bảo validator đúng trước khi kiểm tra invalid
    if (this.isRT) {
      this.fm.dateEnd?.setValidators([Validators.required]);
    } else {
      this.fm.dateEnd?.clearValidators();
    }
    this.fm.dateEnd?.updateValueAndValidity();


    if (this.requestForm?.invalid) {
      return;
    }
    var params = new URLSearchParams();
    params.append('departure', this.departureCode);
    params.append('arrival', this.arrivalCode);
    for (var key in this.requestForm?.value) {
      if (key === 'departure' || key === 'arrival' || !this.requestForm?.value[key]) {
        continue;
      }
      params.append(key, this.requestForm?.value[key]);
    }
    const searchFlight =
    {
      params: params.toString(),
      airports: this.AirportListSelected
    };


    // this.saveToLocalStorage(JSON.stringify(searchFlight));

    this.boxSearchTicketService.addSearchTripRequest(JSON.stringify(searchFlight));
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl('/TripSelection?' + params.toString());
    });
  }

  resetForm() {
    this.requestForm?.reset();
    this.isSubmitForm = false;
    this.fromDate = this.calendar.getToday();
    this.toDate = this.calendar.getToday();
  }

  get fm(): any {
    return this.requestForm?.controls;
  }

  checkErrorDateFlight(): boolean {
    if (this.isRT) {
      return this.toDate === null || this.fromDate === null;
    }
    return this.fromDate === null;
  }

  formatDateInput(event: any) {
    if (event) {
      const formattedDate = `${event.day}/${event.month}/${event.year}`;
      const dateSelect = new Date(event.year, event.month - 1, event.day);
      this.dateStartValue = dateSelect;
      if (this.DateStart) {
        this.DateStart.nativeElement.value = formattedDate;
      }
    }
  }
  fromDate: NgbDate | null = null;
  toDate: NgbDate | null = null;
  hoveredDate: NgbDate | null = null;

  @ViewChild('fromDatepicker') fromDatepicker: NgbInputDatepicker | null = null;
  @ViewChild('toDatepicker') toDatepicker: NgbInputDatepicker | null = null;

  onDateSelection(date: NgbDate, type: 'from' | 'to' = 'from') {
    // Nếu chưa có ngày đi, luôn gán ngày đi trước
    if (!this.fromDate) {
      this.fromDate = date;
      this.requestForm?.patchValue({
        dateStart: this.fromDate ? `${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}` : ''
      });
      this.fm.dateStart?.markAsTouched();
      this.fm.dateStart?.updateValueAndValidity();
      if (!this.isRT) {
        setTimeout(() => {
          this.fromDatepicker?.close();
        });
      } else {
        setTimeout(() => {
          this.fromDatepicker?.close();
          this.toDatepicker?.open();
        });
      }
      return; // Luôn return, không set ngày về nếu ngày đi chưa có
    }

    // Đã có ngày đi, xử lý như bình thường
    if (type === 'from') {
      this.fromDate = date;
      this.requestForm?.patchValue({
        dateStart: this.fromDate ? `${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}` : ''
      });
      this.fm.dateStart?.markAsTouched();
      this.fm.dateStart?.updateValueAndValidity();
      if (!this.isRT) {
        setTimeout(() => {
          this.fromDatepicker?.close();
        });
      } else {
        setTimeout(() => {
          this.fromDatepicker?.close();
          this.toDatepicker?.open();
        });
      }
    } else {
      this.toDate = date;
      this.requestForm?.patchValue({
        dateEnd: this.toDate ? `${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}` : ''
      });
      this.fm.dateEnd?.markAsTouched();
      this.fm.dateEnd?.updateValueAndValidity();
      setTimeout(() => {
        this.toDatepicker?.close();
      });
    }
  }

  calendar = inject(NgbCalendar);

  formatter = inject(NgbDateParserFormatter);
  isRT: boolean = true;

  isRange(date: NgbDate): boolean {
    return !!(this.fromDate && this.toDate && date.after(this.fromDate) && date.before(this.toDate));
  }

  isInside(date: NgbDate): boolean {
    return !!(this.fromDate && this.toDate && date.after(this.fromDate) && date.before(this.toDate));
  }

  isHovered(date: NgbDate): boolean {
    return !!(this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate));
  }

  getValueDisplayDay(type: 'from' | 'to'): string {
    const date = type === 'from' ? this.fromDate : this.toDate;
    if (!date) return '';
    return `${date.day}/${date.month}/${date.year}`;
  }

  changeTypeTrip(isRT: boolean) {
    this.isRT = isRT;
    if (!isRT) {
      this.toDate = null;
      this.fm.dateEnd?.clearValidators();
      this.fm.dateEnd?.updateValueAndValidity();
    } else {
      this.fm.dateEnd?.setValidators([Validators.required]);
      this.fm.dateEnd?.updateValueAndValidity();
    }
  }

  get adultOptions(): number[] {
    // Người lớn: 1 đến 9, tổng người lớn + trẻ em <= 9
    const max = 9 - this.fm.Child?.value;
    return Array.from({ length: max }, (_, i) => i + 1);
  }

  get childOptions(): number[] {
    // Trẻ em: 0 đến (9 - người lớn)
    const max = 9 - this.fm.Adult?.value;
    return Array.from({ length: max + 1 }, (_, i) => i);
  }

  get infantOptions(): number[] {
    // Trẻ sơ sinh: 0 đến số người lớn
    const maxInfants = Math.min(this.fm.Adult?.value || 0, 9);
    return Array.from({ length: maxInfants + 1 }, (_, i) => i);
  }


  getValueDisplayQuantity(): string {
    var result = '';
    const adult = this.lang === 'vi' ? 'người lớn' : 'Adult';
    const child = this.lang === 'vi' ? 'trẻ em' : 'Child';
    const infant = this.lang === 'vi' ? 'em bé' : 'Infant';
    const currentAdult = this.fm.Adult?.value || 1;
    const currentChild = this.fm.Child?.value || 0;
    const currentInfant = this.fm.Infant?.value || 0;
    if (currentAdult > 0) {
      result += `${currentAdult} ${adult}`;
    }
    if (currentChild > 0) {
      result += result.length > 0 ? `, ${currentChild} ${child}` : `${currentChild} ${child}`;
    }
    if (currentInfant > 0) {
      result += result.length > 0 ? `, ${currentInfant} ${infant}` : `${currentInfant} ${infant}`;
    }
    return result;
  }

  displayMonths = 1;
  updateDisplayMonth(month: number) {
    this.displayMonths = month;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.updateDisplayMonths();
    this.checkDevice()
  }

  updateDisplayMonths() {
    this.displayMonths = window.innerWidth <= 768 ? 1 : 2;
  }

  ngAfterViewInit(): void {
    if (this.bannerCarousel) {
      console.log('NgbCarousel instance:', this.bannerCarousel);
    }
  }

  ngOnDestroy() {
    // Clear interval when component is destroyed
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  }

  private updateTime() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    this.currentTime = `${hours}:${minutes}`;
  }
}
