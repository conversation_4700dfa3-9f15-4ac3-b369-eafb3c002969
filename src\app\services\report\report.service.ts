import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

const apiUrl = environment.apiUrl + '/api/Report/';

@Injectable({
  providedIn: 'root'
})
export class ReportService {

  constructor(
    private http: HttpClient
  ) { }

  getReport(): Observable<any> {
    return this.http.get(`${apiUrl}dashboard`);
  }
  getPartnerReport(): Observable<any> {
    return this.http.get(`${apiUrl}dashboard-partner`);
  }
  getPartnerReportWithId(id: string): Observable<any> {
    return this.http.get(`${apiUrl}dashboard-partner/${id}`);
  }
  getPartnerApiReport(): Observable<any> {
    return this.http.get(`${apiUrl}dashboard-partner-api`);
  }
  getPartnerApiReportWithId(id: string): Observable<any> {
    return this.http.get(`${apiUrl}dashboard-partner-api/${id}`);
  }
}
