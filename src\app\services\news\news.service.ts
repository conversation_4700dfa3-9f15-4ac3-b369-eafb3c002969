import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CryptoService } from '../crypto/crypto.service';
import { environment } from '../../../environments/environment.development';


const apiUrl = environment.apiUrl;
const publicKey = atob(environment.publicKey);
@Injectable({
  providedIn: 'root'
})

export class NewsService {
  //select
  constructor(
    private http: HttpClient,
    private cryptoService: CryptoService,
  ) { }

  getPartnerNewsPaging(request: any): any {
    return this.http.post<any>(`${apiUrl}/api/partner/News/paging`, request);
  }
  getPartnerClientNewsPaging(request: any): any {
    return this.http.post<any>(`${apiUrl}/api/partner/News/client/paging`, request);
  }

  async getNewsPaging(request: any): Promise<Observable<any>> {
    try {
      const requestString = JSON.stringify(request);
      const encryptedText = await this.cryptoService.encryptForge(publicKey, requestString);
      const requestEncrypt = {
        encryptData: encryptedText
      };
      return this.http.post<any>(`${apiUrl}/api/News/paging`, requestEncrypt);
    } catch (error) {
      throw error;
    }
  }

  hide(listId: string[], show: boolean): Observable<any> {
    const queryString = listId.map(id => `listId=${id}`).join('&');
    return this.http.put<any>(apiUrl + `/api/News?show=${show}&${queryString}`, null);
  }

  save(data: FormData): Observable<any> {
    return this.http.post<any>(apiUrl + `/api/News`, data);
  }

  partnersave(data: FormData): Observable<any> {
    return this.http.post<any>(apiUrl + `/api/partner/News`, data);
  }

  getByID(Id: string): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/News/${Id}`);
  }

  getNewsTopTime(quantity: number): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/News/top-time?top=${quantity}`);
  }

  getNewsTopView(quantity: number): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/News/top-view?top=${quantity}`);
  }

  getArticle(articleTitle: string): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/News/seo_title=${articleTitle}`);
  }
  getArticlePlus(articleTitle: string): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/News/article/${articleTitle}`);
  }
  getPartnerArticle(articleTitle: string): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/partner/News/client/${articleTitle}`);
  }
  getNewsByType(type: string, quantity: number): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/News/top-time?type=${type}&top=${quantity}`);
  }

  productSearch(request: any): Observable<any> {
    return this.http.post<any>(apiUrl + `/api/News/product-search`, request);
  }
}
