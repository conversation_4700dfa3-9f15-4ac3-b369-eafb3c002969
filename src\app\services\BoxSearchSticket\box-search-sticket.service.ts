import { Injectable } from '@angular/core';
import * as CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root'
})
export class BoxSearchTicketService {
  private storageKey = 'searchFlight';
  private encryptionKey = 'searchFlight';
  _searchTripRequest: any = {
    airlines: "VN",
    adult: 0,
    child: 0,
    infant: 0,
    originDestinationTrip: [],
    directOnly: false,
    typeTrip: "",
    currencyCode: "",
    isCalendar: false,
    isRound: false,
    isAllFare: false,
    userName: "",
    pnr: "",
    AirportListSelected: [],
  }

  constructor() { }

  addSearchTripRequest(data: any) {
    const encryptedData = this.encryptData(data);
    localStorage.setItem(this.storageKey, encryptedData);
    this._searchTripRequest = data;
  }

  removeSearchTripRequest() {
    localStorage.removeItem(this.storageKey);
    this._searchTripRequest = {
      airlines: "VN",
      adult: 0,
      child: 0,
      infant: 0,
      originDestinationTrip: [],
      directOnly: false,
      typeTrip: "",
      currencyCode: "",
      isCalendar: false,
      isRound: false,
      isAllFare: false,
      userName: "",
      pnr: "",
      AirportListSelected: [],
    };
  }

  getSearchTripRequest() {
    const encryptedData = localStorage.getItem(this.storageKey);
    if (encryptedData) {
      var data = this.decryptData(encryptedData);
      this._searchTripRequest = data;
      return data;
    }
    return null;
  }

  private encryptData(data: any): string {
    return CryptoJS.AES.encrypt(JSON.stringify(data), this.encryptionKey).toString();
  }

  private decryptData(data: string): any {
    const bytes = CryptoJS.AES.decrypt(data, this.encryptionKey);
    return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  }
}
