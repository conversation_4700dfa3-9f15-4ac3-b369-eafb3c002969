.editor-container {
    margin: 20px;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px;
}

.toolbar {
    position: relative;
    top: 0;
    z-index: 10;
}

.editor ul,
.editor ol {
    list-style-type: disc;
    list-style-position: outside;
    margin-left: 1.5rem;
    padding-left: 1.5rem;
}

.editor ul {
    list-style-type: disc;
}

.editor ol {
    list-style-type: decimal;
}

img.img-selected {
    outline: 2px solid #3182ce;
    outline-offset: 2px;
    position: relative;
    z-index: 1000;
}

.img-resize-handle {
    position: absolute;
    background: #fff;
    border: 2px solid #3182ce;
    border-radius: 2px;
    width: 10px;
    height: 10px;
    z-index: 10;
    box-sizing: border-box;
    pointer-events: all;
}

.img-crop-overlay {
    pointer-events: auto;
}

.img-crop-rect {
    pointer-events: none;
}

pre {
    background: #f5f5f5;
    color: #222;
    font-family: '<PERSON>ra Mono', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Monaco', monospace;
    font-size: 14px;
    padding: 12px 16px;
    border-radius: 6px;
    margin: 8px 0;
    overflow-x: auto;
    white-space: pre-wrap;
    /* Cho phép xuống dòng trong <pre> thay vì tạo nhiều <pre> */
    word-break: break-word;
    line-height: 1.6;
    border: 1px solid #e5e7eb;
}

pre code {
    background: none;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    padding: 0;
    border: none;
}

.table-col-resize-handle {
    position: absolute;
    width: 6px;
    top: 0;
    right: -3px;
    bottom: 0;
    cursor: col-resize;
    z-index: 100;
    background: rgba(49, 130, 206, 0.15);
    border-radius: 2px;
    transition: background 0.2s;
}

.table-col-resize-handle:hover {
    background: #3182ce;
}

.table-row-resize-handle {
    position: absolute;
    height: 6px;
    left: 0;
    right: 0;
    bottom: -3px;
    cursor: row-resize;
    z-index: 100;
    background: rgba(49, 130, 206, 0.15);
    border-radius: 2px;
    transition: background 0.2s;
}

.table-row-resize-handle:hover {
    background: #3182ce;
}

.table-total-resize-handle {
    box-shadow: 0 2px 6px #0002;
    transition: background 0.2s;
}

.table-total-resize-handle:hover {
    background: #2563eb;
}

ul[style*="list-style-type: box"] li::marker {
    content: "\25A1 ";
    /* □ */
}

ul[style*="list-style-type: dash"] li::marker {
    content: "\2014 ";
    /* — */
}

ul[style*="list-style-type: dot"] li::marker {
    content: "\00B7 ";
    /* · */
}

ul[style*="list-style-type: filled"] li::marker {
    content: "\25CF ";
    /* ● */
}

ul[style*="list-style-type: ring"] li::marker {
    content: "\25CB ";
    /* ○ */
}

ul[style*="list-style-type: black-square"] li::marker {
    content: "\25A0 ";
    /* ■ */
}

ul[style*="list-style-type: diamond"] li::marker {
    content: "\25C6 ";
    /* ◆ */
}

ul[style*="list-style-type: arrow"] li::marker {
    content: "\2192 ";
    /* → */
}

ul[style*="list-style-type: check"] li::marker {
    content: "\2713 ";
    /* ✓ */
}

/* Checklist style: ẩn bullet khi li có checkbox */
.editor ul[style*="list-style-type: none"] li input[type="checkbox"] {
    accent-color: #22c55e;
    /* màu xanh lá cho checkbox, tuỳ ý */
}

.editor ul[style*="list-style-type: none"] li input[type="checkbox"] {
    vertical-align: middle;
    margin-right: 8px;
}

.editor ul[style*="list-style-type: none"] li {
    list-style-type: none;
    /* ẩn bullet nếu là checklist */
    position: relative;
    padding-left: 0;
}

/* Đảm bảo checkbox không bị to quá */
.editor ul li input[type="checkbox"] {
    width: 1.1em;
    height: 1.1em;
    cursor: pointer;
}

/* Optional: style cho blockquote */
.editor blockquote {
    border-left: 4px solid #a3a3a3;
    margin: 1em 0;
    padding: 0.5em 1em;
    color: #555;
    /* background: #f9f9f9; */
    font-style: italic;
}

/* Toolbar nổi cho ảnh khi được chọn */
.editor-image-toolbar {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -38px;
    /* hoặc top: 100% + 8px nếu muốn dưới ảnh */
    z-index: 1001;
    background: #fff;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-shadow: 0 2px 8px #0002;
    padding: 4px 8px;
    display: flex;
    gap: 6px;
    align-items: center;
    min-height: 32px;
    min-width: 120px;
    pointer-events: auto;
    transition: box-shadow 0.2s;
}

.editor-image-toolbar button {
    background: #f3f4f6;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    color: #374151;
    cursor: pointer;
    font-size: 15px;
    transition: background 0.15s;
}

.editor-image-toolbar button:hover {
    background: #e0e7ef;
    color: #2563eb;
}

.editor-image-toolbar .divider {
    width: 1px;
    height: 20px;
    background: #e5e7eb;
    margin: 0 4px;
}

/* Emoji Picker & Special Character Picker Styles */
.emoji-picker,
.special-char-picker {
    position: absolute;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    width: 300px;
    max-height: 400px;
    overflow: hidden;
    display: block !important;
    /* Force display */
}

.picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.picker-header span {
    font-weight: 500;
    color: #1a202c;
}

.close-button {
    background: none;
    border: none;
    color: #64748b;
    font-size: 20px;
    cursor: pointer;
    padding: 0 4px;
}

.close-button:hover {
    color: #1a202c;
}

.picker-content {
    padding: 8px;
    max-height: 350px;
    overflow-y: auto;
    overflow-x: hidden;
}

.emoji-grid,
.special-char-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
}

.emoji-item,
.special-char-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid transparent;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    font-size: 20px;
    padding: 0;
    transition: all 0.2s;
}

.emoji-item:hover,
.special-char-item:hover {
    background: #f1f5f9;
    border-color: #e2e8f0;
}


.text-color-button {
    position: relative;
    min-width: 44px;
    min-height: 40px;
    cursor: pointer;
    background: #f5f5f5;
    border: none;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: box-shadow 0.15s;
}

.text-color-button:hover {
    box-shadow: 0 2px 8px #0001;
}

.text-color-bar {
    display: block;
    width: 24px;
    height: 5px;
    border-radius: 2px;
    margin-top: 2px;
    border: 1px solid #e0e0e0;
}


.color-palette,
.bg-color-palette,
.text-color-palette {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 8px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    padding: 8px;
}

.bg-color-button {
    position: relative;
}

.bg-color-button svg {
    width: 24px;
    height: 24px;
}

.bg-color-button:hover {
    background-color: #f5f5f5;
}

.color-palette .color-grid,
.bg-color-palette .color-grid,
.text-color-palette .color-grid {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.color-palette .color-row,
.bg-color-palette .color-row,
.text-color-palette .color-row {
    display: flex;
    gap: 4px;
}

.color-palette .color-item,
.bg-color-palette .color-item,
.text-color-palette .color-item {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #eee;
}

.color-palette .color-item:hover,
.bg-color-palette .color-item:hover,
.text-color-palette .color-item:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-palette .color-actions,
.bg-color-palette .color-actions,
.text-color-palette .color-actions {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #eee;
    text-align: center;
}

.color-palette .clear-color,
.bg-color-palette .clear-color,
.text-color-palette .clear-color {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
}

.color-palette .clear-color:hover,
.bg-color-palette .clear-color:hover,
.text-color-palette .clear-color:hover {
    background-color: #f5f5f5;
}

.editor h1 {
    font-size: 2.2em;
    font-weight: bold;
    color: #1e293b;
    margin: 1em 0 0.5em 0;
}

.editor h2 {
    font-size: 1.8em;
    font-weight: bold;
    color: #334155;
    margin: 1em 0 0.5em 0;
}

.editor h3 {
    font-size: 1.5em;
    font-weight: bold;
    color: #475569;
    margin: 1em 0 0.5em 0;
}

.editor h4 {
    font-size: 1.2em;
    font-weight: bold;
    color: #64748b;
    margin: 1em 0 0.5em 0;
}

.editor h5 {
    font-size: 1em;
    font-weight: bold;
    color: #64748b;
    margin: 1em 0 0.5em 0;
}

.editor h6 {
    font-size: 0.9em;
    font-weight: bold;
    color: #94a3b8;
    margin: 1em 0 0.5em 0;
}