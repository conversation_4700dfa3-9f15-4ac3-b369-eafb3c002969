import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';

const API_URL = environment.apiUrl + '/api/TicketIssueFees';

@Injectable({
  providedIn: 'root'
})
export class TicketIssueFeeService {

  constructor(
    private http: HttpClient
  ) { }

  postTicketIssueFee(data: any): any {
    return this.http.post(API_URL, data);
  }


  paging(data: any): any {
    return this.http.post(API_URL + '/paging', data);
  }

  deleteTicketIssueFee(data: any): any {
    return this.http.post(API_URL + '/delete', data);
  }
}
