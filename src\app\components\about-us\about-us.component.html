<div class="min-h-screen bg-white">

    <section class="relative pt-32 pb-20 overflow-hidden">
        <div class="absolute inset-0 z-0"><img alt="About Background" decoding="async" data-nimg="fill"
                class="object-cover" src="\assets\img\background\service_tour.jpg"
                style="position: absolute; height: 100%; width: 100%; inset: 0px;">
            <div class="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/30"></div>
        </div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-3xl mx-auto text-center">
                <div class="inline-flex items-center rounded-full border text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-primary/80 mb-4 bg-white/20 backdrop-blur-sm text-white border-white/10 py-1.5 px-4"
                    data-v0-t="badge">Về chúng tôi</div>
                <h1 class="text-4xl md:text-5xl font-bold mb-6 text-white drop-shadow-md">Hành trình của chúng tôi</h1>
                <p class="text-lg text-white/90 mb-8 drop-shadow-sm">Từ một ý tưởng nhỏ đến việc trở thành nền tảng du
                    lịch được tin tưởng bởi hàng triệu khách hàng</p>
            </div>
        </div>
    </section>

    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Dịch Vụ Của Chúng Tôi</h2>
                <p class="text-gray-600 text-lg max-w-3xl mx-auto">Cung cấp các dịch vụ đặt vé máy bay chuyên nghiệp, uy
                    tín với giá cả cạnh tranh nhất thị trường</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="rounded-lg border text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white"
                    data-v0-t="card">
                    <div class="p-8 text-center">
                        <div class="h-16 w-16 rounded-2xl bg-blue-100 flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-plane h-8 w-8 text-blue-600">
                                <path
                                    d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4 text-gray-900">Đặt Vé Máy Bay</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">Đặt vé máy bay trong nước và quốc tế với hơn 50
                            hãng hàng không nổi tiếng thế giới</p>

                    </div>
                </div>
                <div class="rounded-lg border text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white"
                    data-v0-t="card">
                    <div class="p-8 text-center">
                        <div class="h-16 w-16 rounded-2xl bg-green-100 flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-building h-8 w-8 text-green-600">
                                <rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect>
                                <path d="M9 22v-4h6v4"></path>
                                <path d="M8 6h.01"></path>
                                <path d="M16 6h.01"></path>
                                <path d="M12 6h.01"></path>
                                <path d="M12 10h.01"></path>
                                <path d="M12 14h.01"></path>
                                <path d="M16 10h.01"></path>
                                <path d="M16 14h.01"></path>
                                <path d="M8 10h.01"></path>
                                <path d="M8 14h.01"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4 text-gray-900">Đặt Phòng Khách Sạn</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">Đặt phòng khách sạn tại hơn 100 quốc gia trên thế
                            giới với giá ưu đãi đặc biệt</p>

                    </div>
                </div>
                <div class="rounded-lg border text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white"
                    data-v0-t="card">
                    <div class="p-8 text-center">
                        <div class="h-16 w-16 rounded-2xl bg-yellow-100 flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-map-pin h-8 w-8 text-yellow-600">
                                <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                                <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4 text-gray-900">Tour Du Lịch Trọn Gói</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">Các tour du lịch trong nước và quốc tế được thiết
                            kế đặc biệt với chi phí hợp lý</p>

                    </div>
                </div>
                <div class="rounded-lg border text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white"
                    data-v0-t="card">
                    <div class="p-8 text-center">
                        <div class="h-16 w-16 rounded-2xl bg-purple-100 flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="h-8 w-8 text-purple-600">
                                <rect width="18" height="13" x="3" y="6" rx="2"></rect>
                                <path d="m3 10 1.5-1.5L6 10l1.5-1.5L9 10l1.5-1.5L12 10l1.5-1.5L15 10l1.5-1.5L18 10">
                                </path>
                                <path d="M3 16h18"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4 text-gray-900">Dịch Vụ Visa</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">Hỗ trợ làm visa du lịch, công tác và định cư cho
                            hơn 50 quốc gia trên thế giới</p>

                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center">
                <div class="lg:col-span-2">
                    <h2 class="text-3xl md:text-4xl font-bold mb-6 text-gray-900">Về Phòng Vé Indeed Travel</h2>
                    <div class="space-y-6 text-gray-600 leading-relaxed">
                        <p class="text-lg">Với kinh nghiệm trong lĩnh vực đặt vé máy bay và du lịch, Phòng Vé Indeed Travel
                            tự hào là một trong những đại lý vé máy bay uy tín hàng đầu tại Việt Nam.</p>
                        <p class="text-lg">Chúng tôi cam kết mang đến cho khách hàng những trải nghiệm đặt vé thuận
                            tiện, giá cả cạnh tranh cùng dịch vụ hỗ trợ tận tâm 24/24.</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                        <div class="flex items-start">
                            <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-circle-check-big h-5 w-5 text-blue-600">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <path d="m9 11 3 3L22 4"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-1">Giá vé tốt nhất</h4>
                                <p class="text-sm text-gray-600">Luôn cung cấp giá vé cạnh tranh nhất thị trường</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-circle-check-big h-5 w-5 text-blue-600">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <path d="m9 11 3 3L22 4"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-1">Hỗ trợ 24/24</h4>
                                <p class="text-sm text-gray-600">Đội ngũ chuyên viên luôn sẵn sàng hỗ trợ</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-circle-check-big h-5 w-5 text-blue-600">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <path d="m9 11 3 3L22 4"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-1">Đặt vé nhanh chóng</h4>
                                <p class="text-sm text-gray-600">Quy trình đặt vé đơn giản, chỉ trong 3 phút</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="lg:col-span-1 w-full items-center justify-center md:block hidden">
                    <div class=" rounded-2xl p-8 text-center text-white border shadow-xl w-fit">
                        <img alt="Indeed Travel" loading="lazy" decoding="async"
                            src="/assets/img/favicon/logoNgocMai-text.png" class="object-contain h-28"
                            style="inset: 0px; color: transparent;">
                    </div>
                </div>
            </div>
        </div>
    </section>


    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div dir="ltr" data-orientation="horizontal" class="w-full">
                <div class="text-center mb-8">
                    <div role="tablist" aria-orientation="horizontal"
                        class="h-10 items-center justify-center text-muted-foreground grid grid-cols-2 bg-gray-100 p-1 rounded-xl max-w-md mx-auto"
                        tabindex="0" data-orientation="horizontal" style="outline: none;">
                        <button type="button" role="tab" [attr.aria-selected]="activeTab === 'story'"
                            [attr.data-state]="activeTab === 'story' ? 'active' : 'inactive'"
                            (click)="setActiveTab('story')"
                            class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 rounded-lg data-[state=active]:bg-white data-[state=active]:text-primary-600 data-[state=active]:shadow-sm"
                            [class.bg-white]="activeTab === 'story'" [class.text-primary-600]="activeTab === 'story'"
                            [class.shadow-sm]="activeTab === 'story'" tabindex="-1" data-orientation="horizontal"
                            data-radix-collection-item="">Câu chuyện
                        </button>
                        <button type="button" role="tab" [attr.aria-selected]="activeTab === 'values'"
                            [attr.data-state]="activeTab === 'values' ? 'active' : 'inactive'"
                            (click)="setActiveTab('values')"
                            class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 rounded-lg data-[state=active]:bg-white data-[state=active]:text-primary-600 data-[state=active]:shadow-sm"
                            [class.bg-white]="activeTab === 'values'" [class.text-primary-600]="activeTab === 'values'"
                            [class.shadow-sm]="activeTab === 'values'" tabindex="0" data-orientation="horizontal"
                            data-radix-collection-item="">Giá trị
                        </button>
                    </div>
                </div>
                <div *ngIf="activeTab === 'story'" role="tabpanel"
                    class="ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-8">
                    <div class="max-w-4xl mx-auto">
                        <div class="text-center mb-12">
                            <h2 class="text-3xl font-bold mb-4">Câu chuyện của chúng tôi</h2>
                            <p class="text-gray-600 text-lg">Hành trình phát triển từ một ý tưởng nhỏ đến nền tảng du
                                lịch hàng đầu</p>
                        </div>
                        <div class="space-y-8">
                            <div
                                class="rounded-lg border bg-card text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300">
                                <div class="p-6">
                                    <div class="flex items-start">
                                        <div
                                            class="h-12 w-12 rounded-xl bg-primary-100 text-primary-600 flex items-center justify-center mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-rocket h-6 w-6">
                                                <path
                                                    d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z">
                                                </path>
                                                <path
                                                    d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z">
                                                </path>
                                                <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path>
                                                <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-semibold mb-3">Khởi đầu</h3>
                                            <p class="text-gray-600">Năm 2015, với tình yêu du lịch và mong muốn giúp
                                                mọi người dễ dàng tiếp cận với những chuyến bay giá tốt, chúng tôi bắt
                                                đầu hành trình của mình.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="rounded-lg border bg-card text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300">
                                <div class="p-6">
                                    <div class="flex items-start">
                                        <div
                                            class="h-12 w-12 rounded-xl bg-blue-100 text-blue-600 flex items-center justify-center mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-trending-up h-6 w-6">
                                                <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                                <polyline points="16 7 22 7 22 13"></polyline>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-semibold mb-3">Phát triển</h3>
                                            <p class="text-gray-600">Từ một nhóm nhỏ, chúng tôi đã phát triển thành một
                                                nền tảng du lịch toàn diện, phục vụ hàng triệu khách hàng mỗi năm.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="rounded-lg border bg-card text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300">
                                <div class="p-6">
                                    <div class="flex items-start">
                                        <div
                                            class="h-12 w-12 rounded-xl bg-green-100 text-green-600 flex items-center justify-center mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-target h-6 w-6">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <circle cx="12" cy="12" r="6"></circle>
                                                <circle cx="12" cy="12" r="2"></circle>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-semibold mb-3">Hiện tại & Tương lai</h3>
                                            <p class="text-gray-600">Chúng tôi tiếp tục đổi mới và mở rộng dịch vụ.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="activeTab === 'values'" role="tabpanel"
                    class="ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-8">
                    <div class="max-w-4xl mx-auto">
                        <div class="text-center mb-12">
                            <h2 class="text-3xl font-bold mb-4">Giá trị cốt lõi</h2>
                            <p class="text-gray-600 text-lg">Những giá trị định hướng mọi hoạt động và quyết định của
                                chúng tôi</p>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="rounded-lg border bg-card text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300"
                                data-v0-t="card">
                                <div class="p-6">
                                    <div class="flex items-start">
                                        <div
                                            class="h-12 w-12 rounded-xl bg-red-100 text-red-600 flex items-center justify-center mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-heart h-6 w-6">
                                                <path
                                                    d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z">
                                                </path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-semibold mb-3">Khách hàng là trung tâm</h3>
                                            <p class="text-gray-600">Chúng tôi luôn đặt nhu cầu và trải nghiệm của khách
                                                hàng lên hàng đầu trong mọi quyết định.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="rounded-lg border bg-card text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300"
                                data-v0-t="card">
                                <div class="p-6">
                                    <div class="flex items-start">
                                        <div
                                            class="h-12 w-12 rounded-xl bg-yellow-100 text-yellow-600 flex items-center justify-center mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-award h-6 w-6">
                                                <path
                                                    d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526">
                                                </path>
                                                <circle cx="12" cy="8" r="6"></circle>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-semibold mb-3">Chất lượng dịch vụ</h3>
                                            <p class="text-gray-600">Cam kết cung cấp dịch vụ chất lượng cao với đội ngũ
                                                chuyên nghiệp và tận tâm.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="rounded-lg border bg-card text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300"
                                data-v0-t="card">
                                <div class="p-6">
                                    <div class="flex items-start">
                                        <div
                                            class="h-12 w-12 rounded-xl bg-blue-100 text-blue-600 flex items-center justify-center mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-target h-6 w-6">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <circle cx="12" cy="12" r="6"></circle>
                                                <circle cx="12" cy="12" r="2"></circle>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-semibold mb-3">Đổi mới sáng tạo</h3>
                                            <p class="text-gray-600">Không ngừng cải tiến và ứng dụng công nghệ mới để
                                                mang đến trải nghiệm tốt nhất.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="rounded-lg border bg-card text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300"
                                data-v0-t="card">
                                <div class="p-6">
                                    <div class="flex items-start">
                                        <div
                                            class="h-12 w-12 rounded-xl bg-green-100 text-green-600 flex items-center justify-center mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-circle-check-big h-6 w-6">
                                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                <path d="m9 11 3 3L22 4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-semibold mb-3">Minh bạch &amp; Tin cậy</h3>
                                            <p class="text-gray-600">Xây dựng niềm tin thông qua sự minh bạch trong mọi
                                                giao dịch và dịch vụ.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div
                class="bg-gradient-to-r from-primary-500 to-primary-600 rounded-3xl p-8 md:p-12 text-center text-white relative overflow-hidden">
                <div class="absolute -top-20 -right-20 w-64 h-64 bg-primary-400/20 rounded-full blur-3xl"></div>
                <div class="absolute -bottom-20 -left-20 w-64 h-64 bg-primary-400/20 rounded-full blur-3xl"></div>
                <div class="relative z-10">
                    <h2 class="text-3xl md:text-4xl font-bold mb-4">Sẵn sàng bắt đầu hành trình?</h2>
                    <p class="text-white/90 mb-8 text-lg max-w-2xl mx-auto">Hãy để chúng tôi đồng hành cùng bạn trong
                        những chuyến đi tuyệt vời. Khám phá thế giới với dịch vụ tốt nhất từ Indeed Travel.</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button routerLink="/"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  h-10 bg-white text-primary-600 hover:bg-primary-50 px-8 py-3 rounded-xl font-medium">Đặt
                            vé ngay<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-arrow-right h-5 w-5 ml-2">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </button>
                        <a href="tel:0833901323"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:text-accent-foreground h-10 border-white/30 text-white hover:bg-white/10 px-8 py-3 rounded-xl font-medium">Liên
                            hệ tư vấn</a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>