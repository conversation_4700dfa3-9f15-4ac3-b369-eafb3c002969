import { Component, inject } from '@angular/core';
import { CategoryNewsService } from '../../../services/category-news/category-news.service';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { SearchStateService } from '../../../services/search-state/search-state.service';
import { NewsService } from '../../../services/news/news.service';
import { range } from '../../../common/function.common';
import { FormsModule } from '@angular/forms';

interface News {
  id: string;
  index: number;
  name: string;
  seoTitle: string;
  categoryName: string;
  seoCategory: string;
  pathImage: string;
  description: string;
  timeCreate: string;
  author: string;
  viewCount: number;
  status: boolean;
}

interface DataTable {
  allRecords: number;
  from: number;
  to: number;
  pageCount: number;
  pageSize: number;
  pageIndex: number;
  totalRecords: number;
  items: News[];
}


@Component({
  selector: 'app-category-news',
  imports: [
    CommonModule,
    FormsModule,
    RouterLink,
    RouterLinkActive
  ],
  templateUrl: './category-news.component.html',
  styleUrl: './category-news.component.css'
})

export class CategoryNewsComponent {
  private readonly newsService = inject(NewsService);
  private readonly categoryNewsService = inject(CategoryNewsService);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);

  _isLoading: boolean = true;
  categories: any[] = [];
  featuredNews: News | null = null;
  secondaryNews: News[] = [];
  regularNews: News[] = [];
  specialNews: News[] = [];

  // Search and pagination
  searchModel: any = {
    PageIndex: 1,
    PageSize: 8,
    SortColumn: '',
    SortOrder: 'asc',
    Type: '',
    Keyword: '',
    FilterStatus: null
  };

  dataTable: DataTable = {
    allRecords: 0,
    from: 0,
    to: 0,
    pageCount: 0,
    pageSize: 0,
    pageIndex: 0,
    totalRecords: 0,
    items: []
  };
  startIndex = 1;
  finishIndex = 1;
  title: string = '';

  async ngOnInit(): Promise<void> {
    this.route.paramMap.subscribe(params => {
      this.title = window.location.href.split('/').reverse()[0];
      if (this.title !== '') {
        this.searchModel.Type = this.title;
        this.searchModel.pageIndex = 1;
        this.getPaging();
      }
    });
    this.loadCategories();
  }

  async loadCategories() {
    try {
      const res = await this.categoryNewsService.getPartnerClientItems().toPromise();
      if (res.isSuccessed) {
        this.categories = res.resultObj;
      }
    } catch (err) {
      console.error('Error loading categories:', err);
    }
  }

  get categorySelected(): string {
    //get category name selected
    const selectedCategory = this.categories.find(category => category.key === this.searchModel.Type);
    return selectedCategory ? selectedCategory.value : '';
  }

  // Pagination methods
  async changePageSize(event: Event): Promise<void> {
    const value = (event.target as HTMLSelectElement).value;
    this.searchModel.PageSize = Number(value);
    await this.getPaging();
  }

  async getUrl(page: number): Promise<void> {
    if (page < 1 || page > this.dataTable.pageCount || page === this.searchModel.PageIndex) {
      return;
    }
    this.searchModel.PageIndex = page;
    await this.getPaging();
  }

  // Data fetching methods
  async getPaging(): Promise<void> {
    try {
      this._isLoading = true;
      const res = await this.newsService.getPartnerClientNewsPaging(this.searchModel).toPromise();
      if (res.isSuccessed) {
        this.dataTable = res.resultObj;
        this.updatePageIndices();
        this.organizeNewsLayout();
        this._isLoading = false;
      }
    } catch (error) {
      this._isLoading = false;
      console.error('Có lỗi xảy ra khi tải dữ liệu');
    }
  }

  private organizeNewsLayout(): void {
    const items = this.dataTable.items || [];
    if (items.length > 0) {
      // Featured news (first item)
      this.featuredNews = items[0];

      // Secondary news (next 2 items)
      this.secondaryNews = items.slice(1, 3);

      // Regular news (next 3 items)
      this.regularNews = items.slice(3, 6);

      // Special news (remaining items)
      this.specialNews = items.slice(6);
    } else {
      this.featuredNews = null;
      this.secondaryNews = [];
      this.regularNews = [];
      this.specialNews = [];
    }
  }

  range(start: number, end: number): number[] {
    return range(start, end);
  }

  // Search and filter methods
  async onSubmitSearch(): Promise<void> {
    this.searchModel.PageIndex = 1;
    await this.getPaging();
  }

  private updatePageIndices(): void {
    if (this.dataTable.pageCount <= 5) {
      this.startIndex = 1;
      this.finishIndex = this.dataTable.pageCount;
    } else if (this.dataTable.pageIndex <= 3) {
      this.startIndex = 1;
      this.finishIndex = 5;
    } else if (this.dataTable.pageIndex + 2 >= this.dataTable.pageCount) {
      this.startIndex = this.dataTable.pageCount - 4;
      this.finishIndex = this.dataTable.pageCount;
    } else {
      this.startIndex = this.dataTable.pageIndex - 2;
      this.finishIndex = this.dataTable.pageIndex + 2;
    }
  }
  clearCategory(event: Event): void {
    event.preventDefault();
    this.searchModel.Type = '';
    this.searchModel.PageIndex = 1;
    this.searchModel.Keyword = '';
    //redict to /news
    this.router.navigate(['/news']);
  }
  // Search methods
  onSearch(event: Event): void {
    this.onSubmitSearch();
  }

  onCategoryClick(categoryKey: string): void {
    this.searchModel.Type = categoryKey;
    this.searchModel.PageIndex = 1;
    this.onSubmitSearch();
  }
}