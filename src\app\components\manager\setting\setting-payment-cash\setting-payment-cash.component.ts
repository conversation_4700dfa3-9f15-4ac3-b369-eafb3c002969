import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { FeatureService } from '../../../../services/feature/feature.service';

@Component({
  selector: 'app-setting-payment-cash',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './setting-payment-cash.component.html',
  styleUrl: './setting-payment-cash.component.css'
})
export class SettingPaymentCashComponent {

  settingCashForm?: FormGroup;
  formSubmitted: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private readonly featureService: FeatureService
  ) {

  }


  ngOnInit(): void {
    this.valiator();
  }
  ngAfterViewInit(): void {
    this.getPartnerPaymentInfo();
  }

  private valiator() {
    this.settingCashForm = this.formBuilder.group({
      paymentAddress: ['', [Validators.required, Validators.maxLength(512)]],
      workingHours: ['', [Validators.required, Validators.maxLength(512)]],
      paymentDeadline: ['', [Validators.required, Validators.maxLength(512)]],
      note: ['', [Validators.maxLength(1024)]],
    });
  }

  get fm(): any {
    return this.settingCashForm?.controls;
  }

  getPartnerPaymentInfo() {
    this.featureService.getPartnerPaymentInfo('cash').subscribe((res: any) => {
      if (res.isSuccessed) {
        this.settingCashForm?.patchValue(JSON.parse(res.resultObj));
      }
    });
  }

  public onSubmitForm() {
    this.formSubmitted = true;
    if (!this.settingCashForm?.valid) {
      return;
    }
    this.featureService.PartnerUpdatePaymentInfo(this.settingCashForm?.value, 'cash').subscribe(
      (res: any) => {
        if (res.isSuccessed) {
          this.formSubmitted = false;
          alert('Cập nhật thành công!');
        } else {
          alert('Cập nhật thất bại!');
        }
      },
      (error: any) => {
        alert('Error: Cập nhật thất bại!');
      }
    );

  }


}
