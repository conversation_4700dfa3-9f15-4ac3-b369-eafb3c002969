/** @type {import('tailwindcss').Config} */

const colors = [
  'slate',//1
  'gray',//2
  'zinc',//3
  'neutral',//4
  'stone',//5
  'red',//6
  'orange',//7
  'amber',//8
  'yellow',//9
  'lime',//10
  'green',//11
  'emerald',//12
  'teal',//13
  'cyan',//14
  'sky',//15
  'blue',//16
  'indigo',//17
  'violet',//18
  'purple',//19
  'fuchsia',//20
  'pink',//21
  'rose',//22
]


export default {
  darkMode: 'class',
  content: ["./src/**/*.{html,ts,js,json,md}"],
  safelist: [
    ...colors.flatMap(color => [
      `from-${color}-400`,
      `to-${color}-500`,
      `bg-${color}-400`,
      `bg-${color}-500`,
      `ring-${color}-400`,
    ])
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: "#f0f9ff",
        100: "#e0f2fe",
        200: "#bae6fd",
        300: "#7dd3fc",
        400: "#38bdf8",
        500: "#0ea5e9",
        600: "#0284c7",
        700: "#0369a1",
        800: "#075985",
        900: "#0c4a6e",
        950: "#082f49"
        },
      },
    },
  },
  plugins: [],
}