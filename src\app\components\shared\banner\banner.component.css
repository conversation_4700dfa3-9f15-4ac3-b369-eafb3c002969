.dp-hidden,
.dp-hidden input {
    width: 0;
    margin: 0;
    border: none;
    padding: 0;
}



.custom-day {
    text-align: center;
    padding: 0.185rem 0.25rem;
    display: inline-block;
    height: 2rem;
    width: 2rem;
    position: relative;
}

.custom-day.focused {
    background-color: #e6e6e6;
}

.custom-day.range,
.custom-day:hover {
    background-color: #f05922;
    color: white;
}

.custom-day.faded {
    background-color: #f0592287;
}

.ngb-dp-navigation-chevron {
    color: #f05922 !important;
}

/* max screen md */
@media (max-width: 768px) {

    /* #departure-dropdown-menu,
    #arrival-dropdown-menu {
        width: fit-content !important;
    } */

    /* #departure-dropdown-menu {
        left: 0 !important;
        bottom: 0 !important;
        transform: translate(0, 127px) !important;
    } */

    #arrival-dropdown-menu {
        transform: translate3d(0px, 47px, 0px) !important;
    }
}


.shine {
    position: relative;
    background: linear-gradient(100deg,
            rgba(236, 236, 236, 0.2) 8%,
            rgba(255, 255, 255, 0.6) 18%,
            rgba(236, 236, 236, 0.2) 33%);
    background-size: 200% 100%;
    animation: shine 2s ease-in-out infinite;
    /* overflow: hidden; */
}

@keyframes shine {
    100% {
        background-position-x: -200%;
    }
}

::ng-deep .carousel {
    position: relative;
    height: 100%;
    background-color: #f3f4f6;
}

::ng-deep .carousel-inner {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 0.5rem;
}

::ng-deep .carousel-item {
    position: relative;
    display: none;
    float: left;
    width: 100%;
    height: 100%;
    margin-right: -100%;
    transition: transform .8s ease-in-out;
    transform: translate3d(0, 0, 0);
    will-change: transform;
}

::ng-deep .carousel-item.active {
    display: block;
}

::ng-deep .carousel-control-prev,
::ng-deep .carousel-control-next {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 5%;
    padding: 0;
    color: #fff;
    text-align: center;
    background: 0 0;
    border: 0;
    opacity: 1;
    transition: opacity 0.15s ease;
}

::ng-deep .carousel-control-prev {
    left: 0;
}

::ng-deep .carousel-control-next {
    right: 0;
}

::ng-deep .carousel-control-prev:hover,
::ng-deep .carousel-control-next:hover {
    opacity: 1;
}

::ng-deep .carousel-indicators {
    position: absolute;
    right: 0;
    bottom: 1rem;
    left: 0;
    z-index: 15;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    margin: 0;
    list-style: none;
}

::ng-deep .carousel-indicators button {
    width: 25px;
    height: 2px;
    border-radius: 0;
    background-color: #606060;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-right: 8px;
    box-shadow: none;
}

::ng-deep .carousel-indicators button:last-child {
    margin-right: 0;
}

::ng-deep .carousel-indicators button:hover {
    background-color: #404040;
    transform: none;
}

::ng-deep .carousel-indicators .active {
    background-color: #000000;
    transform: none;
    box-shadow: none;
}

::ng-deep .carousel-indicators .active::after {
    content: none;
}



::ng-deep .visually-hidden {
    display: none;
}



::ng-deep .picsum-img-wrapper {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f4f6;
}

::ng-deep .picsum-img-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

::ng-deep .carousel-caption {
    position: absolute;
    right: 0;
    bottom: 1.25rem;
    left: 0;
    padding: 1rem;
    color: #fff;
    text-align: center;
}

::ng-deep .carousel-caption h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

::ng-deep .carousel-caption p {
    font-size: 1rem;
    margin-bottom: 0;
    opacity: 0.9;
}