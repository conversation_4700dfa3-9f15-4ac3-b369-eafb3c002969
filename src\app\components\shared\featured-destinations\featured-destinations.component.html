<section class=" bg-white relative overflow-hidden">
    <div class="container mx-auto px-4 !pb-12">
        <div class="flex flex-col md:flex-row md:items-end justify-between mb-12">
            <div>
                <div
                    class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200">
                    Điể<PERSON> đến hàng đầu</div>
                <h2 class="text-3xl md:text-4xl font-bold mb-2 tracking-tight text-gray-600">Đ<PERSON><PERSON><PERSON> đến phổ biến</h2>
                <p class="text-gray-600 max-w-2xl">Khám ph<PERSON> những điểm đến hấp dẫn nhất với mức gi<PERSON> <PERSON>u đãi đặc biệt</p>
            </div><a href="/destinations"
                class="hidden md:flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors mt-4 md:mt-0"><PERSON>em
                tất cả điểm đến<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chevron-right h-4 w-4 ml-1">
                    <path d="m9 18 6-6-6-6"></path>
                </svg></a>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="group">
                <div class="border text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-500 rounded-2xl bg-white h-full"
                    data-v0-t="card">
                    <div class="relative h-72 w-full overflow-hidden">
                        <img alt="Đà Nẵng" loading="lazy" decoding="async" data-nimg="fill"
                            class="object-cover transition-transform duration-700 scale-100"
                            src="\assets\img\background\service_tour.jpg"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 right-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                            Giảm 15%</div>
                    </div>
                    <div class="relative -mt-24 mx-4 bg-white rounded-xl p-6 shadow-lg z-10">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold">Đà Nẵng</h3>
                            <div class="flex items-center bg-primary-50 px-2 py-1 rounded-full"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="#FFB800" stroke="#FFB800" stroke-width="1" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-1">
                                    <polygon
                                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                    </polygon>
                                </svg><span class="font-medium text-sm text-gray-700">4.8</span></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500 text-sm mb-1">Giá chỉ từ</p>
                                <p class="text-xl font-bold text-primary-600">599.000đ</p>
                            </div><button
                                class="flex items-center space-x-2 bg-primary-100 hover:bg-primary-200 text-primary-700 px-4 py-2 rounded-full transition-colors"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plane h-4 w-4">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg><span class="font-medium">Đặt ngay</span></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="group">
                <div class="border text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-500 rounded-2xl bg-white h-full"
                    data-v0-t="card">
                    <div class="relative h-72 w-full overflow-hidden"><img alt="Phú Quốc" loading="lazy"
                            decoding="async" data-nimg="fill"
                            class="object-cover transition-transform duration-700 scale-100"
                            src="\assets\img\background\service_tour.jpg"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 right-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                            Giảm 20%</div>
                    </div>
                    <div class="relative -mt-24 mx-4 bg-white rounded-xl p-6 shadow-lg z-10">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold">Phú Quốc</h3>
                            <div class="flex items-center bg-primary-50 px-2 py-1 rounded-full"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="#FFB800" stroke="#FFB800" stroke-width="1" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-1">
                                    <polygon
                                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                    </polygon>
                                </svg><span class="font-medium text-sm text-gray-700">4.9</span></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500 text-sm mb-1">Giá chỉ từ</p>
                                <p class="text-xl font-bold text-primary-600">799.000đ</p>
                            </div><button
                                class="flex items-center space-x-2 bg-primary-100 hover:bg-primary-200 text-primary-700 px-4 py-2 rounded-full transition-colors"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plane h-4 w-4">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg><span class="font-medium">Đặt ngay</span></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="group">
                <div class="border text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-500 rounded-2xl bg-white h-full"
                    data-v0-t="card">
                    <div class="relative h-72 w-full overflow-hidden"><img alt="Nha Trang" loading="lazy"
                            decoding="async" data-nimg="fill"
                            class="object-cover transition-transform duration-700 scale-100"
                            src="\assets\img\background\service_tour.jpg"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 right-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                            Giảm 10%</div>
                    </div>
                    <div class="relative -mt-24 mx-4 bg-white rounded-xl p-6 shadow-lg z-10">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold">Nha Trang</h3>
                            <div class="flex items-center bg-primary-50 px-2 py-1 rounded-full"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="#FFB800" stroke="#FFB800" stroke-width="1" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-1">
                                    <polygon
                                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                    </polygon>
                                </svg><span class="font-medium text-sm text-gray-700">4.7</span></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500 text-sm mb-1">Giá chỉ từ</p>
                                <p class="text-xl font-bold text-primary-600">649.000đ</p>
                            </div><button
                                class="flex items-center space-x-2 bg-primary-100 hover:bg-primary-200 text-primary-700 px-4 py-2 rounded-full transition-colors"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plane h-4 w-4">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg><span class="font-medium">Đặt ngay</span></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="group">
                <div class="border text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-500 rounded-2xl bg-white h-full"
                    data-v0-t="card">
                    <div class="relative h-72 w-full overflow-hidden"><img alt="Đà Lạt" loading="lazy" decoding="async"
                            data-nimg="fill" class="object-cover transition-transform duration-700 scale-100"
                            src="\assets\img\background\service_tour.jpg"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 right-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                            Giảm 25%</div>
                    </div>
                    <div class="relative -mt-24 mx-4 bg-white rounded-xl p-6 shadow-lg z-10">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold">Đà Lạt</h3>
                            <div class="flex items-center bg-primary-50 px-2 py-1 rounded-full"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="#FFB800" stroke="#FFB800" stroke-width="1" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-1">
                                    <polygon
                                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                    </polygon>
                                </svg><span class="font-medium text-sm text-gray-700">4.9</span></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500 text-sm mb-1">Giá chỉ từ</p>
                                <p class="text-xl font-bold text-primary-600">549.000đ</p>
                            </div><button
                                class="flex items-center space-x-2 bg-primary-100 hover:bg-primary-200 text-primary-700 px-4 py-2 rounded-full transition-colors"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plane h-4 w-4">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg><span class="font-medium">Đặt ngay</span></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="group">
                <div class="border text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-500 rounded-2xl bg-white h-full"
                    data-v0-t="card">
                    <div class="relative h-72 w-full overflow-hidden"><img alt="Hội An" loading="lazy" decoding="async"
                            data-nimg="fill" class="object-cover transition-transform duration-700 scale-100"
                            src="\assets\img\background\service_tour.jpg"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 right-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                            Giảm 12%</div>
                    </div>
                    <div class="relative -mt-24 mx-4 bg-white rounded-xl p-6 shadow-lg z-10">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold">Hội An</h3>
                            <div class="flex items-center bg-primary-50 px-2 py-1 rounded-full"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="#FFB800" stroke="#FFB800" stroke-width="1" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-1">
                                    <polygon
                                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                    </polygon>
                                </svg><span class="font-medium text-sm text-gray-700">4.8</span></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500 text-sm mb-1">Giá chỉ từ</p>
                                <p class="text-xl font-bold text-primary-600">699.000đ</p>
                            </div><button
                                class="flex items-center space-x-2 bg-primary-100 hover:bg-primary-200 text-primary-700 px-4 py-2 rounded-full transition-colors"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plane h-4 w-4">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg><span class="font-medium">Đặt ngay</span></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="group">
                <div class="border text-card-foreground overflow-hidden border-none shadow-xl hover:shadow-2xl transition-all duration-500 rounded-2xl bg-white h-full"
                    data-v0-t="card">
                    <div class="relative h-72 w-full overflow-hidden"><img alt="Hạ Long" loading="lazy" decoding="async"
                            data-nimg="fill" class="object-cover transition-transform duration-700 scale-100"
                            src="\assets\img\background\service_tour.jpg"
                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div
                            class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent absolute top-4 right-4 bg-primary-500 hover:bg-primary-600 text-white border-none">
                            Giảm 18%</div>
                    </div>
                    <div class="relative -mt-24 mx-4 bg-white rounded-xl p-6 shadow-lg z-10">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold">Hạ Long</h3>
                            <div class="flex items-center bg-primary-50 px-2 py-1 rounded-full"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="#FFB800" stroke="#FFB800" stroke-width="1" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-4 w-4 mr-1">
                                    <polygon
                                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                    </polygon>
                                </svg><span class="font-medium text-sm text-gray-700">4.7</span></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500 text-sm mb-1">Giá chỉ từ</p>
                                <p class="text-xl font-bold text-primary-600">749.000đ</p>
                            </div><button
                                class="flex items-center space-x-2 bg-primary-100 hover:bg-primary-200 text-primary-700 px-4 py-2 rounded-full transition-colors"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-plane h-4 w-4">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg><span class="font-medium">Đặt ngay</span></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex md:hidden justify-center mt-8">
            <a href="/destinations"
                class="flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors">Xem tất
                cả
                điểm
                đến
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chevron-right h-4 w-4 ml-1">
                    <path d="m9 18 6-6-6-6"></path>
                </svg>
            </a>
        </div>
    </div>
    <div class="absolute -top-24 -right-24 w-64 h-64 bg-primary-50 rounded-full opacity-70 blur-3xl"></div>
    <div class="absolute -bottom-32 -left-32 w-80 h-80 bg-blue-50 rounded-full opacity-70 blur-3xl"></div>
</section>