import { HttpEvent, HttpHandlerFn, HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { from, Observable, switchMap } from 'rxjs';
import FingerprintJS from '@fingerprintjs/fingerprintjs';

export const deviceIdInterceptor: HttpInterceptorFn = (req: HttpRequest<any>, next: HttpHandlerFn): Observable<HttpEvent<any>> => {
  return from(getDeviceId()).pipe(
    switchMap(deviceId => {
      const clonedRequest = req.clone({
        setHeaders: {
          'X-Device-Id': deviceId,
          'x-api-key': 'REFxLFcGTNeQ1bNx4JuDDMNzj5IsTB07'
        }
      });
      return next(clonedRequest);
    })
  );
};

async function getDeviceId(): Promise<string> {
  const fp = await FingerprintJS.load();
  const result = await fp.get();
  return result.visitorId; // Device ID duy nhất
}