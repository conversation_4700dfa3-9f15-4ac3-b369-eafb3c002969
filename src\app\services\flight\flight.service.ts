import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

const apiUrl = environment.apiUrl;

@Injectable({
  providedIn: 'root'
})
export class FlightService {

  constructor(
    private http: HttpClient
  ) { }

  getAirports() {
    return this.http.get(`${apiUrl}/api/Airplanes`);
  }

  sentRequest(data: any): any {
    return this.http.post(`${apiUrl}/api/Flights/sent-request`, data);
  }

  // Airports
  pagingAirports(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/airport/paging', request);
  }
  getTreeAirports(): any {
    return this.http.get(`${apiUrl}/api/Flights/airport/tree`);
  }
  GetAirportByID(id: string): any {
    return this.http.get(apiUrl + `/api/Flights/airport/${id}`);
  }
  saveAirport(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/airport', request);
  }
  hideAirport(listId: string[], show: boolean): Observable<any> {
    const queryString = listId.map(id => `listId=${id}`).join('&');
    return this.http.put<any>(apiUrl + `/api/Flights/airport?show=${show}&${queryString}`, null);
  }


  // Flight request
  pagingFlightRequest(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/request/paging', request);
  }
  pagingPartnerFlightRequest(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/request/partner/paging', request);
  }
  GetFlightRequestByID(id: string): any {
    return this.http.get(apiUrl + `/api/Flights/request/${id}`);
  }
  GetFlightRequestDetailsByID(id: string): any {
    return this.http.get(apiUrl + `/api/Flights/request/admin/${id}`);
  }
  updateStatusFlightRequest(Id: string, Status: number): any {
    return this.http.put(apiUrl + `/api/Flights/request/${Id}/status/${Status}`, null);
  }

  SearchTrip(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/SearchTrip', request);
  }

  PriceAncillary(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/PriceAncillary', request);
  }

  BookTrip(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/BookTrip', request);
  }

  Retrieve(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/Retrieve', request);
  }

  FareRules(request: any): any {
    return this.http.post(apiUrl + '/api/FareRules/get-fare-rules', request);
  }

  RequestTrip(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/RequestTrip', request);
  }

  AvailableTrip(request: any): any {
    return this.http.post(apiUrl + '/api/Library/AvailableTrip', request);
  }

  RePayment(request: any): any {
    return this.http.post(apiUrl + '/api/Flights/RePayment', request);
  }


}
