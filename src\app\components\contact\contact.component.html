<div class="min-h-screen bg-white">
    <section class="relative pt-32 pb-20 overflow-hidden">
        <div class="absolute inset-0 z-0"><img alt="Contact Background" decoding="async" data-nimg="fill"
                class="object-cover" src="\assets\img\background\service_tour.jpg"
                style="position: absolute; height: 100%; width: 100%; inset: 0px;">
            <div class="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/30"></div>
        </div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-3xl mx-auto text-center">
                <div class="inline-flex items-center rounded-full border text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-primary/80 mb-4 bg-white/20 backdrop-blur-sm text-white border-white/10 py-1.5 px-4"
                    data-v0-t="badge"><PERSON><PERSON><PERSON> hệ với chúng tôi</div>
                <h1 class="text-4xl md:text-5xl font-bold mb-6 text-white drop-shadow-md">Chúng tôi luôn sẵn sàng hỗ trợ
                    bạn</h1>
                <p class="text-lg text-white/90 mb-8 drop-shadow-sm">Có câu hỏi về dịch vụ hoặc cần tư vấn cho chuyến
                    đi? Đừng ngần ngại liên hệ với chúng tôi!</p>
            </div>
        </div>
    </section>
    <section class="py-16 -mt-10 relative z-10">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
                <div
                    class="rounded-lg border text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
                    <div class="p-6 text-center">
                        <div class="h-16 w-16 rounded-2xl bg-primary-100 flex items-center justify-center mx-auto mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-map-pin h-8 w-8 text-primary-600">
                                <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                                <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Địa chỉ</h3>
                        <p class="text-gray-600 text-sm">122 Yên Phúc – Hà Đông – Hà Nội</p>
                    </div>
                </div>
                <div
                    class="rounded-lg border text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
                    <div class="p-6 text-center">
                        <div class="h-16 w-16 rounded-2xl bg-primary-100 flex items-center justify-center mx-auto mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-phone h-8 w-8 text-primary-600">
                                <path
                                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Điện thoại</h3>
                        <p class="text-gray-600 text-sm">Hotline: 0355 682 523</p>
                    </div>
                </div>
                <div
                    class="rounded-lg border text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
                    <div class="p-6 text-center">
                        <div class="h-16 w-16 rounded-2xl bg-primary-100 flex items-center justify-center mx-auto mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-mail h-8 w-8 text-primary-600">
                                <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Email</h3>
                        <p class="text-gray-600 text-sm">hvtuanneu1509&#64;gmail.com</p>
                    </div>
                </div>
                <div
                    class="rounded-lg border text-card-foreground border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
                    <div class="p-6 text-center">
                        <div class="h-16 w-16 rounded-2xl bg-primary-100 flex items-center justify-center mx-auto mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-clock h-8 w-8 text-primary-600">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">Giờ làm việc</h3>
                        <p class="text-gray-600 text-sm">T2 - CN: 7h30 - 23h30</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="pb-16  bg-gradient-to-b from-white to-primary-50">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <div class="lg:col-span-2">
                    <div class="mb-8">
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200"
                            data-v0-t="badge">Vị trí văn phòng</div>
                        <h2 class="text-3xl font-bold mb-4">Ghé thăm văn phòng của chúng tôi</h2>
                        <p class="text-gray-600">Chúng tôi có mặt tại trung tâm thành phố, thuận tiện cho việc tư vấn
                            trực tiếp và hỗ trợ khách hàng.</p>
                    </div>
                    <div class="relative h-96 rounded-2xl overflow-hidden shadow-lg">
                         <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1389.2385735637804!2d105.78933975897142!3d20.97079721312951!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135acd6efc42325%3A0x1b5942bd1fcf768f!2zMTIyIFAuIFnDqm4gUGjDumMsIFAuIFBow7pjIExhLCBIw6AgxJDDtG5nLCBIw6AgTuG7mWksIFZp4buHdCBOYW0!5e1!3m2!1svi!2s!4v1754022676210!5m2!1svi!2s"
                            width="100%" height="100%" allowfullscreen="" loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade" class="rounded-2xl"
                            style="border: 0px;"></iframe>
                    </div>
                    <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div
                            class="rounded-lg bg-card text-card-foreground shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                            <div class="p-6">
                                <div class="flex items-start space-x-4">
                                    <div
                                        class="h-12 w-12 rounded-xl bg-primary-100 flex items-center justify-center flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-map-pin h-6 w-6 text-primary-600">
                                            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                                            <circle cx="12" cy="10" r="3"></circle>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold mb-2">Địa chỉ chi tiết</h4>
                                        <p class="text-sm text-gray-600">154-156 Nguyễn Văn Cừ, Tân Lập, Buôn Ma Thuột,
                                            Đắk Lắk.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="rounded-lg bg-card text-card-foreground shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                            <div class="p-6">
                                <div class="flex items-start space-x-4">
                                    <div
                                        class="h-12 w-12 rounded-xl bg-blue-100 flex items-center justify-center flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-clock h-6 w-6 text-blue-600">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12 6 12 12 16 14"></polyline>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold mb-2">Giờ tiếp khách</h4>
                                        <p class="text-sm text-gray-600">Thứ 2 - Chủ nhật: 7h30 - 23h30</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="space-y-8">
                    <div>
                        <h3 class="text-xl font-semibold mb-6">Liên hệ nhanh</h3>
                        <div class="space-y-4">
                            <div
                                class="rounded-lg bg-card text-card-foreground shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="p-4">
                                    <a href="tel:0355682523" class="flex items-center space-x-3">
                                        <div class="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-phone h-5 w-5 text-green-600">
                                                <path
                                                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                                </path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="font-medium">Hotline 24/24</p>
                                            <p class="text-sm text-gray-600">0355 682 523</p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <div
                                class="rounded-lg bg-card text-card-foreground shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="p-4">
                                    <a href="https://zalo.me/0355682523" target="_blank"
                                        class="flex items-center space-x-3">
                                        <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-message-circle h-5 w-5 text-blue-600">
                                                <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="font-medium">Zalo</p>
                                            <p class="text-sm text-gray-600">0355 682 523</p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-6">Câu hỏi thường gặp</h3>
                        <div class="space-y-4">
                            <div
                                class="rounded-lg bg-card text-card-foreground shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="p-4">
                                    <h4 class="font-medium mb-2">Làm thế nào để đặt vé máy bay?</h4>
                                    <p class="text-sm text-gray-600">Bạn có thể đặt vé trực tiếp trên website hoặc liên
                                        hệ hotline để được hỗ trợ.</p>
                                </div>
                            </div>
                            <div
                                class="rounded-lg bg-card text-card-foreground shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="p-4">
                                    <h4 class="font-medium mb-2">Chính sách hoàn hủy vé như thế nào?</h4>
                                    <p class="text-sm text-gray-600">Chính sách hoàn hủy phụ thuộc vào loại vé và thời
                                        gian hủy. Vui lòng xem chi tiết tại trang chính sách.</p>
                                </div>
                            </div>
                            <div
                                class="rounded-lg bg-card text-card-foreground shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="p-4">
                                    <h4 class="font-medium mb-2">Có thể thay đổi thông tin vé không?</h4>
                                    <p class="text-sm text-gray-600">Tùy thuộc vào loại vé và quy định của hãng hàng
                                        không. Liên hệ để được tư vấn cụ thể.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200"
                    data-v0-t="badge">Kênh hỗ trợ</div>
                <h2 class="text-3xl font-bold mb-4">Nhiều cách để liên hệ</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Chúng tôi cung cấp nhiều kênh hỗ trợ khác nhau để bạn có thể
                    liên hệ một cách thuận tiện nhất</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div
                    class="rounded-lg border border-gray-100  bg-white   shadow-lg hover:shadow-xl transition-all duration-300 text-center">
                    <div class="p-8">
                        <div class="h-16 w-16 rounded-2xl bg-blue-100 flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-facebook h-8 w-8 text-blue-600">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Facebook</h3>
                        <p class="text-gray-600 mb-6">Kết nối với chúng tôi qua Facebook để nhận thông tin và hỗ trợ
                            nhanh chóng</p>
                        <a href="https://www.facebook.com/tuan.havan.5458" target="_blank"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-facebook h-4 w-4 mr-2">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                            </svg>
                            Kết nối Facebook
                        </a>
                    </div>
                </div>
                <div
                    class="rounded-lg border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 text-center">
                    <div class="p-8">
                        <div class="h-16 w-16 rounded-2xl bg-green-100 flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-phone h-8 w-8 text-green-600">
                                <path
                                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Hotline 24/24</h3>
                        <p class="text-gray-600 mb-6">Gọi điện trực tiếp để được tư vấn và hỗ trợ bởi chuyên viên kinh
                            nghiệm</p>
                        <a href="tel:0355682523"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-full"><svg
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-phone h-4 w-4 mr-2">
                                <path
                                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                </path>
                            </svg>Gọi ngay
                        </a>
                    </div>
                </div>
                <div
                    class="rounded-lg border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 text-center">
                    <div class="p-8">
                        <div
                            class="h-16 w-16 rounded-2xl bg-blue-100 flex items-center justify-center mx-auto mb-6 overflow-hidden">
                            <svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 460.1 436.6" width="2500"
                                height="2372">
                                <style>
                                    .st0 {
                                        fill: #fdfefe
                                    }

                                    .st1 {
                                        fill: #0180c7
                                    }

                                    .st2 {
                                        fill: #0172b1
                                    }

                                    .st3 {
                                        fill: none;
                                        stroke: #0180c7;
                                        stroke-width: 2;
                                        stroke-miterlimit: 10
                                    }
                                </style>
                                <title>logo-zalo-vector</title>
                                <path class="st0"
                                    d="M82.6 380.9c-1.8-.8-3.1-1.7-1-3.5 1.3-1 2.7-1.9 4.1-2.8 13.1-8.5 25.4-17.8 33.5-31.5 6.8-11.4 5.7-18.1-2.8-26.5C69 269.2 48.2 212.5 58.6 145.5 64.5 107.7 81.8 75 107 46.6c15.2-17.2 33.3-31.1 53.1-42.7 1.2-.7 2.9-.9 3.1-2.7-.4-1-1.1-.7-1.7-.7-33.7 0-67.4-.7-101 .2C28.3 1.7.5 26.6.6 62.3c.2 104.3 0 208.6 0 313 0 32.4 24.7 59.5 57 60.7 27.3 1.1 54.6.2 82 .1 2 .1 4 .2 6 .2H290c36 0 72 .2 108 0 33.4 0 60.5-27 60.5-60.3v-.6-58.5c0-1.4.5-2.9-.4-4.4-1.8.1-2.5 1.6-3.5 2.6-19.4 19.5-42.3 35.2-67.4 46.3-61.5 27.1-124.1 29-187.6 7.2-5.5-2-11.5-2.2-17.2-.8-8.4 2.1-16.7 4.6-25 7.1-24.4 7.6-49.3 11-74.8 6zm72.5-168.5c1.7-2.2 2.6-3.5 3.6-4.8 13.1-16.6 26.2-33.2 39.3-49.9 3.8-4.8 7.6-9.7 10-15.5 2.8-6.6-.2-12.8-7-15.2-3-.9-6.2-1.3-9.4-1.1-17.8-.1-35.7-.1-53.5 0-2.5 0-5 .3-7.4.9-5.6 1.4-9 7.1-7.6 12.8 1 3.8 4 6.8 7.8 7.7 2.4.6 4.9.9 7.4.8 10.8.1 21.7 0 32.5.1 1.2 0 2.7-.8 3.6 1-.9 1.2-1.8 2.4-2.7 3.5-15.5 19.6-30.9 39.3-46.4 58.9-3.8 4.9-5.8 10.3-3 16.3s8.5 7.1 14.3 7.5c4.6.3 9.3.1 14 .1 16.2 0 32.3.1 48.5-.1 8.6-.1 13.2-5.3 12.3-13.3-.7-6.3-5-9.6-13-9.7-14.1-.1-28.2 0-43.3 0zm116-52.6c-12.5-10.9-26.3-11.6-39.8-3.6-16.4 9.6-22.4 25.3-20.4 43.5 1.9 17 9.3 30.9 27.1 36.6 11.1 3.6 21.4 2.3 30.5-5.1 2.4-1.9 3.1-1.5 4.8.6 3.3 4.2 9 5.8 14 3.9 5-1.5 8.3-6.1 8.3-11.3.1-20 .2-40 0-60-.1-8-7.6-13.1-15.4-11.5-4.3.9-6.7 3.8-9.1 6.9zm69.3 37.1c-.4 25 20.3 43.9 46.3 41.3 23.9-2.4 39.4-20.3 38.6-45.6-.8-25-19.4-42.1-44.9-41.3-23.9.7-40.8 19.9-40 45.6zm-8.8-19.9c0-15.7.1-31.3 0-47 0-8-5.1-13-12.7-12.9-7.4.1-12.3 5.1-12.4 12.8-.1 4.7 0 9.3 0 14v79.5c0 6.2 3.8 11.6 8.8 12.9 6.9 1.9 14-2.2 15.8-9.1.3-1.2.5-2.4.4-3.7.2-15.5.1-31 .1-46.5z" />
                                <path class="st1"
                                    d="M139.5 436.2c-27.3 0-54.7.9-82-.1-32.3-1.3-57-28.4-57-60.7 0-104.3.2-208.6 0-313C.5 26.7 28.4 1.8 60.5.9c33.6-.9 67.3-.2 101-.2.6 0 1.4-.3 1.7.7-.2 1.8-2 2-3.1 2.7-19.8 11.6-37.9 25.5-53.1 42.7-25.1 28.4-42.5 61-48.4 98.9-10.4 66.9 10.5 123.7 57.8 171.1 8.4 8.5 9.5 15.1 2.8 26.5-8.1 13.7-20.4 23-33.5 31.5-1.4.8-2.8 1.8-4.2 2.7-2.1 1.8-.8 2.7 1 3.5.4.9.9 1.7 1.5 2.5 11.5 10.2 22.4 21.1 33.7 31.5 5.3 4.9 10.6 10 15.7 15.1 2.1 1.9 5.6 2.5 6.1 6.1z" />
                                <path class="st2"
                                    d="M139.5 436.2c-.5-3.5-4-4.1-6.1-6.2-5.1-5.2-10.4-10.2-15.7-15.1-11.3-10.4-22.2-21.3-33.7-31.5-.6-.8-1.1-1.6-1.5-2.5 25.5 5 50.4 1.6 74.9-5.9 8.3-2.5 16.6-5 25-7.1 5.7-1.5 11.7-1.2 17.2.8 63.4 21.8 126 19.8 187.6-7.2 25.1-11.1 48-26.7 67.4-46.2 1-1 1.7-2.5 3.5-2.6.9 1.4.4 2.9.4 4.4v58.5c0 33.4-26.6 60.6-60 60.9h-.5c-36 .2-72 0-108 0H145.5c-2-.2-4-.3-6-.3z" />
                                <path class="st1"
                                    d="M155.1 212.4c15.1 0 29.3-.1 43.4 0 7.9.1 12.2 3.4 13 9.7.9 7.9-3.7 13.2-12.3 13.3-16.2.2-32.3.1-48.5.1-4.7 0-9.3.2-14-.1-5.8-.3-11.5-1.5-14.3-7.5s-.8-11.4 3-16.3c15.4-19.6 30.9-39.3 46.4-58.9.9-1.2 1.8-2.4 2.7-3.5-1-1.7-2.4-.9-3.6-1-10.8-.1-21.7 0-32.5-.1-2.5 0-5-.3-7.4-.8-5.7-1.3-9.2-7-7.9-12.6.9-3.8 3.9-6.9 7.7-7.8 2.4-.6 4.9-.9 7.4-.9 17.8-.1 35.7-.1 53.5 0 3.2-.1 6.3.3 9.4 1.1 6.8 2.3 9.7 8.6 7 15.2-2.4 5.7-6.2 10.6-10 15.5-13.1 16.7-26.2 33.3-39.3 49.8-1.1 1.3-2.1 2.6-3.7 4.8z" />
                                <path class="st1"
                                    d="M271.1 159.8c2.4-3.1 4.9-6 9-6.8 7.9-1.6 15.3 3.5 15.4 11.5.3 20 .2 40 0 60 0 5.2-3.4 9.8-8.3 11.3-5 1.9-10.7.4-14-3.9-1.7-2.1-2.4-2.5-4.8-.6-9.1 7.4-19.4 8.7-30.5 5.1-17.8-5.8-25.1-19.7-27.1-36.6-2.1-18.3 4-33.9 20.4-43.5 13.6-8.1 27.4-7.4 39.9 3.5zm-35.4 36.5c.2 4.4 1.6 8.6 4.2 12.1 5.4 7.2 15.7 8.7 23 3.3 1.2-.9 2.3-2 3.3-3.3 5.6-7.6 5.6-20.1 0-27.7-2.8-3.9-7.2-6.2-11.9-6.3-11-.7-18.7 7.8-18.6 21.9zM340.4 196.9c-.8-25.7 16.1-44.9 40.1-45.6 25.5-.8 44.1 16.3 44.9 41.3.8 25.3-14.7 43.2-38.6 45.6-26.1 2.6-46.8-16.3-46.4-41.3zm25.1-2.4c-.2 5 1.3 9.9 4.3 14 5.5 7.2 15.8 8.6 23 3 1.1-.8 2-1.8 2.9-2.8 5.8-7.6 5.8-20.4.1-28-2.8-3.8-7.2-6.2-11.9-6.3-10.8-.6-18.4 7.6-18.4 20.1zM331.6 177c0 15.5.1 31 0 46.5.1 7.1-5.5 13-12.6 13.2-1.2 0-2.5-.1-3.7-.4-5-1.3-8.8-6.6-8.8-12.9v-79.5c0-4.7-.1-9.3 0-14 .1-7.7 5-12.7 12.4-12.7 7.6-.1 12.7 4.9 12.7 12.9.1 15.6 0 31.3 0 46.9z" />
                                <path class="st0"
                                    d="M235.7 196.3c-.1-14.1 7.6-22.6 18.5-22 4.7.2 9.1 2.5 11.9 6.4 5.6 7.5 5.6 20.1 0 27.7-5.4 7.2-15.7 8.7-23 3.3-1.2-.9-2.3-2-3.3-3.3-2.5-3.5-3.9-7.7-4.1-12.1zM365.5 194.5c0-12.4 7.6-20.7 18.4-20.1 4.7.1 9.1 2.5 11.9 6.3 5.7 7.6 5.7 20.5-.1 28-5.6 7.1-16 8.3-23.1 2.7-1.1-.8-2-1.8-2.8-2.9-3-4.1-4.4-9-4.3-14z" />
                                <path class="st3"
                                    d="M66 1h328.1c35.9 0 65 29.1 65 65v303c0 35.9-29.1 65-65 65H66c-35.9 0-65-29.1-65-65V66C1 30.1 30.1 1 66 1z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Zalo</h3>
                        <p class="text-gray-600 mb-6">Liên hệ qua Zalo để được tư vấn và hỗ trợ nhanh chóng</p>
                        <a href="https://zalo.me/0355682523" target="_blank"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-message-circle h-4 w-4 mr-2">
                                <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                            </svg>
                            Chat Zalo
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Thông tin thanh toán -->
    <!-- <section class="py-16 bg-gray-50" id="banks-infor">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200"
                    data-v0-t="badge">Thanh toán</div>
                <h2 class="text-3xl font-bold mb-4">Thông tin chuyển khoản</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Quý khách có thể chuyển khoản qua các tài khoản ngân hàng sau
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                @for (bankInfo of banksInfoModel.banksInfo; track $index) {
                <div
                    class="rounded-lg border bg-white text-card-foreground shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden">
                    <div class="absolute w-fit h-fit z-0 top-0 right-0 opacity-60">
                        <img [src]="bankInfo.logo" [alt]="bankInfo.bankName" class="md:h-64 h-44 w-auto  ">
                    </div>
                    <div class="p-6 relative z-10 ">
                        <div class="flex items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">{{bankInfo.bankName}}</h3>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="text-sm text-gray-500">Chủ tài khoản</p>
                                <p class="font-medium text-gray-900">{{bankInfo.accountHolder}}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Số tài khoản</p>
                                <p class="font-medium text-gray-900">{{bankInfo.accountNumber}}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Chi nhánh</p>
                                <p class="font-medium text-gray-900">{{bankInfo.branch}}</p>
                            </div>
                        </div>
                    </div>
                </div>
                }
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                <div class="p-6 bg-white rounded-lg border shadow-sm flex items-start space-x-4">
                    <div class="h-12 w-12 rounded-xl bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="2" class="text-blue-600">
                            <rect x="2" y="5" width="20" height="14" rx="2" />
                            <path d="M2 10h20" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-2">Thanh toán bằng thẻ tín dụng</h3>
                        <p class="text-gray-600">Chúng tôi hỗ trợ thanh toán bằng thẻ tín dụng (Visa, MasterCard, v.v.).
                            Quý khách vui lòng liên hệ trực tiếp với chúng tôi để được hướng dẫn và xác nhận phương thức
                            thanh toán này.</p>
                    </div>
                </div>
                <div class="p-6 bg-white rounded-lg border shadow-sm flex items-start space-x-4">
                    <div class="h-12 w-12 rounded-xl bg-green-100 flex items-center justify-center flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="2" class="text-green-600">
                            <rect x="2" y="7" width="20" height="10" rx="2" />
                            <circle cx="12" cy="12" r="3" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-2">Thanh toán bằng tiền mặt</h3>
                        <p class="text-gray-600">Quý khách có thể thanh toán trực tiếp bằng tiền mặt tại phòng vé:<br>
                            154-156 Nguyễn Văn Cừ, Tân Lập, Buôn Ma Thuột, Đắk Lắk.</p>
                    </div>
                </div>
            </div>

            @if (banksInfoModel.note) {
            <div class="mt-8 p-6 bg-white rounded-lg border shadow-sm">
                <h3 class="text-lg font-semibold mb-3">Lưu ý khi chuyển khoản</h3>
                <p class="text-gray-600">{{banksInfoModel.note}}</p>
            </div>
            }
        </div>
    </section> -->
</div>