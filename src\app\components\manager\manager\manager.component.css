#dropdownBottom {
    transform: translate3d(-132.6px, 37.6px, 0px) !important;
}

.container-parent {
    width: -webkit-fill-available;
    margin-right: 0.5rem;
}

.container-parent.height-full {
    height: -webkit-fill-available;
}

@media screen and (max-width: 640px) {
    .container-parent.height-full {
        height: calc(100% - 5rem);
    }
}

aside span {
    text-wrap: normal;
}

#default-sidebar.scrollable {
    width: 4.2rem !important;
}

/* #default-sidebar.scrollable span {
    display: none;
  } */

#default-sidebar.scrollable .sidebar {
    width: 4.2rem !important;
}

.container-parent.scaleX {
    margin-left: 6rem !important;
}

.sidebar .active {
    background: #ea580c;
    color: #fff;
}

.sidebar .active svg {
    fill: white !important;
    color: white !important;
}

.sidebar button .active span svg {
    fill: white !important;
    color: white !important;
}

.menu-item.active svg {
    fill: white !important;
}

.collapse {
    visibility: visible !important;
}