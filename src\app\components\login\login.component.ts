import { CommonModule, DOCUMENT } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth/auth.service';
import { LoaderService } from '../../services/loader/loader.service';
import { Token } from '../../enum/token';

@Component({
  selector: 'app-login',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent implements OnInit {
  showPassword = false;
  loginForm?: FormGroup;
  formSubmitted: boolean = false;
  ErrorMessages: any = null;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    public loaderService: LoaderService,

    private auth: AuthService,
    @Inject(DOCUMENT) private document: Document,
  ) { }

  ngOnInit(): void {
    this.valiator();
    this.loaderService.isLoading.next(false);

    this.autoLogin();
  }


  valiator() {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
      remember: [false]
    });
  }

  get fm(): any {
    return this.loginForm?.controls;
  }

  onSubmitLoginForm() {
    this.formSubmitted = true;
    if (this.loginForm?.invalid) {
      return;
    }

    this.loaderService.isLoading.next(true);
    this.auth.login(this.loginForm?.value).subscribe({
      next: (res) => {
        console.log(res);
        if (res.isSuccessed) {
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
          if (isMobile || res.resultObj?.is_mobile) {
            localStorage.setItem(Token.ACCESS_TOKEN, res.resultObj.access_token);
            localStorage.setItem(Token.REFRESH_TOKEN, res.resultObj.refresh_token);
            localStorage.setItem(Token.CONNECTION_ID, res.resultObj.connection_id);
          }
          // this.auth.saveTokenWhenLogin(res.resultObj);
          window.location.href = res.resultObj.redirectUrl;
        } else {
          this.ErrorMessages = res.message;
          this.loaderService.isLoading.next(false);
        }
      },
      error: (err) => {
        console.error(err);
        this.ErrorMessages = "An error occurred while processing your request, please try again later.";
        this.loaderService.isLoading.next(false);
      }
    });
  }

  autoLogin() {
    this.loaderService.isLoading.next(true);
    this.auth.refreshToken().subscribe({
      next: (res) => {
        if (res.isSuccessed) {
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
          if (isMobile || res.resultObj?.is_mobile) {
            localStorage.setItem(Token.ACCESS_TOKEN, res.resultObj.access_token);
            localStorage.setItem(Token.REFRESH_TOKEN, res.resultObj.refresh_token);
            localStorage.setItem(Token.CONNECTION_ID, res.resultObj.connection_id);
          }
          window.location.href = res.resultObj.redirectUrl;
        } else {
          this.loaderService.isLoading.next(false);
        }
      },
      error: (err) => {
        console.error(err);
        this.loaderService.isLoading.next(false);
      }
    });
  }

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }
}
