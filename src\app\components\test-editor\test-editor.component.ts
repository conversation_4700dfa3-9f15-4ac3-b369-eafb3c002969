import { Component, NgModule, ViewEncapsulation } from '@angular/core';
import { TextEditorComponent } from '../ui-common/text-editor/text-editor.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { EditorViewerComponent } from '../ui-common/editor-viewer/editor-viewer.component';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-test-editor',
  imports: [
    CommonModule,
    FormsModule,
    TextEditorComponent,
    EditorViewerComponent
  ],
  templateUrl: './test-editor.component.html',
  styleUrl: './test-editor.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class TestEditorComponent {

  editorContent: string = `<p _ngcontent-ng-c958676834="" class="mb-2" style="text-align: center; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ;"><span style="color: rgb(233, 116, 53); font-weight: bold; font-family: Roboto;"><span style="font-size: 36px;">Ngọc Mai Travel</span></span></p><p _ngcontent-ng-c958676834="" class="mb-2" style="text-align: center; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; position: relative;"><img src="data:image/png;base64,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" draggable="true" class="" style="resize: none; display: block; margin-left: auto; margin-right: auto; float: none;" width="348" height="91"><br></p><h2 style="text-align: left; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; position: relative;">1. Giới thiệu</h2><p _ngcontent-ng-c958676834="" class="mb-2" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify; text-indent: 2em;">Được thành lập vào ngày 20 tháng 4 năm 2016,&nbsp;<span _ngcontent-ng-c958676834="" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; font-weight: bolder;">Công ty TNHH Thương mại Dịch vụ Du lịch Ngọc Mai</span><span _ngcontent-ng-c958676834="" class="text-primary-600 font-extrabold" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; --tw-text-opacity: 1; color: rgb(234, 88, 12);">&nbsp;(Ngọc Mai Travel)&nbsp;</span>đã phát triển thành một trong những công ty du lịch hàng đầu Việt Nam sau chín năm nỗ lực và phát triển.</p><p _ngcontent-ng-c958676834="" class="mb-2" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify; text-indent: 2em;">Chúng tôi tự hào là đại lý thuộc top đầu của 4 hãng hàng không nội địa Việt Nam và hợp tác với hơn 200 hãng hàng không quốc tế trên toàn thế giới.</p><p _ngcontent-ng-c958676834="" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify; text-indent: 2em;">Với đội ngũ chuyên nghiệp, trẻ trung, nhiệt huyết và tận tâm, chúng tôi luôn sẵn sàng phục vụ các doanh nghiệp, đại lý và công ty du lịch trên khắp cả nước.</p><p _ngcontent-ng-c958676834="" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify;"><br></p><h2 style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify;">2. Nội dung</h2><div><div class="table-total-resize-handle" title="Resize toàn bảng" style="position: absolute; width: 14px; height: 14px; right: -7px; bottom: -7px; background: rgb(49, 130, 206); border: 2px solid rgb(255, 255, 255); border-radius: 4px; cursor: nwse-resize; z-index: 200;"></div><div style="overflow-x: auto; width: 100%;"> <table style="border-width: 1px; border-color: rgb(204, 204, 204); width: 845px; min-width: 400px; position: relative; height: 101px; border-collapse: collapse; table-layout: fixed; margin-left: auto; margin-right: auto;"> <tbody><tr><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 74px; min-height: 24px; position: relative; width: 74px;"><b>&nbsp;STT</b></td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 253px; min-height: 24px; position: relative; width: 253px;"><b>&nbsp;Họ và tên</b><div style="text-align: center; width: 100%; display: block;"></div><div style="text-align: center; width: 100%; display: block;"></div><div style="text-align: center; width: 100%; display: block;"></div></td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 80px; min-height: 24px; position: relative;"><b>&nbsp;Ngày sinh</b></td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 80px; min-height: 24px; position: relative;"><b>&nbsp;Số điện thoại</b></td></tr><tr><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 74px; min-height: 24px; width: 74px;">&nbsp;1.</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 253px; min-height: 24px; width: 253px;">&nbsp;Đặng Quốc Vũ</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 80px; min-height: 24px;">&nbsp;22/10/2001</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 80px; min-height: 24px; position: relative;">&nbsp;0395102127</td></tr><tr><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 74px; min-height: 24px; width: 74px;">&nbsp;2.</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 253px; min-height: 24px; width: 253px;">&nbsp;</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 80px; min-height: 24px;">&nbsp;</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 80px; min-height: 24px; position: relative;">&nbsp;</td></tr> </tbody></table> </div></div><div><br></div><p _ngcontent-ng-c958676834="" class="mb-2" style="text-align: left; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; position: relative;"><br></p> 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" draggable="true" class="" style="resize: none; display: block; margin-left: auto; margin-right: auto; float: none;" width="348" height="91"><br></p><h2 style="text-align: left; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; position: relative;">1. Giới thiệu</h2><p _ngcontent-ng-c958676834="" class="mb-2" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify; text-indent: 2em;">Được thành lập vào ngày 20 tháng 4 năm 2016,&nbsp;<span _ngcontent-ng-c958676834="" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; font-weight: bolder;">Công ty TNHH Thương mại Dịch vụ Du lịch Ngọc Mai</span><span _ngcontent-ng-c958676834="" class="text-primary-600 font-extrabold" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; --tw-text-opacity: 1; color: rgb(234, 88, 12);">&nbsp;(Ngọc Mai Travel)&nbsp;</span>đã phát triển thành một trong những công ty du lịch hàng đầu Việt Nam sau chín năm nỗ lực và phát triển.</p><p _ngcontent-ng-c958676834="" class="mb-2" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify; text-indent: 2em;">Chúng tôi tự hào là đại lý thuộc top đầu của 4 hãng hàng không nội địa Việt Nam và hợp tác với hơn 200 hãng hàng không quốc tế trên toàn thế giới.</p><p _ngcontent-ng-c958676834="" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify; text-indent: 2em;">Với đội ngũ chuyên nghiệp, trẻ trung, nhiệt huyết và tận tâm, chúng tôi luôn sẵn sàng phục vụ các doanh nghiệp, đại lý và công ty du lịch trên khắp cả nước.</p><p _ngcontent-ng-c958676834="" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify;"><br></p><h2 style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify;">2. Nội dung</h2><div><div class="table-total-resize-handle" title="Resize toàn bảng" style="position: absolute; width: 14px; height: 14px; right: -7px; bottom: -7px; background: rgb(49, 130, 206); border: 2px solid rgb(255, 255, 255); border-radius: 4px; cursor: nwse-resize; z-index: 200;"></div><table style="border-width: 1px; border-color: rgb(204, 204, 204); width: 1130px; position: relative; height: 141px; border-collapse: collapse; table-layout: fixed; margin-left: auto; margin-right: auto;"><tbody><tr><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 87px; min-height: 24px; position: relative; width: 87px;"><b>&nbsp;STT</b><div class="table-col-resize-handle" data-col-idx="0" style="position: absolute; width: 6px; top: 0px; right: -3px; bottom: 0px; cursor: col-resize; z-index: 100; background: rgba(49, 130, 206, 0.15);"></div></td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px; position: relative;"><b>&nbsp;Họ và Tên</b><div class="table-col-resize-handle" data-col-idx="1" style="position: absolute; width: 6px; top: 0px; right: -3px; bottom: 0px; cursor: col-resize; z-index: 100; background: rgba(49, 130, 206, 0.15);"></div></td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px; position: relative;"><b>&nbsp;Ngày sinh</b><div class="table-col-resize-handle" data-col-idx="2" style="position: absolute; width: 6px; top: 0px; right: -3px; bottom: 0px; cursor: col-resize; z-index: 100; background: rgba(49, 130, 206, 0.15);"></div></td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px; position: relative;"><b>&nbsp;Số điện thoại<div style="text-align: center; width: 100%; display: block;"></div></b><div class="table-col-resize-handle" data-col-idx="3" style="position: absolute; width: 6px; top: 0px; right: -3px; bottom: 0px; cursor: col-resize; z-index: 100; background: rgba(49, 130, 206, 0.15);"></div><div class="table-row-resize-handle" data-row-idx="0" style="position: absolute; height: 6px; left: 0px; right: 0px; bottom: -3px; cursor: row-resize; z-index: 100; background: rgba(49, 130, 206, 0.15);"></div></td></tr><tr><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 87px; min-height: 24px; width: 87px;">&nbsp;1</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px;">&nbsp;Đặng Quốc Vũ</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px;">&nbsp;22/10/2001</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px; position: relative;">&nbsp;0395102127<div class="table-row-resize-handle" data-row-idx="1" style="position: absolute; height: 6px; left: 0px; right: 0px; bottom: -3px; cursor: row-resize; z-index: 100; background: rgba(49, 130, 206, 0.15);"></div></td></tr><tr><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 87px; min-height: 24px; width: 87px;">&nbsp;2</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px;">&nbsp;</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px;">&nbsp;</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px; position: relative;">&nbsp;</td></tr><tr><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 87px; min-height: 24px; width: 87px;">&nbsp;3</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px;">&nbsp;</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px;">&nbsp;</td><td style="border-width: 1px; border-color: rgb(204, 204, 204); padding: 4px 8px; min-width: 40px; min-height: 24px; position: relative;">&nbsp;</td></tr></tbody></table><br></div><div><br></div> 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" draggable="true" class="" style="resize: none; display: block; margin-left: auto; margin-right: auto; float: none;" width="348" height="91"><br></p><p _ngcontent-ng-c958676834="" class="mb-2" style="text-align: left; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; position: relative;">Giới thiệu</p><p _ngcontent-ng-c958676834="" class="mb-2" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify;">Được thành lập vào ngày 20 tháng 4 năm 2016,&nbsp;<span _ngcontent-ng-c958676834="" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; font-weight: bolder;">Công ty TNHH Thương mại Dịch vụ Du lịch Ngọc Mai</span><span _ngcontent-ng-c958676834="" class="text-primary-600 font-extrabold" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; --tw-text-opacity: 1; color: rgb(234, 88, 12);">&nbsp;(Ngọc Mai Travel)&nbsp;</span>đã phát triển thành một trong những công ty du lịch hàng đầu Việt Nam sau chín năm nỗ lực và phát triển.</p><p _ngcontent-ng-c958676834="" class="mb-2" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify;">Chúng tôi tự hào là đại lý thuộc top đầu của 4 hãng hàng không nội địa Việt Nam và hợp tác với hơn 200 hãng hàng không quốc tế trên toàn thế giới.</p><p _ngcontent-ng-c958676834="" style="--tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; color: rgb(0, 0, 59); font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; text-align: justify;">Với đội ngũ chuyên nghiệp, trẻ trung, nhiệt huyết và tận tâm, chúng tôi luôn sẵn sàng phục vụ các doanh nghiệp, đại lý và công ty du lịch trên khắp cả nước.</p>`;
  value: string = '';
  onValueChange(event: string) {
    this.value = event;
  }
}
