import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject } from '@angular/core';
import { FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { Ng<PERSON><PERSON><PERSON><PERSON><PERSON>, NgbDate, NgbDate<PERSON><PERSON>erF<PERSON>atter, NgbDatepickerModule, NgbDropdownModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { formatCurrency, formatDate, formatDateTo_ddMMyyyy, range } from '../../../../common/function.common';

import { LoaderService } from '../../../../services/loader/loader.service';
import { SearchModel, SearchStateService } from '../../../../services/search-state/search-state.service';
import { NewsService } from '../../../../services/news/news.service';
import { CategoryNewsService } from '../../../../services/category-news/category-news.service';

interface FilterOption {
  key: string | number | null;
  value: string;
  selected: boolean;
}

interface DataTable {
  allRecords: number;
  from: number;
  to: number;
  pageCount: number;
  pageSize: number;
  pageIndex: number;
  totalRecords: number;
  items: any[];
}
@Component({
  selector: 'app-manager-news',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterLink,
    RouterLinkActive,
    NgbDropdownModule,
    NgbDatepickerModule,
  ],
  templateUrl: './manager-news.component.html',
  styleUrl: './manager-news.component.css'
})


export class ManagerNewsComponent implements OnInit {
  // Services
  private readonly newsService = inject(NewsService);
  private readonly categoryNewsService = inject(CategoryNewsService);
  private readonly toastService = inject(ToastrService);
  private readonly router = inject(Router);
  private readonly searchStateService = inject(SearchStateService<SearchModel>);
  public readonly loaderService = inject(LoaderService);


  // Search and pagination
  searchModel: any = {
    PageIndex: 1,
    PageSize: 10,
    SortColumn: '',
    SortOrder: 'asc',
    Type: '',
    Keyword: '',
    FilterStatus: null
  };

  dataTable: DataTable = {
    allRecords: 0,
    from: 0,
    to: 0,
    pageCount: 0,
    pageSize: 0,
    pageIndex: 0,
    totalRecords: 0,
    items: []
  };
  categoryNews: any[] = [];
  startIndex = 1;
  finishIndex = 1;


  async ngOnInit(): Promise<void> {
    await this.getCategoryNews();
    const savedState = this.searchStateService.getSearchState();
    if (savedState) {
      this.searchModel = savedState;
    }
    await this.getPaging();
  }


  // Pagination methods
  async changePageSize(event: Event): Promise<void> {
    const value = (event.target as HTMLSelectElement).value;
    this.searchModel.PageSize = Number(value);
    await this.getPaging();
  }

  async getUrl(page: number): Promise<void> {
    if (page < 1 || page > this.dataTable.pageCount || page === this.searchModel.PageIndex) {
      return;
    }
    this.searchModel.PageIndex = page;
    await this.getPaging();
  }

  // Data fetching methods
  async getPaging(): Promise<void> {
    try {
      const res = await this.newsService.getPartnerNewsPaging(this.searchModel).toPromise();
      if (res.isSuccessed) {
        this.dataTable = res.resultObj;
        this.updatePageIndices();
      }
    } catch (error) {
      this.toastService.error('Có lỗi xảy ra khi tải dữ liệu');
    }
  }

  async getCategoryNews(): Promise<void> {
    try {
      const res = await this.categoryNewsService.getPartnerItems().toPromise();
      if (res.isSuccessed) {
        this.categoryNews = res.resultObj;
      }
    } catch (error) {
      this.toastService.error('Có lỗi xảy ra khi tải dữ liệu');
    }
  }

  // Search and filter methods
  async onSubmitSearch(): Promise<void> {
    this.searchModel.PageIndex = 1;
    this.searchStateService.setSearchState(this.searchModel);
    await this.getPaging();
  }

  private updatePageIndices(): void {
    if (this.dataTable.pageCount <= 5) {
      this.startIndex = 1;
      this.finishIndex = this.dataTable.pageCount;
    } else if (this.dataTable.pageIndex <= 3) {
      this.startIndex = 1;
      this.finishIndex = 5;
    } else if (this.dataTable.pageIndex + 2 >= this.dataTable.pageCount) {
      this.startIndex = this.dataTable.pageCount - 4;
      this.finishIndex = this.dataTable.pageCount;
    } else {
      this.startIndex = this.dataTable.pageIndex - 2;
      this.finishIndex = this.dataTable.pageIndex + 2;
    }
  }


  range(start: number, end: number): number[] {
    return range(start, end);
  }


  handleDoubleClick(item: any): void {
    this.searchStateService.setSearchState(this.searchModel);
    this.router.navigate(['/manager/news', item.id]);
  }

  async sortTable(column: string): Promise<void> {
    if (this.searchModel.SortColumn === column) {
      this.searchModel.SortOrder = this.searchModel.SortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.searchModel.SortColumn = column;
      this.searchModel.SortOrder = 'asc';
    }
    await this.getPaging();
  }

}