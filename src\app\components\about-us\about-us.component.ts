import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-about-us',
  imports: [
    CommonModule,
    RouterLink
  ],
  templateUrl: './about-us.component.html',
  styleUrl: './about-us.component.css'
})
export class AboutUsComponent {
  activeTab: string = 'values'; // Default active tab

  setActiveTab(tab: string) {
    this.activeTab = tab;
  }
}
