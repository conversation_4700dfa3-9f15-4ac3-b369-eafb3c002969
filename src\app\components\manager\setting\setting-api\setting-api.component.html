<div class="p-6 h-full overflow-y-auto">
    <div class="space-y-6">
        <div class="flex justify-between items-center">
            <h2 class="text-2xl font-bold">Cấu hình API</h2>

        </div>
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm ">
            <div class="flex flex-col space-y-1.5 p-6 bg-gray-50 border-b">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-2xl font-semibold leading-none tracking-tight">API Key</h3>
                        <p class="text-sm text-muted-foreground">Tạo ngày {{apiSettings?.timeCreate}}</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-500">Trạng thái:</span>
                        <div class="flex items-center gap-2">
                            <label class="inline-flex items-center me-3 cursor-pointer">
                                <input type="checkbox" value="" class="sr-only peer"
                                    [checked]="apiSettings?.isActive || false" (change)="onToggleApiStatus($event)">
                                <div
                                    class="relative w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-500">
                                </div>
                            </label>
                            @if(apiSettings?.isActive) {
                            <div
                                class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-green-100 text-green-800 hover:bg-green-100">
                                Đang hoạt động</div>
                            } @else {
                            <div
                                class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-red-100 text-red-800 hover:bg-red-100">
                                Ngừng hoạt động</div>
                            }

                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6 pt-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">API
                            Key</label>
                        <div class="flex">
                            <input
                                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-mono text-sm rounded-r-none"
                                readonly="" value="{{apiKeyHide}}">
                            <button (click)="copyToClipboard(apiSettings?.apiKey)"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10 rounded-l-none border-l-0">

                                <ng-container *ngIf="!isCopied; else copiedIcon">
                                    <!-- SVG Copy Icon -->
                                    <svg xmlns="http://www.w3.org/2000/svg" class="lucide lucide-copy h-4 w-4"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect>
                                        <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path>
                                    </svg>
                                </ng-container>
                                <ng-template #copiedIcon>
                                    <!-- SVG Check Icon -->
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="lucide lucide-check h-4 w-4 text-green-500" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path d="M20 6L9 17l-5-5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </ng-template>
                            </button>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Request
                            Limit</label>
                        <input
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            readonly="" value="{{apiSettings?.requestLimit}}">
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Domains</label>
                        <div class="border rounded-md p-3 bg-gray-50 min-h-[100px]">
                            <ul class="list-disc pl-5 space-y-1">
                                @for (item of apiSettings?.domains; track $index) {
                                <li class="text-sm">{{item}}</li>
                                }
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <label
                                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Ngày
                                    tạo</label>
                                <input
                                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    readonly value="{{apiSettings?.timeCreate}}">
                            </div>
                            <div class="space-y-2">
                                <label
                                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Ngày
                                    hết hạn</label>
                                <input
                                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    readonly value="{{apiSettings?.timeExpire}}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>