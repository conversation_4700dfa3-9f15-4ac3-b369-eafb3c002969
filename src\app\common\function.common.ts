export function range(start: number, end: number): number[] {
    if (start > end) {
        return [];
    }
    return Array(end - start + 1).fill(0).map((_, idx) => start + idx);
}

export function formatDate(date: Date): string | null {
    if (!date || date === undefined) {
        return null;
    }
    var date1 = new Date(date)
    return new Intl.DateTimeFormat('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric'
    }).format(date1);
}
export function formatDateTo_ddMMyyyy(date: any): string | null {
    if (!date || date === undefined) {
        return null;
    }

    // If it's a Date object
    if (date instanceof Date) {
        return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
    }

    // If it's an object with day/month/year properties
    if (typeof date === 'object' && ('day' in date || 'month' in date || 'year' in date)) {
        return `${(date.day || 1).toString().padStart(2, '0')}/${(date.month || 1).toString().padStart(2, '0')}/${(date.year || 2000)}`;
    }

    // If it's a string, try to parse it as a date
    if (typeof date === 'string') {
        const parsedDate = new Date(date);
        if (!isNaN(parsedDate.getTime())) {
            return `${parsedDate.getDate().toString().padStart(2, '0')}/${(parsedDate.getMonth() + 1).toString().padStart(2, '0')}/${parsedDate.getFullYear()}`;
        }
    }

    return null;
}

export function formatDateTo_yyyyMMdd(date: Date): string | null {
    if (!date || date === undefined) {
        return null;
    }
    var date1 = new Date(date);
    var day = date1.getDate();
    var month = date1.getMonth() + 1;
    var year = date1.getFullYear();
    return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
}

export function removeFormatCurrency(value: string): number {
    try {
        value = value.replace(/[^\d]/g, ''); // remove non-numeric characters
    } catch (e) {
    }
    return +value || 0;
}

export function formatCurrency(value: string): string {
    try {
        value = value.replace(/[^\d]/g, ''); // remove non-numeric characters
    } catch (e) {
    }
    var number = parseInt(value || '0', 10);
    return `${number.toLocaleString('de-DE')} VNĐ`;
}

export function formatTimeForDateTimeLocal(date: Date): string {
    var date1 = new Date(date);
    var day = date1.getDate();
    var month = date1.getMonth() + 1;
    var year = date1.getFullYear();
    var hour = date1.getHours();
    var minute = date1.getMinutes();
    var second = date1.getSeconds();
    return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}T${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}:${second < 10 ? '0' + second : second}`;
}

export async function imageUrlToFile(url: string): Promise<File> {
    const response = await fetch(url);
    const blob = await response.blob();
    const file = new File([blob], 'image.jpg', { type: 'image/jpeg' });
    return file;
}

export function getDayOfWeek(date: Date): string {
    const dayOfWeek = new Date(date).getDay();
    // return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek];
    return ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'][dayOfWeek];
}

