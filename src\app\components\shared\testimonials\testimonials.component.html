<section class="py-20 bg-gradient-to-b from-white to-primary-50 relative overflow-hidden">
    <div class="container mx-auto px-4">
        <div class="text-center max-w-3xl mx-auto mb-16">
            <div
                class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 mb-4 bg-primary-100 text-primary-700 hover:bg-primary-200 border-primary-200">
                Đ<PERSON>h gi<PERSON> từ khách hàng</div>
            <h2 class="text-3xl md:text-4xl font-bold mb-4 tracking-tight">Khách hàng nói gì về chúng tôi</h2>
            <p class="text-gray-600">Tr<PERSON><PERSON> nghiệm thực tế từ những khách hàng đã sử dụng dịch vụ của chúng tôi</p>
        </div>
        <div class="relative">
            <div class="overflow-hidden">
                <div id="testimonialSlider" class="flex transition-transform duration-500 ease-in-out"
                    style="transform: translateX(0%);">
                    <div class="min-w-full grid grid-cols-1 md:grid-cols-2 gap-8 px-2 py-4">
                        <div class="border border-gray-100 bg-card text-card-foreground  shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden"
                            data-v0-t="card">
                            <div class="p-8 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-quote h-10 w-10 text-primary-100 absolute top-4 right-4">
                                    <path
                                        d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z">
                                    </path>
                                    <path
                                        d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z">
                                    </path>
                                </svg>
                                <div class="flex items-center mb-6">
                                    <div
                                        class="relative h-14 w-14 rounded-full overflow-hidden mr-4 border-2 border-primary-100">
                                        <img alt="Nguyễn Văn A" loading="lazy" decoding="async" data-nimg="fill"
                                            class="object-cover" src="\assets\img\avatar\avatar_default.png"
                                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-lg">Nguyễn Văn Minh</h4>
                                        <p class="text-sm text-gray-500">Giám đốc Marketing</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6 italic">"Là một người thường xuyên phải đi công tác, tôi
                                    rất ấn tượng với dịch vụ của phòng vé Indeed Travel. Hệ thống đặt vé trực tuyến rất
                                    tiện lợi, giá cả cạnh tranh và đội ngũ tư vấn viên rất nhiệt tình. Đặc biệt là tính
                                    năng thông báo giá vé giúp tôi tiết kiệm được nhiều chi phí."
                                </p>
                                <div class="flex"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg></div>
                            </div>
                        </div>
                        <div class="border  border-gray-100 bg-card text-card-foreground shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden"
                            data-v0-t="card">
                            <div class="p-8 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-quote h-10 w-10 text-primary-100 absolute top-4 right-4">
                                    <path
                                        d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z">
                                    </path>
                                    <path
                                        d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z">
                                    </path>
                                </svg>
                                <div class="flex items-center mb-6">
                                    <div
                                        class="relative h-14 w-14 rounded-full overflow-hidden mr-4 border-2 border-primary-100">
                                        <img alt="Trần Thị B" loading="lazy" decoding="async" data-nimg="fill"
                                            class="object-cover" src="\assets\img\avatar\avatar_default.png"
                                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-lg">Lê Thị Hồng</h4>
                                        <p class="text-sm text-gray-500">Giảng viên đại học</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6 italic">"Là giảng viên thường xuyên đi dự hội thảo, tôi rất
                                    hài lòng với dịch vụ của phòng vé Indeed Travel. Họ luôn có những ưu đãi đặc biệt cho
                                    khách hàng thân thiết và hỗ trợ rất tốt khi có thay đổi lịch trình. Giao diện
                                    website cũng rất thân thiện và dễ sử dụng."
                                </p>
                                <div class="flex"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg></div>
                            </div>
                        </div>
                    </div>
                    <div class="min-w-full grid grid-cols-1 md:grid-cols-2 gap-8 px-2 py-4">
                        <div class="border border-gray-100 bg-card text-card-foreground  shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden"
                            data-v0-t="card">
                            <div class="p-8 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-quote h-10 w-10 text-primary-100 absolute top-4 right-4">
                                    <path
                                        d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z">
                                    </path>
                                    <path
                                        d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z">
                                    </path>
                                </svg>
                                <div class="flex items-center mb-6">
                                    <div
                                        class="relative h-14 w-14 rounded-full overflow-hidden mr-4 border-2 border-primary-100">
                                        <img alt="Nguyễn Văn A" loading="lazy" decoding="async" data-nimg="fill"
                                            class="object-cover" src="\assets\img\avatar\avatar_default.png"
                                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-lg">Trần Văn Phúc</h4>
                                        <p class="text-sm text-gray-500">Chủ doanh nghiệp</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6 italic">"Với tư cách là chủ doanh nghiệp, tôi đánh giá cao
                                    dịch vụ của phòng vé Indeed Travel. Họ không chỉ cung cấp giá vé tốt mà còn có chính
                                    sách ưu đãi riêng cho doanh nghiệp. Đội ngũ tư vấn viên chuyên nghiệp, am hiểu và
                                    luôn sẵn sàng hỗ trợ 24/24."
                                </p>
                                <div class="flex"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg></div>
                            </div>
                        </div>
                        <div class="border  border-gray-100 bg-card text-card-foreground shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden"
                            data-v0-t="card">
                            <div class="p-8 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-quote h-10 w-10 text-primary-100 absolute top-4 right-4">
                                    <path
                                        d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z">
                                    </path>
                                    <path
                                        d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z">
                                    </path>
                                </svg>
                                <div class="flex items-center mb-6">
                                    <div
                                        class="relative h-14 w-14 rounded-full overflow-hidden mr-4 border-2 border-primary-100">
                                        <img alt="Trần Thị B" loading="lazy" decoding="async" data-nimg="fill"
                                            class="object-cover" src="\assets\img\avatar\avatar_default.png"
                                            style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-lg">Phạm Thị Lan</h4>
                                        <p class="text-sm text-gray-500">Nhân viên kinh doanh</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6 italic">"Là nhân viên kinh doanh thường xuyên đi công tác,
                                    tôi rất tin tưởng phòng vé Indeed Travel. Họ luôn có những chương trình khuyến mãi hấp
                                    dẫn và dịch vụ chăm sóc khách hàng rất tốt. Đặc biệt là tính năng đặt vé nhanh giúp
                                    tôi tiết kiệm được nhiều thời gian."
                                </p>
                                <div class="flex"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="#FFB800" class="h-5 w-5">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex justify-center mt-10 space-x-4">
                <button (click)="slideTestimonials('prev')" [disabled]="isFirstSlide"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:text-accent-foreground rounded-full h-12 w-12 border-gray-200 hover:bg-orange-50 hover:border-orange-200">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-chevron-left h-5 w-5">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
                <button (click)="slideTestimonials('next')" [disabled]="isLastSlide"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border bg-background hover:text-accent-foreground rounded-full h-12 w-12 border-gray-200 hover:bg-orange-50 hover:border-orange-200">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-chevron-right h-5 w-5">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                    <span class="sr-only">Next</span>
                </button>
            </div>
        </div>
    </div>
    <div class="absolute -top-16 -left-16 w-64 h-64 bg-primary-100 rounded-full opacity-30 blur-3xl"></div>
    <div class="absolute max-md:hidden -bottom-32 -right-32 w-96 h-96 bg-blue-50 rounded-full opacity-40 blur-3xl">
    </div>
</section>