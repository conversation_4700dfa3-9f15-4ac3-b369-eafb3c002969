// viewer.component.ts
import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-editor-viewer',
  imports: [],
  templateUrl: './editor-viewer.component.html',
  styleUrl: './editor-viewer.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class EditorViewerComponent {
  @Input() content: string = '';

  safeContent: SafeHtml = '';

  constructor(private sanitizer: DomSanitizer) { }

  getSafeHtml(html: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }
}
