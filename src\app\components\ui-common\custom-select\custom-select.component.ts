import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, ElementRef, ViewChild, HostListener, OnInit } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

interface SelectOption {
  value: string;
  label: string;
  svg?: string;
}

@Component({
  selector: 'app-custom-select',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div #selectContainer class="custom-select" [class.open]="isOpen">
      <div class="selected-option" >
      <span class="option-icon" (click)="onSelectedDefault($event)">
        <span [innerHTML]="sanitizeSVG(placeholder)" style="pointer-events: none"></span>
      </span>
        <span class="arrow-icon" (click)="toggleDropdown($event)">
          <svg class="dropdown-arrow" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M6 9l6 6 6-6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </span>
      </div>
      <div class="options-container" *ngIf="isOpen">
        <div class="option" *ngFor="let option of options" 
             (click)="selectOption(option, $event)"
             [class.selected]="option.value === selectedOption?.value">
          <span *ngIf="option.svg" [innerHTML]="sanitizeSVG(option.svg)" class="option-icon"></span>
          <span class="option-label">{{ option.label }}</span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .custom-select {
      position: relative;
      width: fit-content;
      user-select: none;
      height: 100%;
    }
    .selected-option {
      display: flex;
      height: 100%;
      align-items: center;
      border: 1px solid #ccc;
      border-radius: 4px;
      cursor: pointer;
      background: white;
    }
    .selected-option .option-icon {
      @apply px-2;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      
      margin-right: 0px;
    }
    .selected-option .arrow-icon {
      margin-left: 0px;
    }
    .option-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    .option-icon:hover {
      background: #f5f5f5;
      border-radius: 4px;
    }
    .option-label {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .arrow-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      margin-left: 8px;
    }
    .dropdown-arrow {
      transition: transform 0.2s ease;
    }
    .open .dropdown-arrow {
      transform: rotate(180deg);
    }
    .options-container {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      width: fit-content;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      margin-top: 4px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .option {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      cursor: pointer;
    }
    .option:hover {
      background: #f5f5f5;
    }
    .option.selected {
      background: #e3f2fd;
    }
    .option-icon svg,
    .option-icon path {
      pointer-events: none;
    }

  `]
})
export class CustomSelectComponent implements OnInit {
  @Input() options: SelectOption[] = [];
  @Input() placeholder: string = 'Select...';
  @Input() value: string = '';
  @Output() valueChange = new EventEmitter<string>();

  @ViewChild('selectContainer') selectContainer!: ElementRef;

  isOpen = false;
  selectedOption: SelectOption | null = null;

  constructor(private sanitizer: DomSanitizer) { }

  ngOnInit() {
    if (this.value) {
      this.selectedOption = this.options.find(opt => opt.value === this.value) || null;
    }
  }

  // onSelectedDefault(event: MouseEvent) {
  //   event.stopPropagation();
  //   if (this.selectedOption) {
  //     this.selectedOption = null;
  //     this.valueChange.emit('');
  //   } else if (this.options.length > 0) {
  //     this.selectedOption = this.options[0];
  //     this.valueChange.emit(this.options[0].value);
  //   }
  // }
  onSelectedDefault(event: MouseEvent) {
    // Check if the target is an SVG element
    const target = event.target as HTMLElement;
    if (target.tagName === 'svg' || target.closest('svg')) {
      event.stopPropagation();
    }

    // Rest of your existing code
    if (this.selectedOption) {
      this.selectedOption = null;
      this.valueChange.emit('');
    } else if (this.options.length > 0) {
      this.selectedOption = this.options[0];
      this.valueChange.emit(this.options[0].value);
    }
  }

  toggleDropdown(event: MouseEvent) {
    if ((event.target as HTMLElement).closest('.arrow-icon')) {
      this.isOpen = !this.isOpen;
    }
  }

  sanitizeSVG(svg: string): SafeHtml {
    if (!svg || !svg.includes('<svg')) {
      return '';
    }
    svg = svg.replace(/fill="#000000"/g, 'fill="currentColor"');
    svg = svg.replace('<svg', '<svg style="pointer-events: none"');
    return this.sanitizer.bypassSecurityTrustHtml(svg);
  }

  selectOption(option: SelectOption, event: MouseEvent) {
    event.stopPropagation();
    this.selectedOption = option;
    this.valueChange.emit(option.value);
    this.isOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent) {
    if (!this.selectContainer.nativeElement.contains(event.target)) {
      this.isOpen = false;
    }
  }
} 