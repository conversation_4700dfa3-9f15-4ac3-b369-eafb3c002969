import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';


const apiUrl = environment.apiUrl;
@Injectable({
  providedIn: 'root'
})
export class CategoryNewsService {

  constructor(
    private http: HttpClient
  ) {

  }

  getItems(): Observable<any> {
    return this.http.get<any>(apiUrl + '/api/categorynews/get-items');
  }
  getPartnerCateforyNewsPaging(request: any): Observable<any> {
    return this.http.post<any>(`${apiUrl}/api/Partner/CategoryNews/padding`, request);
  }
  savePartner(request: any): Observable<any> {
    return this.http.post<any>(`${apiUrl}/api/Partner/CategoryNews`, request);
  }


  getPartnerItems(): Observable<any> {
    return this.http.get<any>(apiUrl + '/api/Partner/CategoryNews/items');
  }

  getPartnerClientItems(): Observable<any> {
    return this.http.get<any>(apiUrl + '/api/Partner/CategoryNews/client/items');
  }

  getItemSelect(): Observable<any> {
    return this.http.get<any>(apiUrl + '/api/categorynews/get-item-select');
  }

  getNewsPaging(request: any): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/CategoryNews/paging?TypeScreen=${request.TypeScreen}&Keyword=${request.Keyword}&PageIndex=${request.PageIndex}&PageSize=${request.PageSize}`);
  }

  getByID(Id: string): Observable<any> {
    return this.http.get<any>(apiUrl + `/api/categorynews/${Id}`);
  }

  hide(listId: string[], show: boolean): Observable<any> {
    const queryString = listId.map(id => `listId=${id}`).join('&');
    return this.http.put<any>(apiUrl + `/api/CategoryNews?show=${show}&${queryString}`, null);
  }

  save(request: any): Observable<any> {
    const formContent = new FormData();
    if (request.Id != null) {
      formContent.append("Id", request.Id.toString());
    }

    formContent.append("Name", request.Name);
    formContent.append("Description", request.Description);
    formContent.append("SortOrder", request.SortOrder.toString());
    formContent.append("Status", request.Status.toString());
    return this.http.post<any>(apiUrl + `/api/CategoryNews`, formContent)
  }
}
