<div class="container relative h-full flex flex-row max-md:flex-col gap-2 min-w-full">
    <div class="container  relative shadow-lg bg-white dark:bg-gray-800 rounded-lg h-full overflow-y-scroll min-w-full">
        <main class="flex-1 p-6 md:p-8">
            <div class="w-full">
                <div class="flex flex-col gap-6">
                    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                        <h1 class="text-3xl font-bold tracking-tight">Thông tin cá nhân</h1>
                        <div class="flex gap-2">
                            <button (click)="openLg(content)"
                                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
                                type="button">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-key mr-2 h-4 w-4">
                                    <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"></path>
                                    <path d="m21 2-9.6 9.6"></path>
                                    <circle cx="7.5" cy="15.5" r="5.5"></circle>
                                </svg>Đổi mật khẩu
                            </button>
                        </div>
                    </div>
                    <div *ngIf="loading" class="flex flex-col gap-6">
                        <!-- Profile Card Skeleton -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="p-6 pt-6">
                                <div class="flex flex-col md:flex-row gap-6">
                                    <div class="flex flex-col items-center md:items-start">
                                        <div class="h-32 w-32 rounded-full bg-gray-200 animate-pulse"></div>
                                        <div class="mt-4 text-center md:text-left space-y-2">
                                            <div class="h-6 w-48 bg-gray-200 rounded animate-pulse"></div>
                                            <div class="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                                            <div class="flex gap-2 mt-2">
                                                <div class="h-5 w-20 bg-gray-200 rounded-full animate-pulse"></div>
                                                <div class="h-5 w-20 bg-gray-200 rounded-full animate-pulse"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div class="space-y-4">
                                                <div class="flex items-center gap-2">
                                                    <div class="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                                                    <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <div class="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                                                    <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <div class="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                                                    <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Details Card Skeleton -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="flex flex-col space-y-1.5 p-6">
                                <div class="h-7 w-48 bg-gray-200 rounded animate-pulse"></div>
                                <div class="h-4 w-64 bg-gray-200 rounded animate-pulse"></div>
                            </div>
                            <div class="p-6 pt-0 space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="space-y-4">
                                        <div>
                                            <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                            <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                        </div>
                                        <div>
                                            <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                            <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                        </div>
                                        <div>
                                            <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                            <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                        </div>
                                        <div>
                                            <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                            <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                        </div>
                                    </div>
                                    <div class="space-y-4">
                                        <div>
                                            <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                            <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                        </div>
                                        <div>
                                            <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                            <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                        </div>
                                        <div>
                                            <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                            <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                        </div>
                                        <div>
                                            <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                            <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Card Skeleton -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="flex flex-col space-y-1.5 p-6">
                                <div class="h-7 w-48 bg-gray-200 rounded animate-pulse"></div>
                                <div class="h-4 w-64 bg-gray-200 rounded animate-pulse"></div>
                            </div>
                            <div class="p-6 pt-0 space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                        <div class="h-4 w-64 bg-gray-200 rounded animate-pulse"></div>
                                    </div>
                                    <div class="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
                                </div>
                                <div class="shrink-0 bg-border h-[1px] w-full"></div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                                        <div class="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                                    </div>
                                    <div class="h-9 w-40 bg-gray-200 rounded animate-pulse"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="!loading && profileData"
                        class="rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
                        <div class="p-6 pt-6">
                            <div class="flex flex-col md:flex-row gap-6">
                                <div class="flex flex-col items-center md:items-start">
                                    <img [src]="profileData.avatar || '/assets/img/avatar/avatar_default.png'"
                                        [alt]="profileData.fullName"
                                        class="h-32 w-32 rounded-full object-cover border-4 border-gray-200">
                                    <div class="mt-4 text-center md:text-left">
                                        <h2 class="text-2xl font-bold">{{profileData.fullName}}</h2>
                                        <p class="text-muted-foreground">&#64;{{profileData.userName}}</p>
                                        <div class="flex gap-2 mt-2 justify-center md:justify-start flex-wrap">
                                            <span *ngFor="let role of profileData.roles"
                                                class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-purple-100 text-purple-800">{{role}}</span>
                                            <span *ngIf="profileData.isOnline"
                                                class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800">Hoạt
                                                động</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="space-y-4">
                                            <div class="flex items-center gap-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-mail h-4 w-4 text-muted-foreground">
                                                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                                </svg>
                                                <span class="text-sm">{{profileData.email}}</span>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-phone h-4 w-4 text-muted-foreground">
                                                    <path
                                                        d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                                    </path>
                                                </svg>
                                                <span class="text-sm">{{profileData.phone}}</span>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-shield h-4 w-4 text-muted-foreground">
                                                    <path
                                                        d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z">
                                                    </path>
                                                </svg>
                                                <span class="text-sm">{{profileData.group}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="!loading && profileData"
                        class="rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Thông tin chi tiết</h3>
                            <p class="text-sm text-muted-foreground">Thông tin cá nhân và liên hệ</p>
                        </div>
                        <div class="p-6 pt-0 space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-4">
                                    <div>
                                        <label
                                            class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Họ
                                            và tên</label>
                                        <p class="text-sm mt-1">{{profileData.fullName}}</p>
                                    </div>
                                    <div>
                                        <label
                                            class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Tên
                                            đăng nhập</label>
                                        <p class="text-sm mt-1">{{profileData.userName}}</p>
                                    </div>
                                    <div>
                                        <label
                                            class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Email</label>
                                        <p class="text-sm mt-1">{{profileData.email}}</p>
                                    </div>
                                    <div>
                                        <label
                                            class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Số
                                            điện thoại</label>
                                        <p class="text-sm mt-1">{{profileData.phone}}</p>
                                    </div>
                                </div>
                                <div class="space-y-4">
                                    <div>
                                        <label
                                            class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Vai
                                            trò</label>
                                        <p class="text-sm mt-1">{{profileData.roles.join(', ')}}</p>
                                    </div>
                                    <div>
                                        <label
                                            class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Đại
                                            lý</label>
                                        <p class="text-sm mt-1">{{profileData.group}}</p>
                                    </div>
                                    <div>
                                        <label
                                            class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Ngày
                                            tham gia</label>
                                        <p class="text-sm mt-1">{{profileData.timeCreate}}</p>
                                    </div>
                                    <div>
                                        <label
                                            class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Hoạt
                                            động gần nhất</label>
                                        <p class="text-sm mt-1">{{profileData.lastActivity}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Bảo mật tài khoản</h3>
                            <p class="text-sm text-muted-foreground">Quản lý mật khẩu và bảo mật tài khoản</p>
                        </div>
                        <div class="p-6 pt-0 space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label
                                        class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Mật
                                        khẩu</label>
                                    <p class="text-sm text-muted-foreground">Đổi mật khẩu để bảo vệ tài khoản của bạn
                                    </p>
                                </div>
                                <button (click)="openLg(content)"
                                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
                                    type="button">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-key mr-2 h-4 w-4">
                                        <path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"></path>
                                        <path d="m21 2-9.6 9.6"></path>
                                        <circle cx="7.5" cy="15.5" r="5.5"></circle>
                                    </svg>
                                    Đổi mật khẩu
                                </button>
                            </div>
                            <div data-orientation="horizontal" role="none" class="shrink-0 bg-border h-[1px] w-full">
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <label
                                        class="peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-sm font-medium">Đăng
                                        nhập gần nhất</label>
                                    <p class="text-sm text-muted-foreground">{{profileData?.loginLatest}}</p>
                                </div>
                                <button routerLink="login-history"
                                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3">Xem
                                    lịch sử đăng nhập</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>


<!-- modal change password -->
<ng-template #content let-modal>
    <div class="fixed bg-white left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg sm:max-w-[450px]"
        style="pointer-events: auto;">
        <form [formGroup]="passwordForm" (ngSubmit)="onSubmit()">
            <div class="flex flex-col space-y-1.5 text-center sm:text-left">
                <h2 class="text-lg font-semibold leading-none tracking-tight">Đổi mật khẩu</h2>
                <p class="text-sm text-muted-foreground">Nhập mật khẩu hiện tại và mật khẩu mới để thay đổi
                    mật khẩu của bạn</p>
            </div>
            <div class="grid gap-4 py-4">
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="currentPassword">Mật khẩu hiện tại *</label>
                    <div class="relative">
                        <input
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-10"
                            id="currentPassword" placeholder="Nhập mật khẩu hiện tại"
                            [type]="showCurrentPassword ? 'text' : 'password'" formControlName="currentPassword">
                        <button type="button" (click)="togglePasswordVisibility('currentPassword')"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground w-10 absolute right-0 top-0 h-full px-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-eye h-4 w-4">
                                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </button>
                    </div>
                    <div *ngIf="passwordForm.get('currentPassword')?.touched && passwordForm.get('currentPassword')?.invalid"
                        class="text-sm text-red-500 mt-1">
                        {{getErrorMessage('currentPassword')}}
                    </div>
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="newPassword">Mật khẩu mới *</label>
                    <div class="relative">
                        <input
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-10"
                            id="newPassword" placeholder="Nhập mật khẩu mới (tối thiểu 6 ký tự)"
                            [type]="showNewPassword ? 'text' : 'password'" formControlName="newPassword">
                        <button type="button" (click)="togglePasswordVisibility('newPassword')"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground w-10 absolute right-0 top-0 h-full px-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-eye h-4 w-4">
                                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </button>
                    </div>
                    <div *ngIf="passwordForm.get('newPassword')?.touched && passwordForm.get('newPassword')?.invalid"
                        class="text-sm text-red-500 mt-1">
                        {{getErrorMessage('newPassword')}}
                    </div>
                </div>
                <div class="space-y-2">
                    <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="confirmPassword">Xác nhận mật khẩu mới *</label>
                    <div class="relative">
                        <input
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-10"
                            id="confirmPassword" placeholder="Nhập lại mật khẩu mới"
                            [type]="showConfirmPassword ? 'text' : 'password'" formControlName="confirmPassword">
                        <button type="button" (click)="togglePasswordVisibility('confirmPassword')"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground w-10 absolute right-0 top-0 h-full px-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-eye h-4 w-4">
                                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </button>
                    </div>
                    <div *ngIf="passwordForm.get('confirmPassword')?.touched && passwordForm.get('confirmPassword')?.invalid"
                        class="text-sm text-red-500 mt-1">
                        {{getErrorMessage('confirmPassword')}}
                    </div>
                </div>
                <div class="bg-blue-50 p-3 rounded-lg">
                    <h4 class="text-sm font-medium mb-2">Yêu cầu mật khẩu:</h4>
                    <ul class="text-xs text-muted-foreground space-y-1">
                        <li>• Tối thiểu 6 ký tự</li>
                        <li>• Khác với mật khẩu hiện tại</li>
                        <li>• Nên sử dụng kết hợp chữ, số và ký tự đặc biệt</li>
                    </ul>
                </div>
            </div>
            <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
                <button type="button" (click)="modal.dismiss('Cross click')"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                    Hủy
                </button>
                <button type="submit" [disabled]="submitting"
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 bg-primary-400 text-white">
                    <span *ngIf="submitting" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                    <span *ngIf="!submitting">Đổi mật khẩu</span>
                </button>
            </div>
        </form>
        <button type="button" (click)="modal.dismiss('Cross click')"
            class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-x h-4 w-4">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg>
            <span class="sr-only">Close</span>
        </button>
    </div>
</ng-template>