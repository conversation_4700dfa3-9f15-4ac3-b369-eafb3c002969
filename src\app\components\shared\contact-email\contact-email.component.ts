import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ContactService } from '../../../services/contact/contact.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-contact-email',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './contact-email.component.html',
  styleUrl: './contact-email.component.css'
})
export class ContactEmailComponent {
  private readonly contactService = inject(ContactService);
  private readonly fb = inject(FormBuilder);
  private readonly toastService = inject(ToastrService);

  emailForm: FormGroup;
  isSubmitting: boolean = false;

  constructor() {
    this.emailForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  async subscribeNewsletter(): Promise<void> {
    if (this.emailForm.invalid) {
      this.toastService.error('<PERSON><PERSON> lòng nhập email hợp lệ', 'Lỗi');
      return;
    }

    try {
      this.isSubmitting = true;
      const response = await this.contactService.sentContact({
        email: this.emailForm.get('email')?.value
      }).toPromise();

      if (response?.isSuccessed) {
        this.toastService.success('Đăng ký nhận tin thành công', 'Thành công');
        this.emailForm.reset();
      } else {
        this.toastService.error('Đăng ký nhận tin thất bại', 'Lỗi');
      }
    } catch (error) {
      this.toastService.error('Có lỗi xảy ra khi đăng ký', 'Lỗi');
    } finally {
      this.isSubmitting = false;
    }
  }

  get emailError(): string {
    const control = this.emailForm.get('email');
    if (control?.touched || control?.dirty) {
      if (control?.hasError('required')) {
        return 'Vui lòng nhập email';
      }
      if (control?.hasError('email')) {
        return 'Email không hợp lệ';
      }
    }
    return '';
  }
}
