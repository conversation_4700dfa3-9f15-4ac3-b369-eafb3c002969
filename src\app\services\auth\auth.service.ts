import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, tap } from 'rxjs';
import { Token } from '../../enum/token';
import { CookieService } from 'ngx-cookie-service';

const apiUrl = environment.apiUrl;

@Injectable({
  providedIn: 'root'
})

export class AuthService {
  constructor(
    private http: HttpClient,
    private cookieService: CookieService
  ) { }

  login(formData: FormData): Observable<any> {
    return this.http.post<any>(apiUrl + '/api/Users/<USER>', formData, { withCredentials: true });
  }

  // login(formData: FormData): Observable<any> {
  //   // Kiểm tra nếu là thiết bị di động
  //   const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  //   return this.http.post<any>(`${apiUrl}/api/Users/<USER>
  //     withCredentials: true
  //   }).pipe(
  //     tap(response => {
  //       if (response && response.isSuccessed) {
  //         // Nếu là thiết bị di động hoặc response chứa is_mobile = true, lưu token vào localStorage
  //         if (isMobile || response.resultObj?.is_mobile) {
  //           localStorage.setItem(Token.ACCESS_TOKEN, response.resultObj.access_token);
  //           localStorage.setItem(Token.REFRESH_TOKEN, response.resultObj.refresh_token);
  //           localStorage.setItem(Token.CONNECTION_ID, response.resultObj.connection_id);
  //         }
  //       }
  //     })
  //   );
  // }

  // Phương thức lấy token từ cả cookie và localStorage
  getToken(): string {
    // Thử lấy từ cookie trước
    const cookieToken = this.cookieService.get(Token.ACCESS_TOKEN);
    if (cookieToken) return cookieToken;

    // Nếu không có trong cookie, lấy từ localStorage
    return localStorage.getItem(Token.ACCESS_TOKEN) || '';
  }

  // Cập nhật interceptor để sử dụng token từ cả hai nguồn
  getAuthorizationHeader(): string {
    const token = this.getToken();
    return token ? `Bearer ${token}` : '';
  }

  register(formData: FormData): Observable<any> {
    return this.http.post<any>(`${apiUrl}/api/Users`, formData);
  }

  getProfile(): Observable<any> {
    return this.http.get<any>(`${apiUrl}/api/Users/<USER>
  }
  getProfileAdvance(): Observable<any> {
    return this.http.get<any>(`${apiUrl}/api/Users/<USER>/profile`);
  }
  // getToken(): string | null {
  //   var cookie = this.getCookie(Token.ACCESS_TOKEN);
  //   if (cookie) {
  //     return cookie;
  //   }
  //   return null;
  // }
  changePassword(formData: FormData): Observable<any> {
    return this.http.post<any>(`${apiUrl}/api/Users/<USER>
  }
  saveToken(name: string, value: string): void {
    localStorage.setItem(name, value);
  }

  // refreshToken(): Observable<any> {
  //   // Make a request to refresh the token (assuming a POST request)
  //   return this.http.post(`${apiUrl}/api/Users/<USER>
  // }
  refreshToken(): Observable<any> {
    // Kiểm tra nếu là thiết bị di động
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    let headers = new HttpHeaders();

    // Nếu là thiết bị di động, thêm refresh token vào header
    if (isMobile) {
      const refreshToken = localStorage.getItem(Token.REFRESH_TOKEN);
      const conectionID = localStorage.getItem(Token.CONNECTION_ID);
      if (refreshToken && conectionID) {
        headers = headers.set('X-Refresh-Token', refreshToken);
        headers = headers.set('X-Connection-Id', conectionID);
      }
    }

    // Gọi API refresh token với header phù hợp
    return this.http.post(`${apiUrl}/api/Users/<USER>
      withCredentials: true,
      headers: headers
    });
  }

  getRefreshToken(): string {
    return localStorage.getItem(Token.REFRESH_TOKEN) || '';
  }

  logout(): Observable<any> {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    let headers = new HttpHeaders();

    // Nếu là thiết bị di động, thêm refresh token vào header
    if (isMobile) {
      const refreshToken = localStorage.getItem(Token.REFRESH_TOKEN);
      const conectionID = localStorage.getItem(Token.CONNECTION_ID);
      if (refreshToken && conectionID) {
        headers = headers.set('X-Refresh-Token', refreshToken);
        headers = headers.set('X-Connection-Id', conectionID);
      }
    }
    return this.http.post(`${apiUrl}/api/Users/<USER>
      withCredentials: true,
      headers: headers
    });
  }
  //get cookie
  getCookie(name: string): string {
    return this.cookieService.get(name) || '';
  }

  //save cookie
  saveCookie(name: string, value: string): void {
    this.cookieService.set(name, value);
  }

  //delete cookie
  deleteCookie(name: string): void {
    this.cookieService.delete(name);
  }

  //delete all cookie
  deleteAllCookie(): void {
    this.cookieService.deleteAll();
  }

  //save token when login
  saveTokenWhenLogin(tokenModel: any): void {
    this.saveCookie(Token.ACCESS_TOKEN, tokenModel.access_token);
    this.saveCookie(Token.REFRESH_TOKEN, tokenModel.refresh_token);
    this.saveCookie(Token.CONNECTION_ID, tokenModel.connection_id);
  }

  createUser(data: any): Observable<any> {
    return this.http.post<any>(`${apiUrl}/api/Users/<USER>
  }

  lockUser(data: any): Observable<any> {
    return this.http.put<any>(`${apiUrl}/api/Users/<USER>
  }
  getListLock(id: any): Observable<any> {
    return this.http.get<any>(`${apiUrl}/api/Users/<USER>/${id}`);
  }
}
