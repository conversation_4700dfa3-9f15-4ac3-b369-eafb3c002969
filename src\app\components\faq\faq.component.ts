import { Component } from '@angular/core';

@Component({
  selector: 'app-faq',
  imports: [],
  templateUrl: './faq.component.html',
  styleUrl: './faq.component.css'
})
export class FaqComponent {
  expandedFaq: number | null = null;

  toggleFaq(faqNumber: number): void {
    if (this.expandedFaq === faqNumber) {
      this.expandedFaq = null;
    } else {
      this.expandedFaq = faqNumber;
    }
  }
}
