<div class="relative h-full flex flex-row max-md:flex-col gap-2 min-w-full">
    <div class=" shadow-lg bg-white dark:bg-gray-800 rounded-lg h-full w-full overflow-auto">
        <div class="flex-1 space-y-4 p-8 pt-6 bg-white rounded-lg">
            <div class="flex items-center justify-between space-y-2">
                <h2 class="text-3xl font-bold tracking-tight">Tổng quan</h2>
            </div>
            <div dir="ltr" data-orientation="horizontal" class="space-y-4">
                <div role="tablist" aria-orientation="horizontal"
                    class="inline-flex h-10 items-center justify-start rounded-md bg-muted  text-muted-foreground border-b w-full"
                    tabindex="0" data-orientation="horizontal" style="outline: none;">
                    <button routerLink="/manager/dashboard" routerLinkActive="bg-[#fb6340] text-white"
                        [routerLinkActiveOptions]="{exact: true}" type="button" role="tab" aria-selected="true"
                        class="inline-flex h-full items-center justify-center whitespace-nowrap rounded-t-lg px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm">
                        Đơn hàng</button>
                    <button routerLink="/manager/dashboard/api" routerLinkActive="bg-[#fb6340] text-white"
                        [routerLinkActiveOptions]="{exact: true}" type="button"
                        class="inline-flex h-full items-center justify-center whitespace-nowrap rounded-t-lg px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm">
                        API</button>
                </div>
                <router-outlet></router-outlet>

            </div>
        </div>
    </div>
</div>