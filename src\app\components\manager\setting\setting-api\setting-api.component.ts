import { Component } from '@angular/core';
import { XApiKeyService } from '../../../../services/XApiKey/xapi-key.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-setting-api',
  imports: [
    CommonModule
  ],
  templateUrl: './setting-api.component.html',
  styleUrl: './setting-api.component.css'
})
export class SettingApiComponent {
  apiSettings: any;
  constructor(
    private apiKeyService: XApiKeyService,
  ) { }

  ngOnInit(): void {
    this.loadApiKeySetting();
  }
  get apiKeyHide() {
    return this.apiSettings?.apiKey ? this.apiSettings.apiKey.replace(/.(?=.{4})/g, '*') : '';
  }

  loadApiKeySetting() {
    var result = this.apiKeyService.GetApiKeySetting().toPromise();
    result.then((response: any) => {
      this.apiSettings = response.resultObj;
    }).catch((error: any) => {
      console.error('Error loading API key settings:', error);
    });
  }
  isCopied = false;
  copyToClipboard(text: string) {
    navigator.clipboard.writeText(text).then(() => {
      this.isCopied = true;
      setTimeout(() => this.isCopied = false, 1500);
    }).catch((error) => {
      console.error('Error copying text to clipboard:', error);
    });
  }
  onToggleApiStatus(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    const newStatus = checkbox.checked;

    // Hiển thị hộp thoại xác nhận
    const confirmToggle = confirm(
      `Bạn có chắc chắn muốn ${newStatus ? 'kích hoạt' : 'ngừng hoạt động'} API không?`
    );

    if (confirmToggle) {
      // Cập nhật trạng thái API
      this.apiSettings.isActive = newStatus;
      this.apiKeyService.toggleApikey(this.apiSettings?.isActive).subscribe((res: any) => {
        if (res.isSuccessed) {
          alert('Cập nhật trạng thái thành công!');
        } else {
          alert('Có lỗi xảy ra khi cập nhật trạng thái!');
          // Hoàn tác trạng thái nếu cập nhật thất bại
          this.apiSettings.isActive = !newStatus;
        }
      });
    } else {
      // Hoàn tác trạng thái nếu người dùng hủy
      checkbox.checked = !newStatus;
    }
  }
}
